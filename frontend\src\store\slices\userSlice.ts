/**
 * 用户状态管理
 * 
 * 管理用户相关的状态
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { userAPI } from '@/services/api/user'
import { User, UserUpdateRequest } from '@/types/user'

interface UserState {
  users: User[]
  currentUser: User | null
  loading: boolean
  error: string | null
  total: number
}

const initialState: UserState = {
  users: [],
  currentUser: null,
  loading: false,
  error: null,
  total: 0,
}

// 异步actions
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (params: { skip?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUsers(params)
      return response
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || '获取用户列表失败')
    }
  }
)

export const fetchUser = createAsyncThunk(
  'user/fetchUser',
  async (userId: number, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUser(userId)
      return response
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || '获取用户信息失败')
    }
  }
)

export const updateUser = createAsyncThunk(
  'user/updateUser',
  async ({ userId, userData }: { userId: number; userData: UserUpdateRequest }, { rejectWithValue }) => {
    try {
      const response = await userAPI.updateUser(userId, userData)
      return response
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || '更新用户信息失败')
    }
  }
)

export const deleteUser = createAsyncThunk(
  'user/deleteUser',
  async (userId: number, { rejectWithValue }) => {
    try {
      await userAPI.deleteUser(userId)
      return userId
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || '删除用户失败')
    }
  }
)

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCurrentUser: (state, action) => {
      state.currentUser = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取用户列表
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false
        state.users = action.payload
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // 获取单个用户
      .addCase(fetchUser.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.loading = false
        state.currentUser = action.payload
      })
      .addCase(fetchUser.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // 更新用户
      .addCase(updateUser.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.loading = false
        const index = state.users.findIndex(user => user.id === action.payload.id)
        if (index !== -1) {
          state.users[index] = action.payload
        }
        if (state.currentUser?.id === action.payload.id) {
          state.currentUser = action.payload
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // 删除用户
      .addCase(deleteUser.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.loading = false
        state.users = state.users.filter(user => user.id !== action.payload)
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const { clearError, setCurrentUser } = userSlice.actions
export default userSlice.reducer
