@echo off
echo Starting Database Services Only
echo =================================

echo Stopping any existing services...
docker-compose down

echo Starting PostgreSQL and Redis...
docker run -d --name bi_postgres -p 5432:5432 -e POSTGRES_DB=bi_platform -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=password postgres:14

docker run -d --name bi_redis -p 6379:6379 redis:7-alpine

echo Waiting for databases to start...
timeout /t 10 /nobreak >nul

echo Database services started!
echo PostgreSQL: localhost:5432 (user: postgres, password: password, db: bi_platform)
echo Redis: localhost:6379
echo.
echo You can now start the backend and frontend manually.
pause
