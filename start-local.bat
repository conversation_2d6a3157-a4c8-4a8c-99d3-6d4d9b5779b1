@echo off
echo Starting Local BI Platform (No Docker)
echo ========================================

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.8+
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found. Please install Node.js 16+
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo Step 1: Setting up backend...
cd backend

REM 创建虚拟环境
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

REM 激活虚拟环境
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM 配置pip镜像源
echo Configuring pip mirror...
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

REM 安装依赖
echo Installing Python dependencies...
pip install -r requirements-sqlite.txt

REM 创建环境配置
if not exist ".env.sqlite" (
    echo Environment config already exists
) else (
    echo Using SQLite configuration
)

echo.
echo Step 2: Starting backend server...
start "Backend Server" cmd /k "cd /d %CD% && venv\Scripts\activate.bat && python app\main_sqlite.py"

cd ..

echo.
echo Step 3: Setting up frontend...
cd frontend

REM 配置npm镜像源
echo Configuring npm mirror...
npm config set registry https://registry.npmmirror.com

REM 安装依赖
if not exist "node_modules" (
    echo Installing Node.js dependencies...
    npm install
) else (
    echo Dependencies already installed
)

echo.
echo Step 4: Starting frontend server...
start "Frontend Server" cmd /k "cd /d %CD% && npm run dev"

cd ..

echo.
echo ========================================
echo Local BI Platform is starting!
echo.
echo Services will be available at:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:8000
echo - API Docs: http://localhost:8000/api/v1/docs
echo.
echo Default login:
echo - Email: <EMAIL>
echo - Password: admin123
echo.
echo Database: SQLite (bi_platform.db)
echo No Docker required!
echo ========================================
echo.
pause
