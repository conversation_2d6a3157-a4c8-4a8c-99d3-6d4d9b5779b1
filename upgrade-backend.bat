@echo off
echo Upgrading to FastAPI Backend
echo ==============================

echo Step 1: Creating virtual environment...
cd backend
if not exist "venv" (
    python -m venv venv
)

echo Step 2: Activating virtual environment...
call venv\Scripts\activate.bat

echo Step 3: Installing FastAPI and dependencies...
pip install fastapi uvicorn sqlalchemy python-dotenv pydantic

echo Step 4: Creating environment config...
if not exist ".env" (
    copy ".env.sqlite" ".env" 2>nul
)

echo Step 5: Starting FastAPI server...
echo.
echo FastAPI server will start at: http://localhost:8000
echo API documentation will be at: http://localhost:8000/api/v1/docs
echo.
echo Press Ctrl+C to stop the server
echo.

python app\main_sqlite.py

pause
