"""
数据源管理API端点

提供数据源管理相关的API接口。
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.db.base import get_db
from app.db.models.user import User
from app.db.models.data_source import DataSourceType, DataSourceStatus
from app.schemas.data_source import (
    DataSourceCreate,
    DataSourceUpdate,
    DataSourceResponse,
    DataSourceTestRequest,
    DataSourceTestResponse,
    DataSourceList,
    DataSourceQuery,
    DataSourceQueryResponse
)
from app.services.data_source_service import DataSourceService

router = APIRouter()


@router.post("/", response_model=DataSourceResponse)
async def create_data_source(
    data_source_data: DataSourceCreate,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    创建数据源。
    
    Args:
        data_source_data: 数据源创建数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        DataSourceResponse: 创建的数据源信息
    """
    # 检查权限：只有分析师及以上级别可以创建数据源
    if not deps.is_analyst_or_above(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要分析师及以上权限"
        )
    
    service = DataSourceService(db)
    
    try:
        data_source = service.create_data_source(data_source_data, current_user.id)
        return data_source
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=DataSourceList)
async def get_data_sources(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: str = Query(None, description="搜索关键词"),
    type_filter: DataSourceType = Query(None, description="类型过滤"),
    status_filter: DataSourceStatus = Query(None, description="状态过滤"),
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取数据源列表。
    
    Args:
        skip: 跳过数量
        limit: 每页数量
        search: 搜索关键词
        type_filter: 类型过滤
        status_filter: 状态过滤
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        DataSourceList: 数据源列表
    """
    service = DataSourceService(db)
    
    data_sources, total = service.get_data_sources(
        skip=skip,
        limit=limit,
        search=search,
        type_filter=type_filter,
        status_filter=status_filter
    )
    
    # 计算分页信息
    pages = (total + limit - 1) // limit
    page = skip // limit + 1
    
    return DataSourceList(
        items=data_sources,
        total=total,
        page=page,
        size=limit,
        pages=pages
    )


@router.get("/{data_source_id}", response_model=DataSourceResponse)
async def get_data_source(
    data_source_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取数据源详情。
    
    Args:
        data_source_id: 数据源ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        DataSourceResponse: 数据源信息
    """
    service = DataSourceService(db)
    data_source = service.get_data_source(data_source_id)
    
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )
    
    return data_source


@router.put("/{data_source_id}", response_model=DataSourceResponse)
async def update_data_source(
    data_source_id: int,
    data_source_data: DataSourceUpdate,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    更新数据源。
    
    Args:
        data_source_id: 数据源ID
        data_source_data: 更新数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        DataSourceResponse: 更新后的数据源信息
    """
    # 检查权限
    if not deps.is_analyst_or_above(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要分析师及以上权限"
        )
    
    service = DataSourceService(db)
    
    try:
        data_source = service.update_data_source(data_source_id, data_source_data)
        if not data_source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="数据源不存在"
            )
        return data_source
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{data_source_id}")
async def delete_data_source(
    data_source_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    删除数据源。
    
    Args:
        data_source_id: 数据源ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        dict: 删除结果
    """
    # 检查权限：只有管理员可以删除数据源
    if not deps.is_admin_or_superuser(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    
    service = DataSourceService(db)
    
    if not service.delete_data_source(data_source_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )
    
    return {"message": "数据源删除成功"}


@router.post("/test", response_model=DataSourceTestResponse)
async def test_data_source_connection(
    test_data: DataSourceTestRequest,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    测试数据源连接。
    
    Args:
        test_data: 测试数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        DataSourceTestResponse: 测试结果
    """
    # 检查权限
    if not deps.is_analyst_or_above(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要分析师及以上权限"
        )
    
    service = DataSourceService(db)
    result = service.test_connection(test_data)
    
    return DataSourceTestResponse(**result)


@router.post("/{data_source_id}/test", response_model=DataSourceTestResponse)
async def test_existing_data_source(
    data_source_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    测试已存在数据源的连接。
    
    Args:
        data_source_id: 数据源ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        DataSourceTestResponse: 测试结果
    """
    service = DataSourceService(db)
    result = service.test_data_source_connection(data_source_id)
    
    return DataSourceTestResponse(**result)


@router.post("/query", response_model=DataSourceQueryResponse)
async def execute_data_source_query(
    query_data: DataSourceQuery,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    执行数据源查询。
    
    Args:
        query_data: 查询数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        DataSourceQueryResponse: 查询结果
    """
    service = DataSourceService(db)
    result = service.execute_query(query_data)
    
    return DataSourceQueryResponse(**result)
