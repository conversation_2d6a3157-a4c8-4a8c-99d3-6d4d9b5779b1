/**
 * 数据可视化页面组件
 */

import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Select,
  Tabs,
  Modal,
  Form,
  Input,
} from 'antd'
import {
  PlusOutlined,
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  Pie<PERSON>hartOutlined,
  Area<PERSON><PERSON>Outlined,
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'

const { Title } = Typography
const { Option } = Select
const { TabPane } = Tabs

const Visualization: React.FC = () => {
  const [activeTab, setActiveTab] = useState('charts')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  // 示例图表配置
  const barChartOption = {
    title: {
      text: '月度销售额',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [120, 200, 150, 80, 70, 110],
        type: 'bar',
        itemStyle: {
          color: '#1890ff',
        },
      },
    ],
  }

  const lineChartOption = {
    title: {
      text: '用户增长趋势',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [820, 932, 901, 934, 1290, 1330],
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#52c41a',
        },
      },
    ],
  }

  const pieChartOption = {
    title: {
      text: '产品类别分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: '产品类别',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '电子产品' },
          { value: 735, name: '服装' },
          { value: 580, name: '食品' },
          { value: 484, name: '图书' },
          { value: 300, name: '其他' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  const areaChartOption = {
    title: {
      text: '访问量统计',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        type: 'line',
        areaStyle: {
          color: 'rgba(24, 144, 255, 0.3)',
        },
        itemStyle: {
          color: '#1890ff',
        },
      },
    ],
  }

  const chartTypes = [
    { key: 'bar', name: '柱状图', icon: <BarChartOutlined />, option: barChartOption },
    { key: 'line', name: '折线图', icon: <LineChartOutlined />, option: lineChartOption },
    { key: 'pie', name: '饼图', icon: <PieChartOutlined />, option: pieChartOption },
    { key: 'area', name: '面积图', icon: <AreaChartOutlined />, option: areaChartOption },
  ]

  const dashboards = [
    {
      id: 1,
      name: '销售仪表板',
      description: '展示销售相关的关键指标',
      charts: ['月度销售额', '用户增长趋势'],
      created_at: '2024-01-01',
    },
    {
      id: 2,
      name: '用户分析仪表板',
      description: '用户行为和分布分析',
      charts: ['产品类别分布', '访问量统计'],
      created_at: '2024-01-02',
    },
  ]

  const handleCreateChart = () => {
    setIsModalVisible(true)
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      console.log('创建图表:', values)
      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>数据可视化</Title>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateChart}>
            创建图表
          </Button>
          <Button>创建仪表板</Button>
        </Space>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="图表库" key="charts">
          <Row gutter={[16, 16]}>
            {chartTypes.map((chart) => (
              <Col xs={24} lg={12} key={chart.key}>
                <Card
                  title={
                    <Space>
                      {chart.icon}
                      {chart.name}
                    </Space>
                  }
                  extra={
                    <Space>
                      <Button type="link">编辑</Button>
                      <Button type="link">导出</Button>
                    </Space>
                  }
                >
                  <ReactECharts option={chart.option} style={{ height: 300 }} />
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="仪表板" key="dashboards">
          <Row gutter={[16, 16]}>
            {dashboards.map((dashboard) => (
              <Col xs={24} sm={12} lg={8} key={dashboard.id}>
                <Card
                  title={dashboard.name}
                  extra={
                    <Space>
                      <Button type="link">编辑</Button>
                      <Button type="link">查看</Button>
                    </Space>
                  }
                >
                  <p>{dashboard.description}</p>
                  <div style={{ marginTop: 16 }}>
                    <strong>包含图表:</strong>
                    <ul style={{ marginTop: 8, paddingLeft: 16 }}>
                      {dashboard.charts.map((chart, index) => (
                        <li key={index}>{chart}</li>
                      ))}
                    </ul>
                  </div>
                  <div style={{ marginTop: 16, color: '#999', fontSize: 12 }}>
                    创建时间: {dashboard.created_at}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="模板库" key="templates">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={8}>
              <Card
                title="销售分析模板"
                cover={
                  <div style={{ height: 200, background: '#f0f2f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <BarChartOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  </div>
                }
                actions={[
                  <Button type="link">预览</Button>,
                  <Button type="link">使用模板</Button>,
                ]}
              >
                <p>包含销售额、增长率、地区分布等关键指标的综合分析模板</p>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Card
                title="用户行为模板"
                cover={
                  <div style={{ height: 200, background: '#f0f2f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <PieChartOutlined style={{ fontSize: 48, color: '#52c41a' }} />
                  </div>
                }
                actions={[
                  <Button type="link">预览</Button>,
                  <Button type="link">使用模板</Button>,
                ]}
              >
                <p>用户访问、转化、留存等行为数据的可视化分析模板</p>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Card
                title="财务报表模板"
                cover={
                  <div style={{ height: 200, background: '#f0f2f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <LineChartOutlined style={{ fontSize: 48, color: '#faad14' }} />
                  </div>
                }
                actions={[
                  <Button type="link">预览</Button>,
                  <Button type="link">使用模板</Button>,
                ]}
              >
                <p>收入、支出、利润等财务数据的专业报表模板</p>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      <Modal
        title="创建图表"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="图表名称"
            rules={[{ required: true, message: '请输入图表名称' }]}
          >
            <Input placeholder="请输入图表名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="图表类型"
            rules={[{ required: true, message: '请选择图表类型' }]}
          >
            <Select placeholder="请选择图表类型">
              <Option value="bar">柱状图</Option>
              <Option value="line">折线图</Option>
              <Option value="pie">饼图</Option>
              <Option value="area">面积图</Option>
              <Option value="scatter">散点图</Option>
              <Option value="heatmap">热力图</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dataSource"
            label="数据源"
            rules={[{ required: true, message: '请选择数据源' }]}
          >
            <Select placeholder="请选择数据源">
              <Option value="1">主数据库</Option>
              <Option value="2">用户数据</Option>
              <Option value="3">日志数据</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入图表描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Visualization
