/**
 * 数字化大屏页面组件
 */

import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Tabs,
  Tag,
} from 'antd'
import {
  PlusOutlined,
  DesktopOutlined,
  EyeOutlined,
  EditOutlined,
  FullscreenOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface BigScreen {
  id: number
  name: string
  description: string
  resolution: string
  theme: string
  status: 'draft' | 'published'
  components: number
  created_at: string
  preview_url: string
}

const BigScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState('screens')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  const [screens] = useState<BigScreen[]>([
    {
      id: 1,
      name: '销售监控大屏',
      description: '实时展示全国销售数据和关键指标',
      resolution: '1920x1080',
      theme: '科技蓝',
      status: 'published',
      components: 8,
      created_at: '2024-01-01',
      preview_url: '/big-screen/1/preview',
    },
    {
      id: 2,
      name: '运营数据大屏',
      description: '展示用户活跃度、订单处理等运营数据',
      resolution: '3840x2160',
      theme: '商务黑',
      status: 'draft',
      components: 6,
      created_at: '2024-01-02',
      preview_url: '/big-screen/2/preview',
    },
    {
      id: 3,
      name: '财务报表大屏',
      description: '财务收支、利润分析等数据展示',
      resolution: '1920x1080',
      theme: '金融绿',
      status: 'published',
      components: 10,
      created_at: '2024-01-03',
      preview_url: '/big-screen/3/preview',
    },
  ])

  const templates = [
    {
      id: 1,
      name: '企业运营监控',
      description: '适用于企业运营数据的实时监控展示',
      preview: '/templates/1.jpg',
      components: ['KPI指标卡', '地图热力图', '实时数据流', '趋势图表'],
      theme: '科技蓝',
    },
    {
      id: 2,
      name: '智慧城市指挥',
      description: '城市管理和公共服务数据展示',
      preview: '/templates/2.jpg',
      components: ['城市地图', '交通流量', '环境监测', '设施状态'],
      theme: '政务蓝',
    },
    {
      id: 3,
      name: '零售门店展示',
      description: '门店销售业绩和客流数据展示',
      preview: '/templates/3.jpg',
      components: ['销售排行', '客流统计', '商品热销', '会员分析'],
      theme: '商务橙',
    },
  ]

  const components = [
    { name: 'KPI指标卡', category: '数据展示', icon: '📊' },
    { name: '实时数字', category: '数据展示', icon: '🔢' },
    { name: '进度条', category: '数据展示', icon: '📈' },
    { name: '柱状图', category: '图表', icon: '📊' },
    { name: '折线图', category: '图表', icon: '📈' },
    { name: '饼图', category: '图表', icon: '🥧' },
    { name: '地图', category: '地理', icon: '🗺️' },
    { name: '热力图', category: '地理', icon: '🌡️' },
    { name: '装饰边框', category: '装饰', icon: '🖼️' },
    { name: '背景图片', category: '装饰', icon: '🖼️' },
    { name: '滚动文本', category: '文本', icon: '📝' },
    { name: '时间显示', category: '文本', icon: '⏰' },
  ]

  const handleCreateScreen = () => {
    setIsModalVisible(true)
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      console.log('创建大屏:', values)
      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  const handlePreview = (screen: BigScreen) => {
    window.open(screen.preview_url, '_blank')
  }

  const handleEdit = (screen: BigScreen) => {
    console.log('编辑大屏:', screen)
  }

  const handleFullscreen = (screen: BigScreen) => {
    window.open(`${screen.preview_url}?fullscreen=true`, '_blank')
  }

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>数字化大屏</Title>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateScreen}>
            创建大屏
          </Button>
          <Button>模板库</Button>
        </Space>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="我的大屏" key="screens">
          <Row gutter={[16, 16]}>
            {screens.map((screen) => (
              <Col xs={24} sm={12} lg={8} key={screen.id}>
                <Card
                  cover={
                    <div
                      style={{
                        height: 200,
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: 48,
                      }}
                    >
                      <DesktopOutlined />
                    </div>
                  }
                  actions={[
                    <Button
                      type="link"
                      icon={<EyeOutlined />}
                      onClick={() => handlePreview(screen)}
                    >
                      预览
                    </Button>,
                    <Button
                      type="link"
                      icon={<EditOutlined />}
                      onClick={() => handleEdit(screen)}
                    >
                      编辑
                    </Button>,
                    <Button
                      type="link"
                      icon={<FullscreenOutlined />}
                      onClick={() => handleFullscreen(screen)}
                    >
                      全屏
                    </Button>,
                  ]}
                >
                  <Card.Meta
                    title={
                      <Space>
                        {screen.name}
                        <Tag color={screen.status === 'published' ? 'green' : 'orange'}>
                          {screen.status === 'published' ? '已发布' : '草稿'}
                        </Tag>
                      </Space>
                    }
                    description={screen.description}
                  />
                  <div style={{ marginTop: 16 }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <div>
                        <Text type="secondary">分辨率: </Text>
                        <Text>{screen.resolution}</Text>
                      </div>
                      <div>
                        <Text type="secondary">主题: </Text>
                        <Text>{screen.theme}</Text>
                      </div>
                      <div>
                        <Text type="secondary">组件数: </Text>
                        <Text>{screen.components}</Text>
                      </div>
                      <div>
                        <Text type="secondary">创建时间: </Text>
                        <Text>{screen.created_at}</Text>
                      </div>
                    </Space>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="模板库" key="templates">
          <Row gutter={[16, 16]}>
            {templates.map((template) => (
              <Col xs={24} sm={12} lg={8} key={template.id}>
                <Card
                  cover={
                    <div
                      style={{
                        height: 200,
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: 24,
                      }}
                    >
                      {template.name}
                    </div>
                  }
                  actions={[
                    <Button type="link">预览</Button>,
                    <Button type="link">使用模板</Button>,
                  ]}
                >
                  <Card.Meta
                    title={template.name}
                    description={template.description}
                  />
                  <div style={{ marginTop: 16 }}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>包含组件:</Text>
                    </div>
                    <Space wrap>
                      {template.components.map((component, index) => (
                        <Tag key={index} color="blue">
                          {component}
                        </Tag>
                      ))}
                    </Space>
                    <div style={{ marginTop: 12 }}>
                      <Text type="secondary">主题: </Text>
                      <Text>{template.theme}</Text>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="组件库" key="components">
          <Row gutter={[16, 16]}>
            {components.map((component, index) => (
              <Col xs={12} sm={8} lg={6} key={index}>
                <Card
                  hoverable
                  style={{ textAlign: 'center' }}
                  bodyStyle={{ padding: 16 }}
                >
                  <div style={{ fontSize: 32, marginBottom: 8 }}>
                    {component.icon}
                  </div>
                  <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                    {component.name}
                  </div>
                  <Tag size="small" color="blue">
                    {component.category}
                  </Tag>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>
      </Tabs>

      <Modal
        title="创建大屏"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="大屏名称"
            rules={[{ required: true, message: '请输入大屏名称' }]}
          >
            <Input placeholder="请输入大屏名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入大屏描述" />
          </Form.Item>

          <Form.Item
            name="resolution"
            label="分辨率"
            rules={[{ required: true, message: '请选择分辨率' }]}
          >
            <Select placeholder="请选择分辨率">
              <Option value="1920x1080">1920x1080 (Full HD)</Option>
              <Option value="2560x1440">2560x1440 (2K)</Option>
              <Option value="3840x2160">3840x2160 (4K)</Option>
              <Option value="1366x768">1366x768</Option>
              <Option value="custom">自定义</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="theme"
            label="主题"
            rules={[{ required: true, message: '请选择主题' }]}
          >
            <Select placeholder="请选择主题">
              <Option value="tech-blue">科技蓝</Option>
              <Option value="business-black">商务黑</Option>
              <Option value="finance-green">金融绿</Option>
              <Option value="government-blue">政务蓝</Option>
              <Option value="retail-orange">零售橙</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="template"
            label="使用模板"
          >
            <Select placeholder="选择模板（可选）" allowClear>
              <Option value="1">企业运营监控</Option>
              <Option value="2">智慧城市指挥</Option>
              <Option value="3">零售门店展示</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default BigScreen
