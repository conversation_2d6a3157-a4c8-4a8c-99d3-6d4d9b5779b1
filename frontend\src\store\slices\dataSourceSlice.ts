/**
 * 数据源状态管理
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

interface DataSource {
  id: number
  name: string
  type: string
  status: 'connected' | 'disconnected' | 'error'
  config: Record<string, any>
  created_at: string
  updated_at: string
}

interface DataSourceState {
  dataSources: DataSource[]
  currentDataSource: DataSource | null
  loading: boolean
  error: string | null
}

const initialState: DataSourceState = {
  dataSources: [],
  currentDataSource: null,
  loading: false,
  error: null,
}

const dataSourceSlice = createSlice({
  name: 'dataSource',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
})

export const { clearError } = dataSourceSlice.actions
export default dataSourceSlice.reducer
