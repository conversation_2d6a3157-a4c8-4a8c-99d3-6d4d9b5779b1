/**
 * 大屏状态管理
 */

import { createSlice } from '@reduxjs/toolkit'

interface BigScreenState {
  screens: any[]
  currentScreen: any | null
  components: any[]
  templates: any[]
  loading: boolean
  error: string | null
}

const initialState: BigScreenState = {
  screens: [],
  currentScreen: null,
  components: [],
  templates: [],
  loading: false,
  error: null,
}

const bigScreenSlice = createSlice({
  name: 'bigScreen',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
})

export const { clearError } = bigScreenSlice.actions
export default bigScreenSlice.reducer
