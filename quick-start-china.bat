@echo off
echo Quick Start BI Platform (China Mirror)
echo =======================================

REM 创建环境配置文件
if not exist "backend\.env" (
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env" >nul
        echo Created environment config file
    )
)

echo Configuring Docker to use China mirrors...

REM 配置Docker使用国内镜像
echo {^
  "registry-mirrors": [^
    "https://docker.mirrors.ustc.edu.cn",^
    "https://hub-mirror.c.163.com",^
    "https://mirror.baidubce.com"^
  ]^
} > %USERPROFILE%\.docker\daemon.json

echo Restarting Docker Desktop (this may take a moment)...
taskkill /f /im "Docker Desktop.exe" >nul 2>&1
timeout /t 3 /nobreak >nul
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
timeout /t 10 /nobreak >nul

echo Starting services with optimized build...
docker-compose -f docker-compose.dev.yml up -d

echo.
echo Waiting for services to start...
timeout /t 20 /nobreak >nul

echo.
echo Checking service status...
docker-compose -f docker-compose.dev.yml ps

echo.
echo Startup complete!
echo Frontend: http://localhost
echo Backend: http://localhost:8000
echo API Docs: http://localhost:8000/api/v1/docs
echo.
echo Default login: <EMAIL> / admin123
echo.
echo Note: This uses development mode for faster startup
echo.
pause
