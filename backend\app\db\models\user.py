"""
用户数据模型

定义用户相关的数据库模型。
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import <PERSON>olean, Column, DateTime, Enum as SQLE<PERSON>, Integer, String, Text
from sqlalchemy.sql import func

from app.db.base import Base


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"  # 超级管理员
    ADMIN = "admin"              # 管理员
    ANALYST = "analyst"          # 分析师
    USER = "user"               # 普通用户


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(200), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    
    # 用户状态
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # 用户角色
    role = Column(SQLEnum(UserRole), default=UserRole.USER, nullable=False)
    
    # 个人设置
    avatar_url = Column(String(500), nullable=True)
    phone = Column(String(20), nullable=True)
    department = Column(String(100), nullable=True)
    position = Column(String(100), nullable=True)
    
    # 偏好设置
    language = Column(String(10), default="zh", nullable=False)
    theme = Column(String(20), default="light", nullable=False)
    timezone = Column(String(50), default="Asia/Shanghai", nullable=False)
    
    # 通知设置
    email_notifications = Column(Boolean, default=True, nullable=False)
    push_notifications = Column(Boolean, default=True, nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    
    # 其他信息
    bio = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', username='{self.username}')>"
