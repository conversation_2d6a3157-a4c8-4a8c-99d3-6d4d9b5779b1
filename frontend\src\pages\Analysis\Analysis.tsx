/**
 * 自然语言分析页面组件
 */

import React, { useState } from 'react'
import {
  Card,
  Input,
  Button,
  Space,
  Typography,
  List,
  Tag,
  Spin,
  Empty,
  Row,
  Col,
  Divider,
} from 'antd'
import {
  SendOutlined,
  HistoryOutlined,
  QuestionCircleOutlined,
  BulbOutlined,
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input

interface QueryResult {
  id: number
  query: string
  sql: string
  result: any[]
  chart?: any
  timestamp: string
  status: 'success' | 'error'
  error?: string
}

const Analysis: React.FC = () => {
  const [query, setQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<QueryResult[]>([
    {
      id: 1,
      query: '显示上个月的销售趋势',
      sql: 'SELECT DATE(created_at) as date, SUM(amount) as total FROM orders WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH) GROUP BY DATE(created_at)',
      result: [
        { date: '2024-01-01', total: 12500 },
        { date: '2024-01-02', total: 15600 },
        { date: '2024-01-03', total: 18900 },
      ],
      timestamp: '2024-01-15 10:30:00',
      status: 'success',
    },
  ])

  const [queryHistory] = useState([
    '显示上个月的销售趋势',
    '各地区的用户分布情况',
    '产品销量排行榜前10',
    '最近一周的订单量变化',
    '用户年龄分布统计',
  ])

  const [suggestions] = useState([
    '显示本月销售额',
    '用户增长趋势',
    '产品类别分析',
    '地区销售对比',
    '客户满意度统计',
  ])

  const handleSubmit = async () => {
    if (!query.trim()) return

    setLoading(true)
    
    // 模拟API调用
    setTimeout(() => {
      const newResult: QueryResult = {
        id: Date.now(),
        query: query,
        sql: 'SELECT * FROM sample_table WHERE condition = "example"',
        result: [
          { name: '产品A', value: 120 },
          { name: '产品B', value: 80 },
          { name: '产品C', value: 150 },
        ],
        timestamp: new Date().toLocaleString(),
        status: 'success',
      }
      
      setResults(prev => [newResult, ...prev])
      setQuery('')
      setLoading(false)
    }, 2000)
  }

  const chartOption = {
    title: {
      text: '查询结果可视化',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: '数据',
        type: 'pie',
        radius: '50%',
        data: results[0]?.result || [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  return (
    <div>
      <Title level={2}>自然语言数据分析</Title>
      <Paragraph type="secondary">
        使用自然语言描述您想要分析的数据，系统将自动生成SQL查询并展示结果
      </Paragraph>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          {/* 查询输入区域 */}
          <Card title="数据查询" style={{ marginBottom: 24 }}>
            <Space.Compact style={{ width: '100%' }}>
              <TextArea
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="请用自然语言描述您想要查询的数据，例如：显示上个月的销售趋势"
                autoSize={{ minRows: 3, maxRows: 6 }}
                style={{ flex: 1 }}
              />
            </Space.Compact>
            <div style={{ marginTop: 16, textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<SendOutlined />}
                loading={loading}
                onClick={handleSubmit}
                disabled={!query.trim()}
              >
                分析数据
              </Button>
            </div>
          </Card>

          {/* 查询结果 */}
          <Card title="查询结果">
            {loading ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>正在分析您的查询...</div>
              </div>
            ) : results.length > 0 ? (
              <List
                dataSource={results}
                renderItem={(item) => (
                  <List.Item key={item.id}>
                    <Card style={{ width: '100%' }}>
                      <div style={{ marginBottom: 16 }}>
                        <Space>
                          <Tag color="blue">查询</Tag>
                          <Text strong>{item.query}</Text>
                          <Text type="secondary">{item.timestamp}</Text>
                        </Space>
                      </div>
                      
                      <Divider />
                      
                      <div style={{ marginBottom: 16 }}>
                        <Text strong>生成的SQL:</Text>
                        <div style={{ 
                          background: '#f5f5f5', 
                          padding: 12, 
                          borderRadius: 4, 
                          marginTop: 8,
                          fontFamily: 'monospace',
                          fontSize: 12,
                        }}>
                          {item.sql}
                        </div>
                      </div>

                      {item.result && item.result.length > 0 && (
                        <div>
                          <Text strong>查询结果:</Text>
                          <ReactECharts 
                            option={chartOption} 
                            style={{ height: 300, marginTop: 16 }} 
                          />
                        </div>
                      )}
                    </Card>
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无查询结果" />
            )}
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          {/* 查询建议 */}
          <Card 
            title={
              <Space>
                <BulbOutlined />
                查询建议
              </Space>
            }
            style={{ marginBottom: 24 }}
          >
            <List
              size="small"
              dataSource={suggestions}
              renderItem={(item) => (
                <List.Item>
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto', textAlign: 'left' }}
                    onClick={() => setQuery(item)}
                  >
                    {item}
                  </Button>
                </List.Item>
              )}
            />
          </Card>

          {/* 查询历史 */}
          <Card
            title={
              <Space>
                <HistoryOutlined />
                查询历史
              </Space>
            }
            style={{ marginBottom: 24 }}
          >
            <List
              size="small"
              dataSource={queryHistory}
              renderItem={(item) => (
                <List.Item>
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto', textAlign: 'left' }}
                    onClick={() => setQuery(item)}
                  >
                    {item}
                  </Button>
                </List.Item>
              )}
            />
          </Card>

          {/* 帮助信息 */}
          <Card
            title={
              <Space>
                <QuestionCircleOutlined />
                使用帮助
              </Space>
            }
          >
            <div style={{ fontSize: 12, lineHeight: 1.6 }}>
              <p><strong>查询示例：</strong></p>
              <ul style={{ paddingLeft: 16 }}>
                <li>显示上个月的销售数据</li>
                <li>各地区用户数量统计</li>
                <li>产品销量排行榜</li>
                <li>最近一周的订单趋势</li>
              </ul>
              <p><strong>支持的时间表达：</strong></p>
              <ul style={{ paddingLeft: 16 }}>
                <li>今天、昨天、本周、上周</li>
                <li>本月、上个月、今年</li>
                <li>最近7天、最近30天</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Analysis
