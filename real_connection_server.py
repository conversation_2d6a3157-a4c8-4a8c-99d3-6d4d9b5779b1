#!/usr/bin/env python3
"""
真实连接测试服务器

提供真实的数据库连接测试功能，而不是模拟测试
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import sqlite3
import os
import socket
import threading
import time
import webbrowser
from datetime import datetime

class RealConnectionTestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_test_page()
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        if self.path == '/api/test-connection':
            self.handle_real_connection_test()
        else:
            self.send_response(404)
            self.end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def handle_real_connection_test(self):
        """处理真实连接测试"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            db_type = data.get('type', '').lower()

            print(f"🔍 收到连接测试请求: {db_type}")
            print(f"📋 连接参数: {self._safe_log_data(data)}")

            # 根据数据库类型进行真实连接测试
            if db_type == 'sqlite':
                result = self._test_sqlite_connection(data)
            elif db_type == 'postgresql':
                result = self._test_postgresql_connection(data)
            elif db_type == 'mysql':
                result = self._test_mysql_connection(data)
            elif db_type == 'mssql':
                result = self._test_mssql_connection(data)
            elif db_type == 'mongodb':
                result = self._test_mongodb_connection(data)
            elif db_type == 'redis':
                result = self._test_redis_connection(data)
            else:
                result = {
                    'success': False,
                    'message': f'不支持的数据库类型: {db_type}',
                    'error_type': 'UNSUPPORTED_TYPE'
                }

            result['test_time'] = datetime.now().isoformat()
            result['mode'] = '真实连接测试'

            if result['success']:
                print(f"✅ {db_type} 连接测试成功")
            else:
                print(f"❌ {db_type} 连接测试失败: {result['message']}")

            self.send_json_response(result)

        except Exception as e:
            print(f"❌ 连接测试异常: {str(e)}")
            error_result = {
                'success': False,
                'message': f'连接测试异常: {str(e)}',
                'error_type': 'INTERNAL_ERROR',
                'test_time': datetime.now().isoformat(),
                'mode': '真实连接测试'
            }
            self.send_json_response(error_result)

    def _safe_log_data(self, data):
        """安全地记录数据（隐藏密码）"""
        safe_data = data.copy()
        if 'password' in safe_data:
            safe_data['password'] = '***'
        return safe_data

    def _test_sqlite_connection(self, data):
        """测试SQLite连接"""
        database = data.get('database', '')

        if not database:
            return {
                'success': False,
                'message': '数据库路径不能为空',
                'error_type': 'MISSING_DATABASE'
            }

        try:
            # 检查文件是否存在（除了:memory:）
            if database != ':memory:' and not os.path.exists(database):
                return {
                    'success': False,
                    'message': f'数据库文件不存在: {database}',
                    'error_type': 'FILE_NOT_FOUND',
                    'suggestion': '请检查文件路径是否正确，或使用 :memory: 创建内存数据库'
                }

            # 尝试连接
            conn = sqlite3.connect(database, timeout=5)
            cursor = conn.cursor()

            # 执行简单查询测试
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]

            # 获取表数量
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]

            conn.close()

            return {
                'success': True,
                'message': 'SQLite连接成功',
                'details': {
                    'version': f'SQLite {version}',
                    'database': database,
                    'table_count': table_count,
                    'driver': '内置sqlite3模块',
                    'features': ['嵌入式数据库', 'ACID事务', 'JSON支持']
                }
            }

        except sqlite3.OperationalError as e:
            return {
                'success': False,
                'message': f'SQLite操作错误: {str(e)}',
                'error_type': 'OPERATIONAL_ERROR',
                'suggestion': '请检查数据库文件权限或路径格式'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'SQLite连接失败: {str(e)}',
                'error_type': 'CONNECTION_ERROR'
            }

    def _test_postgresql_connection(self, data):
        """测试PostgreSQL连接"""
        try:
            import psycopg2
        except ImportError:
            return {
                'success': False,
                'message': 'PostgreSQL驱动未安装，请安装 psycopg2-binary',
                'error_type': 'DRIVER_NOT_FOUND',
                'suggestion': 'pip install psycopg2-binary'
            }

        host = data.get('host', 'localhost')
        port = data.get('port', 5432)
        database = data.get('database', '')
        username = data.get('username', '')
        password = data.get('password', '')

        if not all([database, username]):
            return {
                'success': False,
                'message': '数据库名称和用户名不能为空',
                'error_type': 'MISSING_CREDENTIALS'
            }

        try:
            # 首先测试网络连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, int(port)))
            sock.close()

            if result != 0:
                return {
                    'success': False,
                    'message': f'无法连接到 {host}:{port}',
                    'error_type': 'NETWORK_ERROR',
                    'suggestion': '请检查主机地址、端口号和网络连接'
                }

            # 尝试数据库连接
            conn = psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password,
                connect_timeout=10
            )

            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'")
            table_count = cursor.fetchone()[0]

            conn.close()

            return {
                'success': True,
                'message': 'PostgreSQL连接成功',
                'details': {
                    'version': version.split(' ')[0] + ' ' + version.split(' ')[1],
                    'host': host,
                    'port': port,
                    'database': database,
                    'table_count': table_count,
                    'driver': 'psycopg2',
                    'features': ['ACID事务', 'JSON支持', '全文搜索', '并行查询']
                }
            }

        except psycopg2.OperationalError as e:
            error_msg = str(e)
            if 'authentication failed' in error_msg.lower():
                return {
                    'success': False,
                    'message': '认证失败，请检查用户名和密码',
                    'error_type': 'AUTH_FAILED'
                }
            elif 'database' in error_msg.lower() and 'does not exist' in error_msg.lower():
                return {
                    'success': False,
                    'message': f'数据库 "{database}" 不存在',
                    'error_type': 'DATABASE_NOT_FOUND'
                }
            else:
                return {
                    'success': False,
                    'message': f'PostgreSQL连接失败: {error_msg}',
                    'error_type': 'CONNECTION_ERROR'
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'PostgreSQL连接异常: {str(e)}',
                'error_type': 'UNKNOWN_ERROR'
            }

    def _test_mysql_connection(self, data):
        """测试MySQL连接"""
        try:
            import pymysql
        except ImportError:
            return {
                'success': False,
                'message': 'MySQL驱动未安装，请安装 pymysql',
                'error_type': 'DRIVER_NOT_FOUND',
                'suggestion': 'pip install pymysql'
            }

        host = data.get('host', 'localhost')
        port = data.get('port', 3306)
        database = data.get('database', '')
        username = data.get('username', '')
        password = data.get('password', '')

        if not all([database, username]):
            return {
                'success': False,
                'message': '数据库名称和用户名不能为空',
                'error_type': 'MISSING_CREDENTIALS'
            }

        try:
            # 测试网络连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, int(port)))
            sock.close()

            if result != 0:
                return {
                    'success': False,
                    'message': f'无法连接到 {host}:{port}',
                    'error_type': 'NETWORK_ERROR',
                    'suggestion': '请检查主机地址、端口号和网络连接'
                }

            # 尝试数据库连接
            conn = pymysql.connect(
                host=host,
                port=int(port),
                database=database,
                user=username,
                password=password,
                connect_timeout=10,
                charset='utf8mb4'
            )

            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s", (database,))
            table_count = cursor.fetchone()[0]

            conn.close()

            return {
                'success': True,
                'message': 'MySQL连接成功',
                'details': {
                    'version': f'MySQL {version}',
                    'host': host,
                    'port': port,
                    'database': database,
                    'table_count': table_count,
                    'driver': 'pymysql',
                    'features': ['InnoDB引擎', '分区表', 'JSON支持', '窗口函数']
                }
            }

        except pymysql.OperationalError as e:
            error_code, error_msg = e.args
            if error_code == 1045:  # Access denied
                return {
                    'success': False,
                    'message': '认证失败，请检查用户名和密码',
                    'error_type': 'AUTH_FAILED'
                }
            elif error_code == 1049:  # Unknown database
                return {
                    'success': False,
                    'message': f'数据库 "{database}" 不存在',
                    'error_type': 'DATABASE_NOT_FOUND'
                }
            else:
                return {
                    'success': False,
                    'message': f'MySQL连接失败: {error_msg}',
                    'error_type': 'CONNECTION_ERROR'
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'MySQL连接异常: {str(e)}',
                'error_type': 'UNKNOWN_ERROR'
            }

    def _test_mssql_connection(self, data):
        """测试SQL Server连接"""
        try:
            import pyodbc
        except ImportError:
            return {
                'success': False,
                'message': 'SQL Server驱动未安装，请安装 pyodbc',
                'error_type': 'DRIVER_NOT_FOUND',
                'suggestion': 'pip install pyodbc'
            }

        host = data.get('host', 'localhost')
        port = data.get('port', 1433)
        database = data.get('database', '')
        username = data.get('username', '')
        password = data.get('password', '')

        if not all([database, username]):
            return {
                'success': False,
                'message': '数据库名称和用户名不能为空',
                'error_type': 'MISSING_CREDENTIALS'
            }

        try:
            # 测试网络连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, int(port)))
            sock.close()

            if result != 0:
                return {
                    'success': False,
                    'message': f'无法连接到 {host}:{port}',
                    'error_type': 'NETWORK_ERROR',
                    'suggestion': '请检查主机地址、端口号和网络连接'
                }

            # 构建连接字符串
            conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={host},{port};DATABASE={database};UID={username};PWD={password};Timeout=10'

            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            cursor.execute("SELECT @@VERSION")
            version = cursor.fetchone()[0].split('\n')[0]

            cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_catalog = ?", database)
            table_count = cursor.fetchone()[0]

            conn.close()

            return {
                'success': True,
                'message': 'SQL Server连接成功',
                'details': {
                    'version': version,
                    'host': host,
                    'port': port,
                    'database': database,
                    'table_count': table_count,
                    'driver': 'pyodbc + ODBC Driver 17',
                    'features': ['T-SQL', '列存储索引', '内存优化表', 'Always Encrypted']
                }
            }

        except pyodbc.Error as e:
            error_msg = str(e)
            if 'login failed' in error_msg.lower():
                return {
                    'success': False,
                    'message': '认证失败，请检查用户名和密码',
                    'error_type': 'AUTH_FAILED'
                }
            elif 'cannot open database' in error_msg.lower():
                return {
                    'success': False,
                    'message': f'无法打开数据库 "{database}"',
                    'error_type': 'DATABASE_NOT_FOUND'
                }
            else:
                return {
                    'success': False,
                    'message': f'SQL Server连接失败: {error_msg}',
                    'error_type': 'CONNECTION_ERROR'
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'SQL Server连接异常: {str(e)}',
                'error_type': 'UNKNOWN_ERROR'
            }

    def _test_mongodb_connection(self, data):
        """测试MongoDB连接"""
        try:
            from pymongo import MongoClient
        except ImportError:
            return {
                'success': False,
                'message': 'MongoDB驱动未安装，请安装 pymongo',
                'error_type': 'DRIVER_NOT_FOUND',
                'suggestion': 'pip install pymongo'
            }

        host = data.get('host', 'localhost')
        port = data.get('port', 27017)
        database = data.get('database', '')
        username = data.get('username', '')
        password = data.get('password', '')

        if not database:
            return {
                'success': False,
                'message': '数据库名称不能为空',
                'error_type': 'MISSING_DATABASE'
            }

        try:
            # 测试网络连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, int(port)))
            sock.close()

            if result != 0:
                return {
                    'success': False,
                    'message': f'无法连接到 {host}:{port}',
                    'error_type': 'NETWORK_ERROR',
                    'suggestion': '请检查主机地址、端口号和网络连接'
                }

            # 构建连接URI
            if username and password:
                uri = f'mongodb://{username}:{password}@{host}:{port}/{database}'
            else:
                uri = f'mongodb://{host}:{port}/{database}'

            # 尝试连接
            client = MongoClient(uri, serverSelectionTimeoutMS=10000)

            # 测试连接
            client.admin.command('ping')

            # 获取服务器信息
            server_info = client.server_info()
            version = server_info['version']

            # 获取数据库信息
            db = client[database]
            collection_names = db.list_collection_names()

            client.close()

            return {
                'success': True,
                'message': 'MongoDB连接成功',
                'details': {
                    'version': f'MongoDB {version}',
                    'host': host,
                    'port': port,
                    'database': database,
                    'collection_count': len(collection_names),
                    'driver': 'pymongo',
                    'features': ['文档存储', '聚合管道', '分片', '副本集']
                }
            }

        except Exception as e:
            error_msg = str(e)
            if 'authentication failed' in error_msg.lower():
                return {
                    'success': False,
                    'message': '认证失败，请检查用户名和密码',
                    'error_type': 'AUTH_FAILED'
                }
            elif 'timeout' in error_msg.lower():
                return {
                    'success': False,
                    'message': '连接超时，请检查服务器状态',
                    'error_type': 'TIMEOUT'
                }
            else:
                return {
                    'success': False,
                    'message': f'MongoDB连接失败: {error_msg}',
                    'error_type': 'CONNECTION_ERROR'
                }

    def _test_redis_connection(self, data):
        """测试Redis连接"""
        try:
            import redis
        except ImportError:
            return {
                'success': False,
                'message': 'Redis驱动未安装，请安装 redis',
                'error_type': 'DRIVER_NOT_FOUND',
                'suggestion': 'pip install redis'
            }

        host = data.get('host', 'localhost')
        port = data.get('port', 6379)
        password = data.get('password', '')
        database = data.get('database', 0)

        try:
            # 测试网络连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, int(port)))
            sock.close()

            if result != 0:
                return {
                    'success': False,
                    'message': f'无法连接到 {host}:{port}',
                    'error_type': 'NETWORK_ERROR',
                    'suggestion': '请检查主机地址、端口号和网络连接'
                }

            # 尝试连接
            r = redis.Redis(
                host=host,
                port=int(port),
                password=password if password else None,
                db=int(database) if database else 0,
                socket_timeout=10,
                socket_connect_timeout=10
            )

            # 测试连接
            r.ping()

            # 获取服务器信息
            info = r.info()
            version = info['redis_version']
            used_memory = info['used_memory_human']
            connected_clients = info['connected_clients']

            # 获取键数量
            key_count = r.dbsize()

            r.close()

            return {
                'success': True,
                'message': 'Redis连接成功',
                'details': {
                    'version': f'Redis {version}',
                    'host': host,
                    'port': port,
                    'database': database,
                    'key_count': key_count,
                    'used_memory': used_memory,
                    'connected_clients': connected_clients,
                    'driver': 'redis-py',
                    'features': ['键值存储', '发布订阅', 'Lua脚本', '集群模式']
                }
            }

        except redis.AuthenticationError:
            return {
                'success': False,
                'message': '认证失败，请检查密码',
                'error_type': 'AUTH_FAILED'
            }
        except redis.ConnectionError as e:
            return {
                'success': False,
                'message': f'Redis连接失败: {str(e)}',
                'error_type': 'CONNECTION_ERROR'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Redis连接异常: {str(e)}',
                'error_type': 'UNKNOWN_ERROR'
            }

    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False, indent=2).encode('utf-8'))

    def send_test_page(self):
        """发送测试页面"""
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实连接测试服务器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .status { padding: 15px; border-radius: 8px; margin: 20px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .endpoint { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #007bff; }
        code { background: #f1f3f4; padding: 2px 6px; border-radius: 4px; font-family: monospace; }
        .feature-list { list-style: none; padding: 0; }
        .feature-list li { padding: 8px 0; border-bottom: 1px solid #eee; }
        .feature-list li:before { content: "✅ "; margin-right: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 真实连接测试服务器</h1>

        <div class="status success">
            <strong>✅ 服务器运行正常</strong><br>
            真实连接测试服务已启动，可以进行实际的数据库连接验证。
        </div>

        <div class="status info">
            <strong>🎯 服务特色</strong><br>
            与模拟测试不同，本服务器提供真实的数据库连接验证，确保连接参数的准确性。
        </div>

        <h3>📡 API端点</h3>
        <div class="endpoint">
            <strong>POST</strong> <code>/api/test-connection</code><br>
            用于测试数据库连接的真实性
        </div>

        <h3>🔧 支持的数据库</h3>
        <ul class="feature-list">
            <li><strong>SQLite</strong> - 文件数据库，立即可用</li>
            <li><strong>PostgreSQL</strong> - 需要安装 psycopg2-binary</li>
            <li><strong>MySQL</strong> - 需要安装 pymysql</li>
            <li><strong>SQL Server</strong> - 需要安装 pyodbc</li>
            <li><strong>MongoDB</strong> - 需要安装 pymongo</li>
            <li><strong>Redis</strong> - 需要安装 redis</li>
        </ul>

        <h3>⚡ 验证特性</h3>
        <ul class="feature-list">
            <li>网络连接测试</li>
            <li>认证验证</li>
            <li>数据库存在性检查</li>
            <li>版本信息获取</li>
            <li>详细错误诊断</li>
            <li>安全日志记录</li>
        </ul>

        <div class="status warning">
            <strong>⚠️ 注意事项</strong><br>
            • 请确保目标数据库服务正在运行<br>
            • 检查网络连接和防火墙设置<br>
            • 验证用户权限和认证信息<br>
            • 某些数据库需要额外安装驱动
        </div>

        <h3>🚀 使用方法</h3>
        <p>将高级数据源管理平台的连接测试API地址修改为：</p>
        <code>http://localhost:8080/api/test-connection</code>

        <p style="margin-top: 30px; text-align: center; color: #666;">
            🔗 <strong>真实连接测试服务器</strong> - 确保数据源连接的真实性和可靠性
        </p>
    </div>
</body>
</html>'''

        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))

def start_server(port=8080):
    """启动服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, RealConnectionTestHandler)

    print("=" * 60)
    print("🔍 真实连接测试服务器")
    print("=" * 60)
    print(f"🚀 服务器启动成功")
    print(f"📡 监听端口: {port}")
    print(f"🌐 访问地址: http://localhost:{port}")
    print(f"📋 API端点: http://localhost:{port}/api/test-connection")
    print()
    print("🔧 支持的数据库:")
    print("   ✅ SQLite (内置支持)")
    print("   🔧 PostgreSQL (需要 psycopg2-binary)")
    print("   🔧 MySQL (需要 pymysql)")
    print("   🔧 SQL Server (需要 pyodbc)")
    print("   🔧 MongoDB (需要 pymongo)")
    print("   🔧 Redis (需要 redis)")
    print()
    print("💡 提示: 修改前端连接测试API地址为:")
    print(f"   http://localhost:{port}/api/test-connection")
    print()
    print("⚠️  按 Ctrl+C 停止服务器")
    print("=" * 60)

    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open(f'http://localhost:{port}')

    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        httpd.shutdown()

if __name__ == '__main__':
    start_server()