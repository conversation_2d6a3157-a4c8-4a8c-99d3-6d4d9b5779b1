@echo off
echo Minimal BI Platform Start (Database Only)
echo ==========================================

echo Starting database services only...
docker-compose up -d postgres redis

echo.
echo Database services started!
echo PostgreSQL: localhost:5432
echo Redis: localhost:6379
echo.
echo To start backend manually:
echo cd backend
echo pip install fastapi uvicorn sqlalchemy psycopg2-binary
echo uvicorn app.main:app --reload
echo.
echo To start frontend manually:
echo cd frontend
echo npm install
echo npm run dev
echo.
pause
