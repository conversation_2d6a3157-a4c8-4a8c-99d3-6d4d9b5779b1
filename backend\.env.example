# 应用配置
PROJECT_NAME=自然语言BI数据分析平台
VERSION=1.0.0
SECRET_KEY=your-secret-key-here
API_V1_STR=/api/v1

# 服务器配置
SERVER_NAME=localhost
SERVER_HOST=http://localhost
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=bi_platform
POSTGRES_PORT=5432

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# JWT配置
ACCESS_TOKEN_EXPIRE_MINUTES=10080
REFRESH_TOKEN_EXPIRE_MINUTES=43200
ALGORITHM=HS256

# 邮件配置
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=BI Platform

# 超级用户配置
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123

# 文件上传配置
MAX_UPLOAD_SIZE=10485760
UPLOAD_FOLDER=uploads

# 日志配置
LOG_LEVEL=INFO

# NLP模型配置
NLP_MODEL_PATH=models
DEFAULT_LANGUAGE=zh
SUPPORTED_LANGUAGES=zh,en

# 缓存配置
CACHE_TTL=300
QUERY_CACHE_TTL=1800
