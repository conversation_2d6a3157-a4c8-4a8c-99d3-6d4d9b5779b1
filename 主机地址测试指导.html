<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主机地址测试指导</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px; color: #333;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { 
            background: white; border-radius: 16px; padding: 30px; margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center;
        }
        .card { 
            background: white; border-radius: 16px; padding: 30px; margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .step { 
            background: #f8fafc; border: 2px solid #e5e7eb; border-radius: 12px; 
            padding: 20px; margin: 15px 0; position: relative;
        }
        .step-number { 
            position: absolute; top: -12px; left: 20px; background: #667eea; 
            color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;
        }
        .test-data { 
            background: #1f2937; color: #e5e7eb; padding: 15px; border-radius: 8px; 
            font-family: monospace; font-size: 13px; margin: 10px 0;
        }
        .expected { 
            background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; 
            padding: 15px; margin: 10px 0;
        }
        .warning { 
            background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; 
            padding: 15px; margin: 10px 0; color: #991b1b;
        }
        .highlight { background: #fef3c7; padding: 2px 6px; border-radius: 4px; font-weight: 600; }
        .checklist { list-style: none; padding: 0; }
        .checklist li { padding: 8px 0; display: flex; align-items: center; }
        .checklist li:before { content: "☐"; margin-right: 10px; font-size: 16px; }
        .checklist li.checked:before { content: "✅"; }
        .btn { 
            background: #667eea; color: white; border: none; padding: 12px 24px;
            border-radius: 10px; cursor: pointer; margin: 8px; font-size: 14px;
            font-weight: 600; transition: all 0.3s ease; text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
        .btn-success { background: #10b981; }
        .btn-danger { background: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 主机地址测试指导</h1>
            <p>测试主机地址输入修复效果</p>
            <div style="margin-top: 15px;">
                <span class="highlight">问题：修改主机地址后页面自动关闭</span>
            </div>
        </div>

        <!-- 测试步骤 -->
        <div class="card">
            <h3>🧪 核心测试步骤</h3>
            
            <!-- 步骤1 -->
            <div class="step">
                <div class="step-number">1</div>
                <h4>🔧 打开创建数据源页面</h4>
                <p><strong>操作:</strong></p>
                <ol>
                    <li>在高级数据源管理平台中点击 <span class="highlight">"➕ 创建数据源"</span></li>
                    <li>确认模态框正常打开且不自动消失</li>
                </ol>
                
                <div class="expected">
                    <strong>✅ 预期结果:</strong> 模态框正常打开并保持稳定
                </div>
            </div>

            <!-- 步骤2 -->
            <div class="step">
                <div class="step-number">2</div>
                <h4>🗄️ 选择需要主机地址的数据库类型</h4>
                <p><strong>推荐选择:</strong> PostgreSQL 或 MySQL</p>
                
                <div class="test-data">
数据源名称: 主机地址测试
描述: 测试主机地址验证修复
数据库类型: PostgreSQL
                </div>
                
                <p><strong>操作:</strong></p>
                <ol>
                    <li>填写数据源名称和描述</li>
                    <li>选择 <span class="highlight">"PostgreSQL"</span> 数据库类型</li>
                    <li>等待连接配置表单出现</li>
                </ol>
            </div>

            <!-- 步骤3 -->
            <div class="step">
                <div class="step-number">3</div>
                <h4>⭐ 测试问题主机地址 (关键测试)</h4>
                <p><strong>目标:</strong> 验证带下划线的主机地址不会导致页面关闭</p>
                
                <div class="test-data">
主机地址: server_01.company.com
端口: 5432
数据库: postgres
用户名: postgres
密码: password
                </div>
                
                <p><strong>操作:</strong></p>
                <ol>
                    <li>在 <span class="highlight">"主机地址"</span> 字段输入: <code>server_01.company.com</code></li>
                    <li>点击其他字段或按 <kbd>Tab</kbd> 键</li>
                    <li>观察页面是否保持打开</li>
                    <li>检查主机地址字段的验证状态</li>
                </ol>
                
                <div class="expected">
                    <strong>✅ 预期结果 (修复后):</strong>
                    <ul>
                        <li>页面保持打开，不会自动关闭</li>
                        <li>主机地址字段显示绿色成功状态</li>
                        <li>显示"✅ 格式正确"提示</li>
                        <li>可以继续填写其他字段</li>
                    </ul>
                </div>
                
                <div class="warning">
                    <strong>❌ 修复前的问题:</strong>
                    <ul>
                        <li>输入带下划线的主机地址后页面会自动关闭</li>
                        <li>无法继续完成数据源创建</li>
                        <li>用户体验极差</li>
                    </ul>
                </div>
            </div>

            <!-- 步骤4 -->
            <div class="step">
                <div class="step-number">4</div>
                <h4>🔍 测试其他主机地址格式</h4>
                <p><strong>目标:</strong> 验证各种有效主机地址格式都能正常工作</p>
                
                <p><strong>测试用例:</strong></p>
                <div class="test-data">
✅ 应该通过的格式:
- localhost
- 127.0.0.1
- *************
- example.com
- sub.example.com
- my-server.local
- server_01.company.com (重点测试)

❌ 应该被拒绝的格式:
- invalid..domain
- .invalid
- host with spaces
                </div>
                
                <p><strong>操作:</strong></p>
                <ol>
                    <li>逐一测试上述主机地址格式</li>
                    <li>观察验证结果和页面稳定性</li>
                    <li>确认错误格式有适当的提示</li>
                </ol>
            </div>

            <!-- 步骤5 -->
            <div class="step">
                <div class="step-number">5</div>
                <h4>💾 完成数据源创建流程</h4>
                <p><strong>目标:</strong> 验证整个创建流程正常工作</p>
                
                <p><strong>操作:</strong></p>
                <ol>
                    <li>使用有效的主机地址完成表单填写</li>
                    <li>点击 <span class="highlight">"🔍 测试连接"</span> (预期会失败，因为服务器不存在)</li>
                    <li>观察错误提示是否正常显示</li>
                    <li>确认可以正常关闭模态框</li>
                </ol>
                
                <div class="expected">
                    <strong>✅ 预期结果:</strong>
                    <ul>
                        <li>连接测试失败但显示具体错误信息</li>
                        <li>页面功能完全正常</li>
                        <li>可以正常关闭和重新打开模态框</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 测试检查清单 -->
        <div class="card">
            <h3>✅ 测试检查清单</h3>
            <ul class="checklist">
                <li id="check-modal">模态框正常打开且稳定</li>
                <li id="check-type">数据库类型选择正常</li>
                <li id="check-host">主机地址 server_01.company.com 输入正常</li>
                <li id="check-stable">页面不会自动关闭</li>
                <li id="check-validation">主机地址验证显示成功状态</li>
                <li id="check-other">其他主机地址格式测试正常</li>
                <li id="check-flow">完整创建流程可以正常进行</li>
            </ul>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-success" onclick="markAllChecked()">✅ 全部测试通过</button>
                <button class="btn" onclick="resetChecklist()">🔄 重置清单</button>
            </div>
        </div>

        <!-- 问题对比 -->
        <div class="card">
            <h3>📊 修复效果对比</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div class="warning">
                    <h4>❌ 修复前</h4>
                    <ul>
                        <li>输入 server_01.company.com 后页面关闭</li>
                        <li>无法使用带下划线的主机名</li>
                        <li>验证逻辑过于严格</li>
                        <li>缺少错误处理机制</li>
                        <li>用户体验极差</li>
                    </ul>
                </div>
                
                <div class="expected">
                    <h4>✅ 修复后</h4>
                    <ul>
                        <li>支持带下划线的主机名</li>
                        <li>页面保持稳定，不会关闭</li>
                        <li>更合理的验证规则</li>
                        <li>完善的错误处理</li>
                        <li>用户体验良好</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 故障排除 -->
        <div class="card">
            <h3>🔧 如果仍有问题</h3>
            
            <div class="warning">
                <strong>如果页面仍然自动关闭:</strong>
                <ol>
                    <li>按 F12 打开浏览器开发者工具</li>
                    <li>查看 Console 标签页的错误信息</li>
                    <li>检查页面底部的操作日志</li>
                    <li>尝试刷新页面后重新测试</li>
                    <li>使用简化测试页面进行对比</li>
                </ol>
            </div>
            
            <div class="expected">
                <strong>如果修复成功:</strong>
                <p>恭喜！主机地址问题已经彻底解决。您现在可以安全地使用各种有效的主机地址格式，包括带下划线的主机名。</p>
            </div>
        </div>
    </div>

    <script>
        function markAllChecked() {
            const items = document.querySelectorAll('.checklist li');
            items.forEach(item => item.classList.add('checked'));
        }
        
        function resetChecklist() {
            const items = document.querySelectorAll('.checklist li');
            items.forEach(item => item.classList.remove('checked'));
        }
        
        // 页面加载时的提示
        window.onload = function() {
            console.log('🧪 主机地址测试指导页面已加载');
            console.log('📋 请按照步骤进行测试，重点验证 server_01.company.com 输入是否正常');
        };
    </script>
</body>
</html>
