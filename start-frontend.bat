@echo off
echo Starting React Frontend
echo =========================

echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo Step 1: Going to frontend directory...
cd frontend

echo Step 2: Configuring npm registry...
npm config set registry https://registry.npmmirror.com

echo Step 3: Installing dependencies...
if not exist "node_modules" (
    echo Installing Node.js dependencies...
    npm install
) else (
    echo Dependencies already installed
)

echo Step 4: Starting development server...
echo.
echo Frontend will be available at: http://localhost:3000
echo Make sure backend is running at: http://localhost:8000
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev

pause
