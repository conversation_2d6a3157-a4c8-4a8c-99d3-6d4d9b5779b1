# 自然语言BI数据分析平台

一个基于自然语言处理的智能数据分析平台，支持自然语言查询、数据可视化和数字化大屏展示。

## 🚀 功能特性

### 核心功能
- **自然语言查询**: 使用自然语言描述数据需求，自动生成SQL查询
- **数据可视化**: 多种图表类型，智能推荐最适合的可视化方式
- **数字化大屏**: 支持多种分辨率的大屏展示，实时数据更新
- **数据源管理**: 支持多种数据源类型（MySQL、PostgreSQL、MongoDB等）
- **用户权限管理**: 基于角色的权限控制系统

### 技术特性
- **前端**: React 18 + TypeScript + Ant Design + ECharts
- **后端**: FastAPI + SQLAlchemy + PostgreSQL + Redis
- **NLP**: Hugging Face Transformers + LangChain
- **部署**: Docker + Kubernetes + CI/CD

## 📋 系统要求

- Node.js 18+
- Python 3.10+
- PostgreSQL 14+
- Redis 7+
- Docker & Docker Compose

## 🛠️ 快速开始

### 使用Docker Compose（推荐）

#### Windows用户
```cmd
# 方法1: 批处理脚本
start.bat

# 方法2: PowerShell脚本
.\start.ps1
```

#### Linux/Mac用户
```bash
# 给脚本执行权限并运行
chmod +x start.sh
./start.sh
```

#### 手动启动
1. **克隆项目**
```bash
git clone <repository-url>
cd bi-platform
```

2. **启动服务**
```bash
# 启动所有服务
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

3. **访问应用**
- 前端: http://localhost
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/api/v1/docs

### 本地开发

#### 后端开发

1. **进入后端目录**
```bash
cd backend
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息
```

5. **启动数据库**
```bash
# 使用Docker启动PostgreSQL和Redis
docker-compose up -d postgres redis
```

6. **运行数据库迁移**
```bash
alembic upgrade head
```

7. **启动后端服务**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端开发

1. **进入前端目录**
```bash
cd frontend
```

2. **安装依赖**
```bash
npm install
```

3. **启动开发服务器**
```bash
npm run dev
```

4. **访问应用**
```bash
http://localhost:3000
```

## 📁 项目结构

```
bi_platform/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── db/             # 数据库模型
│   │   ├── nlp/            # NLP处理模块
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── tests/              # 测试
│   ├── alembic/            # 数据库迁移
│   ├── requirements.txt    # Python依赖
│   └── Dockerfile          # Docker配置
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # UI组件
│   │   ├── pages/          # 页面
│   │   ├── services/       # API调用
│   │   ├── store/          # 状态管理
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   ├── package.json        # Node.js依赖
│   └── Dockerfile          # Docker配置
├── docs/                   # 项目文档
├── docker-compose.yml      # Docker Compose配置
└── README.md              # 项目说明
```

## 🔧 配置说明

### 环境变量

后端主要环境变量：

```bash
# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=bi_platform

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-password
```

### 数据库初始化

首次运行时会自动创建数据库表和初始数据：

- 默认超级管理员账户：<EMAIL> / admin123

## 🧪 测试

### 后端测试

```bash
cd backend
pytest
```

### 前端测试

```bash
cd frontend
npm test
```

## 📚 API文档

启动后端服务后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc

## 🚀 部署

### 生产环境部署

1. **配置生产环境变量**
```bash
# 设置生产环境配置
export ENVIRONMENT=production
export SECRET_KEY=your-production-secret-key
```

2. **使用生产配置启动**
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Kubernetes部署

```bash
# 应用Kubernetes配置
kubectl apply -f k8s/
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：

1. 查看 [文档](docs/)
2. 提交 [Issue](../../issues)
3. 联系开发团队

## 🔄 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新信息。
