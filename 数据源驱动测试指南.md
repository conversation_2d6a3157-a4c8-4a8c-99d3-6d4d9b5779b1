# 🗄️ 数据源驱动测试指南

## 📋 概述

数据源驱动测试平台现已升级为**正式模式**，支持真实的数据库连接和查询操作。

### ✅ 支持的数据库类型

| 数据库 | 状态 | 驱动 | 特性 |
|--------|------|------|------|
| SQLite | ✅ 完全支持 | 内置sqlite3 | 嵌入式、轻量级 |
| PostgreSQL | ✅ 完全支持 | psycopg2-binary | ACID事务、JSON支持 |
| MySQL | ✅ 完全支持 | pymysql | InnoDB引擎、分区表 |
| SQL Server | ✅ 完全支持 | pyodbc | T-SQL、列存储 |
| MongoDB | ✅ 完全支持 | pymongo | 文档存储、聚合管道 |
| Redis | ✅ 完全支持 | redis-py | 键值存储、发布订阅 |

## 🧪 测试步骤

### 1. SQLite 测试（推荐开始）

**配置信息：**
- 数据库类型: `sqlite`
- 数据库路径: `C:\Users\<USER>\test\test\test_database.db`

**测试查询：**
```sql
-- 查看所有表
SELECT name FROM sqlite_master WHERE type='table';

-- 查询用户数据
SELECT * FROM users LIMIT 10;

-- 查询订单统计
SELECT status, COUNT(*) as count FROM orders GROUP BY status;

-- 查询错误日志
SELECT * FROM logs WHERE level = 'ERROR' LIMIT 5;
```

### 2. PostgreSQL 测试

**配置信息：**
- 主机: `localhost`
- 端口: `5432`
- 数据库: `postgres`
- 用户名: `postgres`
- 密码: `your_password`

**测试查询：**
```sql
-- 查看版本
SELECT version();

-- 查看所有表
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
```

### 3. MySQL 测试

**配置信息：**
- 主机: `localhost`
- 端口: `3306`
- 数据库: `mysql`
- 用户名: `root`
- 密码: `your_password`

**测试查询：**
```sql
-- 查看版本
SELECT VERSION();

-- 查看所有表
SHOW TABLES;
```

### 4. MongoDB 测试

**配置信息：**
- 主机: `localhost`
- 端口: `27017`
- 数据库: `test`
- 用户名: `admin` (可选)
- 密码: `password` (可选)

**测试查询：**
```json
{
  "collection": "users",
  "query": {}
}
```

### 5. Redis 测试

**配置信息：**
- 主机: `localhost`
- 端口: `6379`
- 密码: `your_password` (可选)

**测试命令：**
```
KEYS *
GET user:1001
INFO server
```

## 🔧 功能特性

### 真实连接测试
- ✅ 实际连接数据库服务器
- ✅ 获取真实的版本信息
- ✅ 验证连接参数正确性
- ✅ 显示详细的错误信息

### 真实查询执行
- ✅ 执行真实的SQL/NoSQL查询
- ✅ 返回实际的查询结果
- ✅ 显示查询执行时间
- ✅ 支持查询优化建议

### 真实元数据获取
- ✅ 获取真实的表/集合列表
- ✅ 分析真实的表结构
- ✅ 显示列信息和数据类型
- ✅ 统计行数和大小信息

## 🎯 测试建议

### 开始测试
1. **从SQLite开始** - 已经准备好测试数据
2. **测试连接** - 验证驱动是否正常工作
3. **执行查询** - 测试查询功能
4. **查看表结构** - 验证元数据获取

### 高级测试
1. **连接外部数据库** - 测试真实的生产环境
2. **复杂查询** - 测试JOIN、聚合等复杂操作
3. **性能测试** - 测试大数据量查询
4. **错误处理** - 测试错误连接和查询

## 🚨 注意事项

### 安全提醒
- ⚠️ 不要在生产环境中使用测试密码
- ⚠️ 确保数据库访问权限正确配置
- ⚠️ 测试查询时注意数据安全

### 性能提醒
- ⚠️ 大表查询时建议使用LIMIT限制结果
- ⚠️ 复杂查询可能需要较长执行时间
- ⚠️ 网络连接可能影响查询性能

## 🔍 故障排除

### 连接失败
1. **检查服务状态** - 确保数据库服务正在运行
2. **验证网络** - 确保网络连接正常
3. **检查权限** - 确保用户有连接权限
4. **查看日志** - 检查服务器控制台输出

### 查询失败
1. **语法检查** - 确保SQL语法正确
2. **权限验证** - 确保有查询权限
3. **表存在性** - 确保表/集合存在
4. **数据类型** - 检查数据类型兼容性

## 📊 测试报告

测试完成后，平台会显示：
- ✅ 连接成功率
- ✅ 查询执行时间
- ✅ 数据返回准确性
- ✅ 错误处理效果

## 🎉 成功标准

一个完整的测试应该包括：
1. ✅ 成功连接数据库
2. ✅ 获取版本和服务器信息
3. ✅ 列出所有表/集合
4. ✅ 执行基本查询
5. ✅ 获取表结构信息
6. ✅ 处理错误情况

---

**开始测试吧！** 🚀

访问 http://localhost:8000 开始您的数据源驱动测试之旅！
