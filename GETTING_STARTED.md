# 快速开始指南

## 🚀 一键启动

### Windows用户

**方法1: 使用批处理脚本**
```cmd
# 双击运行或在命令行执行
start.bat
```

**方法2: 使用PowerShell脚本**
```powershell
# 在PowerShell中执行
.\start.ps1
```

**方法3: 手动启动**
如果脚本不工作，请按照下面的手动启动步骤操作

### Linux/Mac用户
```bash
# 给脚本执行权限并运行
chmod +x start.sh
./start.sh
```

## 📋 系统要求

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 至少4GB可用内存
- **磁盘**: 至少2GB可用空间

## 🔧 手动启动步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd bi-platform
```

### 2. 配置环境变量
```bash
# 复制环境配置文件
cp backend/.env.example backend/.env

# 根据需要修改配置
# 编辑 backend/.env 文件
```

### 3. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🌐 访问地址

启动成功后，您可以通过以下地址访问：

- **前端应用**: http://localhost
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/v1/docs
- **数据库**: localhost:5432 (用户名: postgres, 密码: password)
- **Redis**: localhost:6379

## 🔑 默认登录信息

- **邮箱**: <EMAIL>
- **密码**: admin123

## 📊 示例数据

系统已预置了以下示例数据：

### 销售数据表 (sample_sales)
- 产品销售记录
- 包含产品名称、类别、销售额、日期、地区等字段
- 可用于测试自然语言查询功能

### 用户数据表 (sample_users)
- 用户基本信息
- 包含用户名、邮箱、年龄、性别、城市等字段
- 可用于用户行为分析

### 订单数据表 (sample_orders)
- 订单详细信息
- 包含订单号、用户ID、产品、数量、金额等字段
- 可用于订单分析和统计

## 🧪 测试查询示例

登录后，您可以在"自然语言分析"页面尝试以下查询：

1. **销售分析**
   - "显示上个月的销售趋势"
   - "各地区的销售额对比"
   - "电子产品的销售情况"

2. **用户分析**
   - "用户年龄分布统计"
   - "各城市的用户数量"
   - "活跃用户分析"

3. **订单分析**
   - "今天的订单量"
   - "订单状态分布"
   - "热销产品排行"

## 🛠️ 开发模式

如果您想进行开发，可以按以下步骤操作：

### 后端开发
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 前端开发
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 🔍 常见问题

### Windows用户常见问题

#### Q: 批处理脚本无法运行
A: 尝试以下解决方案：
1. 右键点击脚本，选择"以管理员身份运行"
2. 使用PowerShell脚本：`.\start.ps1`
3. 使用快速启动脚本：`quick-start.bat`
4. 按照[手动启动指南](MANUAL_START.md)手动执行命令

#### Q: PowerShell执行策略限制
A: 如果PowerShell提示执行策略限制，运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Q: Docker Desktop未启动
A: 确保Docker Desktop应用程序正在运行，系统托盘中应该有Docker图标。

### 通用问题

#### Q: 启动失败，提示端口被占用
A: 检查端口80、8000、5432、6379是否被其他程序占用，或修改docker-compose.yml中的端口映射。

#### Q: 数据库连接失败
A: 确保PostgreSQL容器正常启动，可以通过`docker-compose logs postgres`查看日志。

#### Q: 前端页面无法加载
A: 检查后端API是否正常运行，确保CORS配置正确。

#### Q: 自然语言查询没有响应
A: 这是正常的，当前版本的NLP功能是模拟实现，实际的自然语言处理需要进一步开发。

#### Q: 服务启动很慢
A: 首次启动需要下载Docker镜像，可能需要几分钟时间。后续启动会更快。

## 📚 更多信息

- [项目文档](docs/)
- [API文档](http://localhost:8000/api/v1/docs)
- [开发总结](docs/development_summary.md)
- [需求规格说明](docs/requirements_specification.md)
- [项目规范](docs/project_specification.md)

## 🆘 获取帮助

如果遇到问题，请：

1. 查看日志: `docker-compose logs -f`
2. 检查服务状态: `docker-compose ps`
3. 重启服务: `docker-compose restart`
4. 查看文档或提交Issue

## 🎉 开始探索

现在您可以开始探索这个强大的BI数据分析平台了！

1. 登录系统
2. 查看仪表板
3. 管理数据源
4. 尝试自然语言查询
5. 创建数据可视化
6. 设计数字化大屏

祝您使用愉快！
