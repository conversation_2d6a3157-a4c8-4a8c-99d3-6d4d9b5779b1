import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'

import MainLayout from './components/Layout/MainLayout'
import Login from './pages/Auth/Login'
import Register from './pages/Auth/Register'
import Dashboard from './pages/Dashboard/Dashboard'
import DataSources from './pages/DataSources/DataSources'
import Analysis from './pages/Analysis/Analysis'
import Visualization from './pages/Visualization/Visualization'
import BigScreen from './pages/BigScreen/BigScreen'
import Settings from './pages/Settings/Settings'
import { useAppSelector } from './store/hooks'

import './App.css'

const App: React.FC = () => {
  const { isAuthenticated } = useAppSelector((state) => state.auth)

  if (!isAuthenticated) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Layout>
    )
  }

  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/data-sources" element={<DataSources />} />
        <Route path="/analysis" element={<Analysis />} />
        <Route path="/visualization" element={<Visualization />} />
        <Route path="/big-screen" element={<BigScreen />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </MainLayout>
  )
}

export default App
