/**
 * 仪表板页面组件
 */

import React from 'react'
import { Row, Col, Card, Statistic, Typography, Button, Space } from 'antd'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  UserOutlined,
  DesktopOutlined,
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'

const { Title } = Typography

const Dashboard: React.FC = () => {
  // 示例数据
  const statsData = [
    {
      title: '数据源',
      value: 12,
      prefix: <DatabaseOutlined />,
      valueStyle: { color: '#3f8600' },
      suffix: <ArrowUpOutlined />,
    },
    {
      title: '分析报告',
      value: 156,
      prefix: <BarChartOutlined />,
      valueStyle: { color: '#3f8600' },
      suffix: <ArrowUpOutlined />,
    },
    {
      title: '活跃用户',
      value: 89,
      prefix: <UserOutlined />,
      valueStyle: { color: '#cf1322' },
      suffix: <ArrowDownOutlined />,
    },
    {
      title: '大屏展示',
      value: 8,
      prefix: <DesktopOutlined />,
      valueStyle: { color: '#3f8600' },
      suffix: <ArrowUpOutlined />,
    },
  ]

  // 图表配置
  const chartOption = {
    title: {
      text: '数据查询趋势',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['查询次数', '成功率'],
      top: 30,
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: [
      {
        type: 'value',
        name: '查询次数',
      },
      {
        type: 'value',
        name: '成功率(%)',
        max: 100,
      },
    ],
    series: [
      {
        name: '查询次数',
        type: 'bar',
        data: [120, 200, 150, 80, 70, 110, 130],
        itemStyle: {
          color: '#1890ff',
        },
      },
      {
        name: '成功率',
        type: 'line',
        yAxisIndex: 1,
        data: [95, 98, 92, 88, 90, 94, 96],
        itemStyle: {
          color: '#52c41a',
        },
      },
    ],
  }

  const pieOption = {
    title: {
      text: '数据源类型分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 50,
    },
    series: [
      {
        name: '数据源类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 5, name: 'MySQL' },
          { value: 3, name: 'PostgreSQL' },
          { value: 2, name: 'MongoDB' },
          { value: 1, name: 'Redis' },
          { value: 1, name: 'CSV文件' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>仪表板</Title>
        <Space>
          <Button type="primary">创建新分析</Button>
          <Button>查看报告</Button>
          <Button>数据源管理</Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {statsData.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.prefix}
                valueStyle={stat.valueStyle}
                suffix={stat.suffix}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card>
            <ReactECharts option={chartOption} style={{ height: 400 }} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card>
            <ReactECharts option={pieOption} style={{ height: 400 }} />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="最近查询" extra={<Button type="link">查看全部</Button>}>
            <div style={{ height: 200 }}>
              <p>• 上周销售数据分析 - 2小时前</p>
              <p>• 用户行为统计 - 4小时前</p>
              <p>• 产品销量对比 - 1天前</p>
              <p>• 地区分布分析 - 2天前</p>
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="系统状态" extra={<Button type="link">详细信息</Button>}>
            <div style={{ height: 200 }}>
              <p>🟢 数据库连接正常</p>
              <p>🟢 API服务运行正常</p>
              <p>🟡 缓存使用率 78%</p>
              <p>🟢 所有数据源连接正常</p>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
