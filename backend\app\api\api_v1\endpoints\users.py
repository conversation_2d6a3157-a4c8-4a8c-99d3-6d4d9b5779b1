"""
用户管理API端点

提供用户管理相关的API接口。
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.db.base import get_db
from app.schemas.user import User as UserSchema, UserUpdate
from app.services.user_service import UserService

router = APIRouter()


@router.get("/", response_model=List[UserSchema])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: UserSchema = Depends(deps.get_current_active_superuser),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取用户列表（仅超级管理员）。
    
    Args:
        skip: 跳过的记录数
        limit: 返回的记录数限制
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        List[UserSchema]: 用户列表
    """
    user_service = UserService(db)
    users = user_service.get_users(skip=skip, limit=limit)
    return users


@router.get("/{user_id}", response_model=UserSchema)
async def get_user(
    user_id: int,
    current_user: UserSchema = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    根据ID获取用户信息。
    
    Args:
        user_id: 用户ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        UserSchema: 用户信息
        
    Raises:
        HTTPException: 当用户不存在或无权限时
    """
    user_service = UserService(db)
    user = user_service.get_user(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查权限：只能查看自己的信息，除非是管理员
    if user.id != current_user.id and not deps.is_admin_or_superuser(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    return user


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: UserSchema = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    更新用户信息。
    
    Args:
        user_id: 用户ID
        user_update: 用户更新数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        UserSchema: 更新后的用户信息
        
    Raises:
        HTTPException: 当用户不存在或无权限时
    """
    user_service = UserService(db)
    user = user_service.get_user(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查权限：只能更新自己的信息，除非是管理员
    if user.id != current_user.id and not deps.is_admin_or_superuser(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    # 如果是普通用户，不允许修改角色
    if user.id == current_user.id and not deps.is_admin_or_superuser(current_user):
        user_update.role = None
    
    updated_user = user_service.update_user(user_id, user_update)
    return updated_user


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    current_user: UserSchema = Depends(deps.get_current_active_superuser),
    db: Session = Depends(get_db)
) -> Any:
    """
    删除用户（仅超级管理员）。
    
    Args:
        user_id: 用户ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        dict: 删除结果
        
    Raises:
        HTTPException: 当用户不存在时
    """
    user_service = UserService(db)
    user = user_service.get_user(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不允许删除自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )
    
    user_service.delete_user(user_id)
    return {"message": "用户删除成功"}
