@echo off
echo Quick Start BI Platform
echo ========================

REM Create environment config file
if not exist "backend\.env" (
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env" >nul
        echo Created environment config file
    )
)

echo Starting services...
docker-compose up -d --build

echo.
echo Waiting for services to start...
timeout /t 20 /nobreak >nul

echo.
echo Checking service status...
docker-compose ps

echo.
echo Startup complete!
echo Frontend: http://localhost
echo Backend: http://localhost:8000
echo API Docs: http://localhost:8000/api/v1/docs
echo.
echo Default login: <EMAIL> / admin123
echo.
pause
