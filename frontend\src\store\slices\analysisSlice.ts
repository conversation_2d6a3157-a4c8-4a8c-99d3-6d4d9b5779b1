/**
 * 分析状态管理
 */

import { createSlice } from '@reduxjs/toolkit'

interface AnalysisState {
  queries: any[]
  currentQuery: any | null
  results: any[]
  loading: boolean
  error: string | null
}

const initialState: AnalysisState = {
  queries: [],
  currentQuery: null,
  results: [],
  loading: false,
  error: null,
}

const analysisSlice = createSlice({
  name: 'analysis',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
})

export const { clearError } = analysisSlice.actions
export default analysisSlice.reducer
