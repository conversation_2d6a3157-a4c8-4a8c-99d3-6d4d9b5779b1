// 数据源驱动测试平台 JavaScript 函数

// 更新连接表单
async function updateConnectionForm() {
    const type = document.getElementById('dsType').value;
    const formDiv = document.getElementById('connectionForm');
    
    if (!type) {
        formDiv.innerHTML = '<p class="info">请先选择数据库类型</p>';
        return;
    }

    try {
        const response = await fetch(`/api/v1/data-sources/types/${type}/requirements`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        const requirements = await response.json();

        let html = '<div class="driver-info">';
        html += `<strong>驱动:</strong> ${requirements.driver}<br>`;
        html += `<strong>默认端口:</strong> ${requirements.default_port}<br>`;
        html += `<strong>SSL支持:</strong> ${requirements.supports_ssl ? '是' : '否'}<br>`;
        if (requirements.features) {
            html += `<strong>特性:</strong> ${requirements.features.join(', ')}`;
        }
        html += '</div>';

        // 必需字段
        requirements.required_fields.forEach(field => {
            const label = getFieldLabel(field);
            const defaultValue = getDefaultValue(field, type);
            const inputType = field === 'password' ? 'password' : 'text';
            
            html += `
                <div class="form-group">
                    <label>${label} *:</label>
                    <input type="${inputType}" id="ds${capitalize(field)}" value="${defaultValue}" />
                </div>
            `;
        });

        // 可选字段
        if (requirements.optional_fields) {
            requirements.optional_fields.forEach(field => {
                const label = getFieldLabel(field);
                const inputType = field === 'password' ? 'password' : 'text';
                
                html += `
                    <div class="form-group">
                        <label>${label}:</label>
                        <input type="${inputType}" id="ds${capitalize(field)}" />
                    </div>
                `;
            });
        }

        formDiv.innerHTML = html;
    } catch (error) {
        formDiv.innerHTML = '<p class="error">加载连接要求失败</p>';
    }
}

// 获取字段标签
function getFieldLabel(field) {
    const labels = {
        'host': '主机地址',
        'port': '端口',
        'database': '数据库',
        'username': '用户名',
        'password': '密码',
        'auth_source': '认证数据库',
        'charset': '字符集',
        'timeout': '超时时间'
    };
    return labels[field] || field;
}

// 获取默认值
function getDefaultValue(field, type) {
    if (field === 'host') return 'localhost';
    if (field === 'port') {
        const ports = {
            'postgresql': 5432,
            'mysql': 3306,
            'mssql': 1433,
            'mongodb': 27017,
            'redis': 6379,
            'sqlite': 0
        };
        return ports[type] || '';
    }
    if (field === 'database') {
        if (type === 'sqlite') return ':memory:';
        if (type === 'postgresql') return 'postgres';
        if (type === 'mysql') return 'mysql';
        if (type === 'mssql') return 'master';
        return 'test';
    }
    if (field === 'username') {
        if (type === 'postgresql') return 'postgres';
        if (type === 'mysql') return 'root';
        if (type === 'mssql') return 'sa';
        return 'admin';
    }
    return '';
}

// 首字母大写
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

// 创建数据源
async function createDataSource() {
    const name = document.getElementById('dsName').value;
    const description = document.getElementById('dsDescription').value;
    const type = document.getElementById('dsType').value;

    if (!name || !type) {
        log('❌ 请填写数据源名称和类型', 'createResult');
        return;
    }

    // 收集连接参数
    const data = { name, description, type };
    
    // 根据类型收集不同的字段
    const fields = ['host', 'port', 'database', 'username', 'password'];
    fields.forEach(field => {
        const element = document.getElementById(`ds${capitalize(field)}`);
        if (element && element.value) {
            data[field] = field === 'port' ? parseInt(element.value) : element.value;
        }
    });

    try {
        const response = await fetch('/api/v1/data-sources/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();
        if (response.ok) {
            log(`✅ 创建成功: ${result.name} (ID: ${result.id}, 类型: ${result.type})`, 'createResult');
            // 清空表单
            document.getElementById('dsName').value = '';
            document.getElementById('dsDescription').value = '';
        } else {
            log(`❌ 创建失败: ${result.detail}`, 'createResult');
        }
    } catch (error) {
        log('❌ 创建请求失败: ' + error.message, 'createResult');
    }
}

// 加载数据源列表
async function loadDataSources() {
    try {
        const response = await fetch('/api/v1/data-sources/', {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        const result = await response.json();
        if (response.ok) {
            displayDataSources(result.items);
        } else {
            log(`❌ 加载失败: ${result.detail}`, 'createResult');
        }
    } catch (error) {
        log('❌ 加载请求失败: ' + error.message, 'createResult');
    }
}

// 显示数据源列表
function displayDataSources(dataSources) {
    const container = document.getElementById('dataSourceList');
    if (dataSources.length === 0) {
        container.innerHTML = '<p class="info">暂无数据源</p>';
        return;
    }

    let html = '';
    dataSources.forEach(ds => {
        const statusClass = `status-${ds.status}`;
        html += `
            <div class="data-source-item">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h4>${ds.name} <span class="feature-tag">${ds.type}</span></h4>
                        <p>${ds.description || '无描述'}</p>
                        <small>创建时间: ${new Date(ds.created_at).toLocaleString()}</small>
                    </div>
                    <div>
                        <span class="${statusClass}">● ${ds.status}</span><br>
                        <button class="btn" onclick="testDataSourceConnection(${ds.id})">测试连接</button>
                        <button class="btn" onclick="loadTables(${ds.id})">查看表</button>
                        <button class="btn btn-danger" onclick="deleteDataSource(${ds.id})">删除</button>
                    </div>
                </div>
                <div id="tables-${ds.id}" style="margin-top: 15px;"></div>
            </div>
        `;
    });
    container.innerHTML = html;
}

// 测试数据源连接
async function testDataSourceConnection(id) {
    try {
        const response = await fetch(`/api/v1/data-sources/${id}/test`, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${token}` }
        });

        const result = await response.json();
        if (result.success) {
            log(`✅ 数据源 ${id} 连接成功: ${result.message}`, 'createResult');
        } else {
            log(`❌ 数据源 ${id} 连接失败: ${result.message}`, 'createResult');
        }
    } catch (error) {
        log(`❌ 连接测试失败: ${error.message}`, 'createResult');
    }
}

// 加载表列表
async function loadTables(dsId) {
    try {
        const response = await fetch(`/api/v1/data-sources/${dsId}/tables`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        const result = await response.json();
        if (response.ok) {
            displayTables(dsId, result.tables);
        } else {
            log(`❌ 加载表列表失败: ${result.detail}`, 'createResult');
        }
    } catch (error) {
        log(`❌ 加载表列表失败: ${error.message}`, 'createResult');
    }
}

// 显示表列表
function displayTables(dsId, tables) {
    const container = document.getElementById(`tables-${dsId}`);
    
    if (tables.length === 0) {
        container.innerHTML = '<p class="info">暂无表信息</p>';
        return;
    }

    let html = '<h5>📊 表列表:</h5><div class="table-list">';
    tables.forEach(table => {
        const tableName = table.table_name || table.collection_name || table.key_pattern || table;
        html += `<div class="table-item" onclick="showTableInfo('${tableName}', ${dsId})">${tableName}</div>`;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

// 删除数据源
async function deleteDataSource(id) {
    if (!confirm(`确定要删除数据源 ID: ${id} 吗？`)) return;

    try {
        const response = await fetch(`/api/v1/data-sources/${id}`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${token}` }
        });

        const result = await response.json();
        if (response.ok) {
            log(`✅ ${result.message}`, 'createResult');
            loadDataSources();
        } else {
            log(`❌ 删除失败: ${result.detail}`, 'createResult');
        }
    } catch (error) {
        log(`❌ 删除请求失败: ${error.message}`, 'createResult');
    }
}

// 更新测试表单
function updateTestForm() {
    const type = document.getElementById('testType').value;
    const formDiv = document.getElementById('testForm');
    
    const configs = {
        'postgresql': { host: 'localhost', port: 5432, database: 'postgres', username: 'postgres', password: 'password' },
        'mysql': { host: 'localhost', port: 3306, database: 'mysql', username: 'root', password: 'password' },
        'mssql': { host: 'localhost', port: 1433, database: 'master', username: 'sa', password: 'Password123!' },
        'mongodb': { host: 'localhost', port: 27017, database: 'test', username: 'admin', password: 'password' },
        'redis': { host: 'localhost', port: 6379, password: 'password' },
        'sqlite': { database: ':memory:' }
    };
    
    const config = configs[type];
    let html = '';
    
    Object.keys(config).forEach(key => {
        const inputType = key === 'password' ? 'password' : (key === 'port' ? 'number' : 'text');
        html += `
            <div class="form-group">
                <label>${getFieldLabel(key)}:</label>
                <input type="${inputType}" id="test${capitalize(key)}" value="${config[key]}" />
            </div>
        `;
    });
    
    formDiv.innerHTML = html;
}

// 测试连接
async function testConnection() {
    const type = document.getElementById('testType').value;
    
    // 收集测试数据
    const data = { type };
    const fields = ['host', 'port', 'database', 'username', 'password'];
    
    fields.forEach(field => {
        const element = document.getElementById(`test${capitalize(field)}`);
        if (element && element.value) {
            data[field] = field === 'port' ? parseInt(element.value) : element.value;
        }
    });

    try {
        const response = await fetch('/api/v1/data-sources/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();
        const resultDiv = document.getElementById('testResult');
        
        if (result.success) {
            let html = `<span class="success">✅ ${result.message}</span><br>`;
            if (result.details) {
                html += '<strong>连接详情:</strong><br>';
                Object.keys(result.details).forEach(key => {
                    html += `${key}: ${result.details[key]}<br>`;
                });
            }
            resultDiv.innerHTML = html;
        } else {
            resultDiv.innerHTML = `<span class="error">❌ ${result.message}</span>`;
        }
    } catch (error) {
        document.getElementById('testResult').innerHTML = 
            `<span class="error">❌ 请求失败: ${error.message}</span>`;
    }
}
