@echo off
echo BI Platform Troubleshooting
echo ============================

echo.
echo 1. Checking Docker status...
docker info >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not running
    echo Please start Docker Desktop and try again
    goto :end
) else (
    echo OK: Docker is running
)

echo.
echo 2. Checking Docker Compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker Compose not found
    goto :end
) else (
    echo OK: Docker Compose is available
)

echo.
echo 3. Checking project files...
if not exist "docker-compose.yml" (
    echo ERROR: docker-compose.yml not found
    echo Make sure you are in the correct directory
    goto :end
) else (
    echo OK: docker-compose.yml found
)

echo.
echo 4. Checking current services...
docker-compose ps

echo.
echo 5. Checking port usage...
netstat -an | findstr ":80 "
netstat -an | findstr ":8000 "
netstat -an | findstr ":5432 "
netstat -an | findstr ":6379 "

echo.
echo 6. Checking Docker containers...
docker ps -a

echo.
echo 7. Checking Docker images...
docker images | findstr "test"

echo.
echo Troubleshooting complete!
echo.
echo Common solutions:
echo - If ports are in use: docker-compose down
echo - If containers are stuck: docker-compose down -v
echo - If images are corrupted: docker-compose build --no-cache
echo - If all else fails: docker system prune -f
echo.

:end
pause
