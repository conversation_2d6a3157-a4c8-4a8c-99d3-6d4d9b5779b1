#!/usr/bin/env python3
"""
数据源功能测试脚本

测试数据源管理功能是否正常工作。
"""

import requests
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8000/api/v1"
TEST_USER = {
    "username": "<EMAIL>",
    "password": "admin123"
}

class DataSourceTester:
    def __init__(self):
        self.token = None
        self.session = requests.Session()
    
    def login(self):
        """登录获取token"""
        print("🔑 正在登录...")
        
        response = self.session.post(
            f"{BASE_URL}/auth/login",
            data={
                "username": TEST_USER["username"],
                "password": TEST_USER["password"]
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            self.token = data["access_token"]
            self.session.headers.update({
                "Authorization": f"Bearer {self.token}"
            })
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.text}")
            return False
    
    def test_create_data_source(self):
        """测试创建数据源"""
        print("\n📊 测试创建数据源...")
        
        test_data_source = {
            "name": "测试PostgreSQL数据源",
            "description": "用于测试的PostgreSQL数据库",
            "type": "postgresql",
            "host": "localhost",
            "port": 5432,
            "database": "test_db",
            "username": "test_user",
            "password": "test_password",
            "config": {
                "ssl_mode": "prefer",
                "timeout": 30
            },
            "is_active": True
        }
        
        response = self.session.post(
            f"{BASE_URL}/data-sources/",
            json=test_data_source
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 数据源创建成功: ID={data['id']}, Name={data['name']}")
            return data["id"]
        else:
            print(f"❌ 数据源创建失败: {response.text}")
            return None
    
    def test_get_data_sources(self):
        """测试获取数据源列表"""
        print("\n📋 测试获取数据源列表...")
        
        response = self.session.get(f"{BASE_URL}/data-sources/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取数据源列表成功: 共{data['total']}个数据源")
            for item in data["items"]:
                print(f"   - {item['name']} ({item['type']}) - {item['status']}")
            return data["items"]
        else:
            print(f"❌ 获取数据源列表失败: {response.text}")
            return []
    
    def test_get_data_source_detail(self, data_source_id):
        """测试获取数据源详情"""
        print(f"\n🔍 测试获取数据源详情 (ID: {data_source_id})...")
        
        response = self.session.get(f"{BASE_URL}/data-sources/{data_source_id}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取数据源详情成功: {data['name']}")
            print(f"   类型: {data['type']}")
            print(f"   状态: {data['status']}")
            print(f"   主机: {data['host']}:{data['port']}")
            return data
        else:
            print(f"❌ 获取数据源详情失败: {response.text}")
            return None
    
    def test_connection(self):
        """测试数据源连接"""
        print("\n🔗 测试数据源连接...")
        
        test_connection_data = {
            "type": "sqlite",
            "host": "",
            "port": 0,
            "database": ":memory:",
            "username": "",
            "password": "",
            "config": {}
        }
        
        response = self.session.post(
            f"{BASE_URL}/data-sources/test",
            json=test_connection_data
        )
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print(f"✅ 连接测试成功: {data['message']}")
            else:
                print(f"⚠️ 连接测试失败: {data['message']}")
            return data["success"]
        else:
            print(f"❌ 连接测试请求失败: {response.text}")
            return False
    
    def test_update_data_source(self, data_source_id):
        """测试更新数据源"""
        print(f"\n✏️ 测试更新数据源 (ID: {data_source_id})...")
        
        update_data = {
            "description": "更新后的描述 - " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "port": 5433
        }
        
        response = self.session.put(
            f"{BASE_URL}/data-sources/{data_source_id}",
            json=update_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 数据源更新成功: {data['description']}")
            return True
        else:
            print(f"❌ 数据源更新失败: {response.text}")
            return False
    
    def test_query_execution(self, data_source_id):
        """测试查询执行"""
        print(f"\n🔍 测试查询执行 (ID: {data_source_id})...")
        
        query_data = {
            "data_source_id": data_source_id,
            "query": "SELECT 1 as test_column, 'Hello World' as message",
            "limit": 10
        }
        
        response = self.session.post(
            f"{BASE_URL}/data-sources/query",
            json=query_data
        )
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print(f"✅ 查询执行成功: 返回{data['total']}条记录")
                print(f"   列名: {data['columns']}")
                print(f"   执行时间: {data['execution_time']:.3f}秒")
                if data["data"]:
                    print(f"   示例数据: {data['data'][0]}")
            else:
                print(f"⚠️ 查询执行失败: {data['message']}")
            return data["success"]
        else:
            print(f"❌ 查询请求失败: {response.text}")
            return False
    
    def test_delete_data_source(self, data_source_id):
        """测试删除数据源"""
        print(f"\n🗑️ 测试删除数据源 (ID: {data_source_id})...")
        
        response = self.session.delete(f"{BASE_URL}/data-sources/{data_source_id}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 数据源删除成功: {data['message']}")
            return True
        else:
            print(f"❌ 数据源删除失败: {response.text}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始数据源功能测试")
        print("=" * 60)
        
        # 登录
        if not self.login():
            return False
        
        # 测试创建数据源
        data_source_id = self.test_create_data_source()
        if not data_source_id:
            return False
        
        # 测试获取数据源列表
        self.test_get_data_sources()
        
        # 测试获取数据源详情
        self.test_get_data_source_detail(data_source_id)
        
        # 测试连接
        self.test_connection()
        
        # 测试更新数据源
        self.test_update_data_source(data_source_id)
        
        # 测试查询执行
        self.test_query_execution(data_source_id)
        
        # 测试删除数据源
        self.test_delete_data_source(data_source_id)
        
        print("\n" + "=" * 60)
        print("🎉 数据源功能测试完成！")
        return True

def main():
    """主函数"""
    tester = DataSourceTester()
    
    try:
        success = tester.run_all_tests()
        if success:
            print("✅ 所有测试通过")
        else:
            print("❌ 部分测试失败")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        print("   启动命令: python backend/app/main.py")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
