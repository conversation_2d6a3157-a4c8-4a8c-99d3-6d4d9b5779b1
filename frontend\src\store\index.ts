/**
 * Redux Store 配置
 * 
 * 配置应用的全局状态管理
 */

import { configureStore } from '@reduxjs/toolkit'
import authReducer from './slices/authSlice'
import userReducer from './slices/userSlice'
import dataSourceReducer from './slices/dataSourceSlice'
import analysisReducer from './slices/analysisSlice'
import visualizationReducer from './slices/visualizationSlice'
import bigScreenReducer from './slices/bigScreenSlice'

export const store = configureStore({
  reducer: {
    auth: authReducer,
    user: userReducer,
    dataSource: dataSourceReducer,
    analysis: analysisReducer,
    visualization: visualizationReducer,
    bigScreen: bigScreenReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
