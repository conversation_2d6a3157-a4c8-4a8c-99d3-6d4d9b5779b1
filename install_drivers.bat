@echo off
chcp 65001 >nul
echo ============================================================
echo 🗄️ 数据库驱动一键安装器
echo ============================================================
echo.

echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 🔧 升级pip...
python -m pip install --upgrade pip

echo.
echo 📦 安装核心数据库驱动...
echo.

echo 🐘 安装PostgreSQL驱动...
python -m pip install psycopg2-binary
if errorlevel 1 (
    echo ⚠️ PostgreSQL驱动安装失败，继续安装其他驱动...
) else (
    echo ✅ PostgreSQL驱动安装成功
)

echo.
echo 🐬 安装MySQL驱动...
python -m pip install pymysql
if errorlevel 1 (
    echo ⚠️ MySQL驱动安装失败，继续安装其他驱动...
) else (
    echo ✅ MySQL驱动安装成功
)

echo.
echo 🍃 安装MongoDB驱动...
python -m pip install pymongo
if errorlevel 1 (
    echo ⚠️ MongoDB驱动安装失败，继续安装其他驱动...
) else (
    echo ✅ MongoDB驱动安装成功
)

echo.
echo 🔴 安装Redis驱动...
python -m pip install redis
if errorlevel 1 (
    echo ⚠️ Redis驱动安装失败，继续安装其他驱动...
) else (
    echo ✅ Redis驱动安装成功
)

echo.
echo 🔧 安装可选驱动...
echo.

echo 🏢 安装SQL Server驱动...
python -m pip install pyodbc
if errorlevel 1 (
    echo ⚠️ SQL Server驱动安装失败 (可能需要ODBC驱动)
) else (
    echo ✅ SQL Server驱动安装成功
)

echo.
echo 🔍 安装Elasticsearch驱动...
python -m pip install elasticsearch
if errorlevel 1 (
    echo ⚠️ Elasticsearch驱动安装失败
) else (
    echo ✅ Elasticsearch驱动安装成功
)

echo.
echo 🦆 安装DuckDB驱动...
python -m pip install duckdb
if errorlevel 1 (
    echo ⚠️ DuckDB驱动安装失败
) else (
    echo ✅ DuckDB驱动安装成功
)

echo.
echo ============================================================
echo 🧪 测试驱动安装
echo ============================================================

echo.
echo 📁 测试SQLite (内置)...
python -c "import sqlite3; print('✅ SQLite 可用')" 2>nul || echo "❌ SQLite 不可用"

echo.
echo 🐘 测试PostgreSQL驱动...
python -c "import psycopg2; print('✅ PostgreSQL驱动 可用')" 2>nul || echo "❌ PostgreSQL驱动 不可用"

echo.
echo 🐬 测试MySQL驱动...
python -c "import pymysql; print('✅ MySQL驱动 可用')" 2>nul || echo "❌ MySQL驱动 不可用"

echo.
echo 🍃 测试MongoDB驱动...
python -c "import pymongo; print('✅ MongoDB驱动 可用')" 2>nul || echo "❌ MongoDB驱动 不可用"

echo.
echo 🔴 测试Redis驱动...
python -c "import redis; print('✅ Redis驱动 可用')" 2>nul || echo "❌ Redis驱动 不可用"

echo.
echo ============================================================
echo 📊 安装完成
echo ============================================================
echo.
echo 🎉 数据库驱动安装完成！
echo.
echo 🚀 下一步:
echo   1. 启动真实连接测试服务器: python real_connection_server.py
echo   2. 打开高级数据源管理平台进行测试
echo   3. 尝试创建和测试数据源连接
echo.
echo 💡 提示:
echo   - SQLite 立即可用，无需额外配置
echo   - 其他数据库需要相应的服务器运行
echo   - 如有安装失败，请查看错误信息或手动安装
echo.

pause
