<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源管理测试指导</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px; color: #333;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            background: white; border-radius: 16px; padding: 30px; margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center;
        }
        .card { 
            background: white; border-radius: 16px; padding: 30px; margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .btn { 
            background: #667eea; color: white; border: none; padding: 12px 24px;
            border-radius: 10px; cursor: pointer; margin: 8px; font-size: 14px;
            font-weight: 600; transition: all 0.3s ease; text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
        .btn-success { background: #10b981; }
        .btn-danger { background: #ef4444; }
        .btn-warning { background: #f59e0b; }
        .test-step { 
            background: #f8fafc; border: 2px solid #e5e7eb; border-radius: 12px; 
            padding: 20px; margin: 15px 0; position: relative;
        }
        .test-step.success { border-color: #10b981; background: #f0fdf4; }
        .test-step.error { border-color: #ef4444; background: #fef2f2; }
        .test-step.warning { border-color: #f59e0b; background: #fffbeb; }
        .step-number { 
            position: absolute; top: -12px; left: 20px; background: #667eea; 
            color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;
        }
        .status { padding: 15px; border-radius: 8px; margin: 15px 0; }
        .status.info { background: #dbeafe; border: 1px solid #93c5fd; color: #1e40af; }
        .status.success { background: #dcfce7; border: 1px solid #86efac; color: #166534; }
        .status.error { background: #fee2e2; border: 1px solid #fca5a5; color: #991b1b; }
        .status.warning { background: #fef3c7; border: 1px solid #fcd34d; color: #92400e; }
        code { background: #f1f5f9; padding: 2px 6px; border-radius: 4px; font-family: monospace; }
        .highlight { background: #fef3c7; padding: 2px 6px; border-radius: 4px; font-weight: 600; }
        .checklist { list-style: none; padding: 0; }
        .checklist li { padding: 8px 0; display: flex; align-items: center; }
        .checklist li:before { content: "☐"; margin-right: 10px; font-size: 16px; }
        .checklist li.checked:before { content: "✅"; }
        .test-data { 
            background: #1f2937; color: #e5e7eb; padding: 15px; border-radius: 8px; 
            font-family: monospace; font-size: 13px; margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 数据源管理测试指导</h1>
            <p>完整的测试流程和验证步骤</p>
            <div style="margin-top: 15px;">
                <a href="file:///C:/Users/<USER>/test/test/advanced_datasource_manager.html" class="btn btn-success">
                    🚀 打开数据源管理平台
                </a>
                <a href="file:///C:/Users/<USER>/test/test/real_connection_test_demo.html" class="btn btn-warning">
                    🔍 打开连接测试演示
                </a>
            </div>
        </div>

        <!-- 测试前准备 -->
        <div class="card">
            <h3>📋 测试前准备检查</h3>
            <div class="status info">
                <strong>请确保以下条件满足后再开始测试</strong>
            </div>
            
            <ul class="checklist">
                <li id="check-server">真实连接测试服务器已启动 (python real_connection_server.py)</li>
                <li id="check-drivers">数据库驱动已安装 (python check_database_drivers.py)</li>
                <li id="check-sqlite">SQLite测试数据库文件存在</li>
                <li id="check-browser">浏览器已打开数据源管理平台</li>
            </ul>
            
            <button class="btn" onclick="checkPrerequisites()">🔍 检查准备条件</button>
        </div>

        <!-- 测试步骤 -->
        <div class="grid">
            <!-- 测试1: SQLite成功连接 -->
            <div class="card">
                <h3>✅ 测试1: SQLite成功连接</h3>
                <div class="test-step">
                    <div class="step-number">1</div>
                    <h4>创建SQLite数据源</h4>
                    <p><strong>目标:</strong> 验证正确配置能够成功连接</p>
                    
                    <div class="test-data">
数据源名称: 测试SQLite数据库
描述: 本地SQLite测试数据库
数据库类型: SQLite
数据库路径: C:\Users\<USER>\test\test\test_database.db
                    </div>
                    
                    <p><strong>操作步骤:</strong></p>
                    <ol>
                        <li>点击"➕ 创建数据源"</li>
                        <li>填写上述信息</li>
                        <li>点击"🔍 测试连接"</li>
                        <li>等待测试结果</li>
                        <li>点击"💾 保存数据源"</li>
                    </ol>
                    
                    <p><strong>预期结果:</strong></p>
                    <ul>
                        <li>✅ 连接测试成功</li>
                        <li>✅ 显示SQLite版本信息</li>
                        <li>✅ 显示表数量</li>
                        <li>✅ 保存按钮启用</li>
                        <li>✅ 数据源创建成功</li>
                    </ul>
                </div>
            </div>

            <!-- 测试2: 错误连接检测 -->
            <div class="card">
                <h3>❌ 测试2: 错误连接检测</h3>
                <div class="test-step">
                    <div class="step-number">2</div>
                    <h4>测试错误文件路径</h4>
                    <p><strong>目标:</strong> 验证错误配置能够被准确检测</p>
                    
                    <div class="test-data">
数据源名称: 错误SQLite测试
描述: 测试错误检测功能
数据库类型: SQLite
数据库路径: C:\不存在的路径\错误文件.db
                    </div>
                    
                    <p><strong>预期结果:</strong></p>
                    <ul>
                        <li>❌ 连接测试失败</li>
                        <li>❌ 显示"文件不存在"错误</li>
                        <li>❌ 提供解决建议</li>
                        <li>❌ 保存按钮禁用</li>
                    </ul>
                </div>
            </div>

            <!-- 测试3: PostgreSQL连接 -->
            <div class="card">
                <h3>🐘 测试3: PostgreSQL连接</h3>
                <div class="test-step">
                    <div class="step-number">3</div>
                    <h4>测试PostgreSQL连接</h4>
                    <p><strong>目标:</strong> 验证网络数据库连接检测</p>
                    
                    <div class="test-data">
数据源名称: 测试PostgreSQL
数据库类型: PostgreSQL
主机地址: localhost
端口: 5432
数据库: postgres
用户名: postgres
密码: password
                    </div>
                    
                    <p><strong>预期结果 (无PostgreSQL服务器):</strong></p>
                    <ul>
                        <li>❌ 网络连接失败</li>
                        <li>❌ 显示"无法连接到 localhost:5432"</li>
                        <li>❌ 提供网络检查建议</li>
                    </ul>
                    
                    <p><strong>预期结果 (有PostgreSQL服务器):</strong></p>
                    <ul>
                        <li>✅ 或 ❌ 根据实际认证情况</li>
                        <li>✅ 显示具体的认证错误或连接成功</li>
                    </ul>
                </div>
            </div>

            <!-- 测试4: 错误主机测试 -->
            <div class="card">
                <h3>🌐 测试4: 网络错误检测</h3>
                <div class="test-step">
                    <div class="step-number">4</div>
                    <h4>测试不存在的主机</h4>
                    <p><strong>目标:</strong> 验证网络连接检测能力</p>
                    
                    <div class="test-data">
数据源名称: 错误网络测试
数据库类型: MySQL
主机地址: nonexistent.server.com
端口: 3306
数据库: test
用户名: test
密码: test
                    </div>
                    
                    <p><strong>预期结果:</strong></p>
                    <ul>
                        <li>❌ 网络连接失败</li>
                        <li>❌ 显示主机无法访问错误</li>
                        <li>❌ 提供网络检查建议</li>
                    </ul>
                </div>
            </div>

            <!-- 测试5: 服务器离线测试 -->
            <div class="card">
                <h3>🔌 测试5: 服务器状态检测</h3>
                <div class="test-step">
                    <div class="step-number">5</div>
                    <h4>测试服务器离线情况</h4>
                    <p><strong>目标:</strong> 验证服务器连接状态检测</p>
                    
                    <p><strong>操作:</strong></p>
                    <ol>
                        <li>停止真实连接测试服务器</li>
                        <li>尝试测试任何数据源连接</li>
                        <li>观察错误提示</li>
                        <li>重新启动服务器</li>
                    </ol>
                    
                    <p><strong>预期结果 (服务器离线):</strong></p>
                    <ul>
                        <li>❌ 显示"无法连接到真实连接测试服务器"</li>
                        <li>❌ 提供启动服务器的解决方案</li>
                        <li>❌ 显示具体的启动命令</li>
                    </ul>
                </div>
            </div>

            <!-- 测试6: 界面功能测试 -->
            <div class="card">
                <h3>🎨 测试6: 界面功能验证</h3>
                <div class="test-step">
                    <div class="step-number">6</div>
                    <h4>测试界面交互功能</h4>
                    <p><strong>目标:</strong> 验证用户界面功能</p>
                    
                    <p><strong>测试项目:</strong></p>
                    <ul>
                        <li>字段验证 (必填、格式检查)</li>
                        <li>按钮状态变化</li>
                        <li>进度条显示</li>
                        <li>错误提示样式</li>
                        <li>成功提示样式</li>
                        <li>数据源列表显示</li>
                        <li>编辑功能</li>
                        <li>删除功能</li>
                        <li>批量测试功能</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 测试结果记录 -->
        <div class="card">
            <h3>📊 测试结果记录</h3>
            <div id="testResults">
                <div class="status info">
                    <strong>测试结果将在这里显示</strong><br>
                    请按照上述步骤进行测试，并记录每个测试的结果。
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-success" onclick="recordTestResult('success')">✅ 记录成功</button>
                <button class="btn btn-danger" onclick="recordTestResult('failure')">❌ 记录失败</button>
                <button class="btn btn-warning" onclick="recordTestResult('partial')">⚠️ 记录部分成功</button>
                <button class="btn" onclick="clearTestResults()">🔄 清空记录</button>
            </div>
        </div>

        <!-- 故障排除 -->
        <div class="card">
            <h3>🔧 故障排除指南</h3>
            
            <div class="test-step error">
                <h4>❌ 如果连接测试服务器无法启动</h4>
                <ol>
                    <li>检查端口8080是否被占用</li>
                    <li>检查Python环境是否正常</li>
                    <li>尝试更换端口号</li>
                    <li>检查防火墙设置</li>
                </ol>
            </div>
            
            <div class="test-step warning">
                <h4>⚠️ 如果数据库驱动缺失</h4>
                <ol>
                    <li>运行: <code>python install_core_drivers.py</code></li>
                    <li>或手动安装: <code>pip install psycopg2-binary pymysql pymongo redis</code></li>
                    <li>检查安装状态: <code>python check_database_drivers.py</code></li>
                </ol>
            </div>
            
            <div class="test-step success">
                <h4>✅ 如果一切正常</h4>
                <p>恭喜！您的数据源管理系统已经具备完整的真实连接验证能力。</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        
        function checkPrerequisites() {
            // 模拟检查准备条件
            const checks = [
                { id: 'check-server', name: '服务器状态', url: 'http://localhost:8080' },
                { id: 'check-drivers', name: '驱动状态', check: () => true },
                { id: 'check-sqlite', name: 'SQLite文件', check: () => true },
                { id: 'check-browser', name: '浏览器状态', check: () => true }
            ];
            
            checks.forEach(async (check, index) => {
                setTimeout(async () => {
                    const element = document.getElementById(check.id);
                    
                    if (check.url) {
                        try {
                            const response = await fetch(check.url, { mode: 'no-cors' });
                            element.classList.add('checked');
                        } catch (error) {
                            // 服务器可能未启动
                            console.log(`${check.name} 检查失败:`, error);
                        }
                    } else {
                        element.classList.add('checked');
                    }
                }, index * 500);
            });
        }
        
        function recordTestResult(type) {
            const timestamp = new Date().toLocaleTimeString();
            const result = {
                time: timestamp,
                type: type,
                message: getResultMessage(type)
            };
            
            testResults.push(result);
            updateTestResultsDisplay();
        }
        
        function getResultMessage(type) {
            const messages = {
                'success': '✅ 测试通过 - 功能正常工作',
                'failure': '❌ 测试失败 - 发现问题需要修复',
                'partial': '⚠️ 部分成功 - 某些功能需要改进'
            };
            return messages[type] || '📝 测试记录';
        }
        
        function updateTestResultsDisplay() {
            const container = document.getElementById('testResults');
            
            if (testResults.length === 0) {
                container.innerHTML = '<div class="status info"><strong>暂无测试记录</strong></div>';
                return;
            }
            
            let html = '<h4>测试记录:</h4>';
            testResults.forEach((result, index) => {
                const statusClass = result.type === 'success' ? 'success' : 
                                  result.type === 'failure' ? 'error' : 'warning';
                html += `
                    <div class="status ${statusClass}">
                        <strong>[${result.time}]</strong> ${result.message}
                    </div>
                `;
            });
            
            // 添加统计
            const successCount = testResults.filter(r => r.type === 'success').length;
            const failureCount = testResults.filter(r => r.type === 'failure').length;
            const partialCount = testResults.filter(r => r.type === 'partial').length;
            
            html += `
                <div class="status info">
                    <strong>测试统计:</strong> 
                    成功: ${successCount} | 失败: ${failureCount} | 部分: ${partialCount} | 总计: ${testResults.length}
                </div>
            `;
            
            container.innerHTML = html;
        }
        
        function clearTestResults() {
            testResults = [];
            updateTestResultsDisplay();
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            setTimeout(checkPrerequisites, 1000);
        };
    </script>
</body>
</html>
