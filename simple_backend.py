"""
最简单的后端测试服务器
只需要Python标准库，无需额外依赖
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime

class SimpleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>BI数据分析平台 - 简化版</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; }
                    .success { background-color: #f0f9ff; border-color: #0ea5e9; }
                    .info { background-color: #f8fafc; border-color: #64748b; }
                    button { background: #0ea5e9; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
                    button:hover { background: #0284c7; }
                    .data { background: #f1f5f9; padding: 10px; border-radius: 4px; margin: 10px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🚀 BI数据分析平台 - 简化版</h1>
                    
                    <div class="card success">
                        <h2>✅ 系统状态</h2>
                        <p>后端服务运行正常</p>
                        <p>时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
                        <p>数据库: 内存模拟</p>
                    </div>
                    
                    <div class="card info">
                        <h2>📊 示例数据</h2>
                        <button onclick="loadData()">加载销售数据</button>
                        <div id="data-container"></div>
                    </div>
                    
                    <div class="card info">
                        <h2>🔑 模拟登录</h2>
                        <p>邮箱: <EMAIL></p>
                        <p>密码: admin123</p>
                        <button onclick="testLogin()">测试登录</button>
                        <div id="login-result"></div>
                    </div>
                    
                    <div class="card info">
                        <h2>🔗 API接口</h2>
                        <ul>
                            <li><a href="/api/health">/api/health</a> - 健康检查</li>
                            <li><a href="/api/data">/api/data</a> - 获取数据</li>
                            <li>/api/login - 登录接口 (POST)</li>
                        </ul>
                    </div>
                </div>
                
                <script>
                    function loadData() {
                        fetch('/api/data')
                            .then(response => response.json())
                            .then(data => {
                                document.getElementById('data-container').innerHTML = 
                                    '<div class="data"><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                            })
                            .catch(error => {
                                document.getElementById('data-container').innerHTML = 
                                    '<div class="data">错误: ' + error + '</div>';
                            });
                    }
                    
                    function testLogin() {
                        fetch('/api/login', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({username: '<EMAIL>', password: 'admin123'})
                        })
                        .then(response => response.json())
                        .then(data => {
                            document.getElementById('login-result').innerHTML = 
                                '<div class="data"><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                        })
                        .catch(error => {
                            document.getElementById('login-result').innerHTML = 
                                '<div class="data">错误: ' + error + '</div>';
                        });
                    }
                </script>
            </body>
            </html>
            """
            self.wfile.write(html.encode('utf-8'))
            
        elif self.path == '/api/health':
            self.send_json_response({
                "status": "healthy",
                "message": "简化版BI平台运行正常",
                "timestamp": datetime.now().isoformat()
            })
            
        elif self.path == '/api/data':
            # 模拟销售数据
            sample_data = [
                {"product": "iPhone 15", "sales": 8999, "region": "北京"},
                {"product": "MacBook Pro", "sales": 15999, "region": "上海"},
                {"product": "AirPods Pro", "sales": 1999, "region": "广州"},
                {"product": "iPad Air", "sales": 4999, "region": "深圳"},
                {"product": "Apple Watch", "sales": 2999, "region": "杭州"}
            ]
            self.send_json_response({
                "data": sample_data,
                "total": len(sample_data),
                "message": "示例销售数据"
            })
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/login':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                username = data.get('username', '')
                password = data.get('password', '')
                
                if username == '<EMAIL>' and password == 'admin123':
                    self.send_json_response({
                        "success": True,
                        "message": "登录成功",
                        "token": "mock_token_12345",
                        "user": {
                            "id": 1,
                            "email": "<EMAIL>",
                            "name": "管理员"
                        }
                    })
                else:
                    self.send_json_response({
                        "success": False,
                        "message": "用户名或密码错误"
                    }, status=401)
            except:
                self.send_json_response({
                    "success": False,
                    "message": "请求格式错误"
                }, status=400)
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

def run_server():
    """启动服务器"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, SimpleHandler)
    
    print("🚀 简化版BI数据分析平台启动成功!")
    print("=" * 50)
    print(f"📱 访问地址: http://localhost:8000")
    print(f"🔧 API接口: http://localhost:8000/api/health")
    print(f"📊 数据接口: http://localhost:8000/api/data")
    print(f"🔑 默认登录: <EMAIL> / admin123")
    print("=" * 50)
    print("按 Ctrl+C 停止服务器")
    print()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
