@echo off
echo Starting Frontend Development Server
echo =====================================

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found. Please install Node.js 18+
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查npm是否安装
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm not found. Please install npm
    pause
    exit /b 1
)

REM 配置npm国内镜像源
echo Configuring npm registry...
npm config set registry https://registry.npmmirror.com

REM 安装依赖（如果node_modules不存在）
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
) else (
    echo Dependencies already installed, skipping...
)

echo.
echo Starting Vite development server...
echo Frontend will be available at: http://localhost:3000
echo.

npm run dev
