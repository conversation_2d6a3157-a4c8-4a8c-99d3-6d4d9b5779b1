<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时测试指导</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px; color: #333;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { 
            background: white; border-radius: 16px; padding: 30px; margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center;
        }
        .card { 
            background: white; border-radius: 16px; padding: 30px; margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .btn { 
            background: #667eea; color: white; border: none; padding: 12px 24px;
            border-radius: 10px; cursor: pointer; margin: 8px; font-size: 14px;
            font-weight: 600; transition: all 0.3s ease; text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
        .btn-success { background: #10b981; }
        .btn-danger { background: #ef4444; }
        .btn-warning { background: #f59e0b; }
        .step { 
            background: #f8fafc; border: 2px solid #e5e7eb; border-radius: 12px; 
            padding: 20px; margin: 15px 0; position: relative;
        }
        .step-number { 
            position: absolute; top: -12px; left: 20px; background: #667eea; 
            color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;
        }
        .test-data { 
            background: #1f2937; color: #e5e7eb; padding: 15px; border-radius: 8px; 
            font-family: monospace; font-size: 13px; margin: 10px 0;
        }
        .expected { 
            background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; 
            padding: 15px; margin: 10px 0;
        }
        .warning { 
            background: #fffbeb; border: 1px solid #fed7aa; border-radius: 8px; 
            padding: 15px; margin: 10px 0; color: #92400e;
        }
        .status { padding: 15px; border-radius: 8px; margin: 15px 0; }
        .status.info { background: #dbeafe; border: 1px solid #93c5fd; color: #1e40af; }
        .status.success { background: #dcfce7; border: 1px solid #86efac; color: #166534; }
        .status.error { background: #fee2e2; border: 1px solid #fca5a5; color: #991b1b; }
        .checklist { list-style: none; padding: 0; }
        .checklist li { padding: 8px 0; display: flex; align-items: center; }
        .checklist li:before { content: "☐"; margin-right: 10px; font-size: 16px; }
        .checklist li.checked:before { content: "✅"; }
        .highlight { background: #fef3c7; padding: 2px 6px; border-radius: 4px; font-weight: 600; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 实时测试指导</h1>
            <p>按照以下步骤测试数据源管理功能</p>
            <div style="margin-top: 15px;">
                <span id="serverStatus" class="highlight">检查服务器状态中...</span>
            </div>
        </div>

        <!-- 测试步骤 -->
        <div class="card">
            <h3>📋 测试步骤</h3>
            
            <!-- 步骤1 -->
            <div class="step">
                <div class="step-number">1</div>
                <h4>🔧 验证模态框功能</h4>
                <p><strong>目标:</strong> 确认创建页面不会自动消失</p>
                
                <p><strong>操作:</strong></p>
                <ol>
                    <li>在数据源管理平台中点击 <span class="highlight">"➕ 创建数据源"</span> 按钮</li>
                    <li>观察模态框是否正常打开</li>
                    <li>等待3秒，确认模态框不会自动消失</li>
                </ol>
                
                <div class="expected">
                    <strong>✅ 预期结果:</strong>
                    <ul>
                        <li>模态框正常打开</li>
                        <li>模态框保持打开状态</li>
                        <li>页面底部显示"✅ 创建数据源对话框已打开"</li>
                        <li>可以看到表单字段</li>
                    </ul>
                </div>
                
                <div class="warning">
                    <strong>⚠️ 如果模态框仍然自动消失:</strong>
                    <ul>
                        <li>按F12打开浏览器控制台查看错误</li>
                        <li>检查页面底部的操作日志</li>
                        <li>尝试刷新页面后重试</li>
                    </ul>
                </div>
            </div>

            <!-- 步骤2 -->
            <div class="step">
                <div class="step-number">2</div>
                <h4>📁 测试SQLite连接 (推荐)</h4>
                <p><strong>目标:</strong> 验证真实连接测试功能</p>
                
                <div class="test-data">
数据源名称: 测试SQLite数据库
描述: 本地SQLite测试数据库
数据库类型: SQLite
数据库路径: C:\Users\<USER>\test\test\test_database.db
                </div>
                
                <p><strong>操作:</strong></p>
                <ol>
                    <li>在模态框中填写上述信息</li>
                    <li>点击 <span class="highlight">"🔍 测试连接"</span> 按钮</li>
                    <li>等待测试结果</li>
                    <li>如果成功，点击 <span class="highlight">"💾 保存数据源"</span></li>
                </ol>
                
                <div class="expected">
                    <strong>✅ 预期结果:</strong>
                    <ul>
                        <li>连接测试显示绿色成功状态</li>
                        <li>显示SQLite版本信息</li>
                        <li>显示表数量信息</li>
                        <li>"保存数据源"按钮启用</li>
                        <li>保存成功后模态框关闭</li>
                        <li>数据源出现在列表中</li>
                    </ul>
                </div>
            </div>

            <!-- 步骤3 -->
            <div class="step">
                <div class="step-number">3</div>
                <h4>❌ 测试错误检测功能</h4>
                <p><strong>目标:</strong> 验证错误连接能被准确检测</p>
                
                <div class="test-data">
数据源名称: 错误测试
描述: 测试错误检测功能
数据库类型: SQLite
数据库路径: C:\不存在的路径\错误文件.db
                </div>
                
                <p><strong>操作:</strong></p>
                <ol>
                    <li>再次点击"➕ 创建数据源"</li>
                    <li>填写上述错误信息</li>
                    <li>点击"🔍 测试连接"</li>
                    <li>观察错误检测结果</li>
                </ol>
                
                <div class="expected">
                    <strong>✅ 预期结果:</strong>
                    <ul>
                        <li>连接测试显示红色失败状态</li>
                        <li>显示"文件不存在"具体错误</li>
                        <li>提供解决建议</li>
                        <li>"保存数据源"按钮保持禁用</li>
                    </ul>
                </div>
            </div>

            <!-- 步骤4 -->
            <div class="step">
                <div class="step-number">4</div>
                <h4>🌐 测试网络数据库 (可选)</h4>
                <p><strong>目标:</strong> 验证网络连接检测</p>
                
                <div class="test-data">
数据源名称: 测试PostgreSQL
数据库类型: PostgreSQL
主机地址: localhost
端口: 5432
数据库: postgres
用户名: postgres
密码: password
                </div>
                
                <div class="expected">
                    <strong>✅ 预期结果 (无PostgreSQL服务器):</strong>
                    <ul>
                        <li>显示"无法连接到 localhost:5432"</li>
                        <li>提供网络检查建议</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 测试检查清单 -->
        <div class="card">
            <h3>✅ 测试检查清单</h3>
            <ul class="checklist">
                <li id="check-modal">模态框正常打开且不自动消失</li>
                <li id="check-form">可以正常填写表单字段</li>
                <li id="check-sqlite">SQLite连接测试成功</li>
                <li id="check-error">错误连接被正确检测</li>
                <li id="check-save">数据源保存功能正常</li>
                <li id="check-list">数据源列表正常显示</li>
            </ul>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-success" onclick="markAllChecked()">✅ 全部测试通过</button>
                <button class="btn btn-warning" onclick="resetChecklist()">🔄 重置清单</button>
            </div>
        </div>

        <!-- 故障排除 -->
        <div class="card">
            <h3>🔧 故障排除</h3>
            
            <div class="status error">
                <strong>❌ 如果模态框仍然自动消失:</strong>
                <ol>
                    <li>按F12打开浏览器开发者工具</li>
                    <li>查看Console标签页是否有JavaScript错误</li>
                    <li>检查页面底部的操作日志</li>
                    <li>尝试使用简化测试页面: <a href="file:///C:/Users/<USER>/test/test/simple_modal_test.html" target="_blank">simple_modal_test.html</a></li>
                </ol>
            </div>
            
            <div class="status error">
                <strong>❌ 如果连接测试显示服务器错误:</strong>
                <ol>
                    <li>检查真实连接测试服务器是否运行</li>
                    <li>访问 <a href="http://localhost:8080" target="_blank">http://localhost:8080</a> 验证服务器状态</li>
                    <li>如果服务器未运行，执行: <code>python real_connection_server.py</code></li>
                </ol>
            </div>
            
            <div class="status success">
                <strong>✅ 如果一切正常:</strong>
                <p>恭喜！您的数据源管理系统已经完全正常工作，具备真实的连接验证能力。</p>
            </div>
        </div>
    </div>

    <script>
        // 检查服务器状态
        async function checkServerStatus() {
            const statusElement = document.getElementById('serverStatus');
            
            try {
                const response = await fetch('http://localhost:8080/', {
                    method: 'GET',
                    mode: 'cors',
                    timeout: 5000
                });
                
                if (response.ok) {
                    statusElement.textContent = '✅ 真实连接测试服务器运行正常';
                    statusElement.style.background = '#dcfce7';
                    statusElement.style.color = '#166534';
                } else {
                    throw new Error('服务器响应异常');
                }
            } catch (error) {
                statusElement.textContent = '❌ 真实连接测试服务器离线';
                statusElement.style.background = '#fee2e2';
                statusElement.style.color = '#991b1b';
            }
        }
        
        function markAllChecked() {
            const items = document.querySelectorAll('.checklist li');
            items.forEach(item => item.classList.add('checked'));
        }
        
        function resetChecklist() {
            const items = document.querySelectorAll('.checklist li');
            items.forEach(item => item.classList.remove('checked'));
        }
        
        // 页面加载时检查服务器状态
        window.onload = function() {
            checkServerStatus();
            
            // 每30秒检查一次服务器状态
            setInterval(checkServerStatus, 30000);
        };
    </script>
</body>
</html>
