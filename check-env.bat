@echo off
echo Environment Check
echo ==================

echo.
echo 1. Checking Python...
python --version 2>nul
if errorlevel 1 (
    echo   ❌ Python NOT FOUND
    echo   Please install Python from: https://www.python.org/downloads/
    set PYTHON_OK=0
) else (
    echo   ✅ Python found
    python --version
    set PYTHON_OK=1
)

echo.
echo 2. Checking Node.js...
node --version 2>nul
if errorlevel 1 (
    echo   ❌ Node.js NOT FOUND
    echo   Please install Node.js from: https://nodejs.org/
    set NODE_OK=0
) else (
    echo   ✅ Node.js found
    node --version
    set NODE_OK=1
)

echo.
echo 3. Checking npm...
npm --version 2>nul
if errorlevel 1 (
    echo   ❌ npm NOT FOUND
    set NPM_OK=0
) else (
    echo   ✅ npm found
    npm --version
    set NPM_OK=1
)

echo.
echo 4. Checking project files...
if exist "simple_backend.py" (
    echo   ✅ simple_backend.py found
) else (
    echo   ❌ simple_backend.py NOT FOUND
)

if exist "backend" (
    echo   ✅ backend directory found
) else (
    echo   ❌ backend directory NOT FOUND
)

if exist "frontend" (
    echo   ✅ frontend directory found
) else (
    echo   ❌ frontend directory NOT FOUND
)

echo.
echo 5. Testing Python import...
python -c "import json, http.server; print('✅ Python modules OK')" 2>nul
if errorlevel 1 (
    echo   ❌ Python modules test failed
) else (
    echo   ✅ Python modules test passed
)

echo.
echo ==================
echo Summary:
echo ==================

if %PYTHON_OK%==1 (
    echo ✅ You can run: start-simple.bat
) else (
    echo ❌ Install Python first
)

if %NODE_OK%==1 if %NPM_OK%==1 (
    echo ✅ You can run frontend development
) else (
    echo ❌ Install Node.js first for frontend
)

echo.
echo Recommended next steps:
if %PYTHON_OK%==1 (
    echo 1. Run: start-simple.bat  ^(for basic test^)
    if %NODE_OK%==1 (
        echo 2. Run: start-local.bat  ^(for full development^)
    )
) else (
    echo 1. Install Python 3.8+ from https://www.python.org/downloads/
    echo 2. Install Node.js 16+ from https://nodejs.org/
    echo 3. Run this check again
)

echo.
pause
