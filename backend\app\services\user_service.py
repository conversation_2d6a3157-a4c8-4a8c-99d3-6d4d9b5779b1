"""
用户服务

提供用户相关的业务逻辑。
"""

from typing import List, Optional

from sqlalchemy.orm import Session

from app.core.security import get_password_hash
from app.db.models.user import User
from app.schemas.user import UserCreate, UserUpdate


class UserService:
    """用户服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user(self, user_id: int) -> Optional[User]:
        """
        根据ID获取用户。
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[User]: 用户对象或None
        """
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱获取用户。
        
        Args:
            email: 邮箱地址
            
        Returns:
            Optional[User]: 用户对象或None
        """
        return self.db.query(User).filter(User.email == email).first()
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名获取用户。
        
        Args:
            username: 用户名
            
        Returns:
            Optional[User]: 用户对象或None
        """
        return self.db.query(User).filter(User.username == username).first()
    
    def get_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        获取用户列表。
        
        Args:
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[User]: 用户列表
        """
        return self.db.query(User).offset(skip).limit(limit).all()
    
    def create_user(self, user_data: UserCreate) -> User:
        """
        创建用户。
        
        Args:
            user_data: 用户创建数据
            
        Returns:
            User: 创建的用户对象
        """
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            email=user_data.email,
            username=user_data.username,
            full_name=user_data.full_name,
            hashed_password=hashed_password,
            role=user_data.role,
            phone=user_data.phone,
            department=user_data.department,
            position=user_data.position,
            language=user_data.language,
            theme=user_data.theme,
            timezone=user_data.timezone,
            email_notifications=user_data.email_notifications,
            push_notifications=user_data.push_notifications,
            bio=user_data.bio,
        )
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        return db_user
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """
        更新用户信息。
        
        Args:
            user_id: 用户ID
            user_data: 用户更新数据
            
        Returns:
            Optional[User]: 更新后的用户对象或None
        """
        db_user = self.get_user(user_id)
        if not db_user:
            return None
        
        update_data = user_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_user, field, value)
        
        self.db.commit()
        self.db.refresh(db_user)
        return db_user
    
    def delete_user(self, user_id: int) -> bool:
        """
        删除用户。
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否删除成功
        """
        db_user = self.get_user(user_id)
        if not db_user:
            return False
        
        self.db.delete(db_user)
        self.db.commit()
        return True
