#!/usr/bin/env python3
"""
数据库依赖安装脚本

自动安装所有支持的数据库驱动和依赖
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def print_banner():
    """打印安装横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🗄️  数据库依赖安装器  🗄️                            ║
    ║                                                              ║
    ║        自动安装所有数据库驱动和依赖                           ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def run_command(command, description, optional=False):
    """运行命令并处理结果"""
    print(f"\n🔧 {description}...")
    print(f"📝 执行命令: {command}")
    
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            if optional:
                print(f"⚠️ {description} 失败 (可选依赖)")
                print(f"   错误: {result.stderr.strip()}")
                return True  # 可选依赖失败不影响整体安装
            else:
                print(f"❌ {description} 失败")
                print(f"   错误: {result.stderr.strip()}")
                return False
                
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {str(e)}")
        return False

def install_core_dependencies():
    """安装核心依赖"""
    print("\n" + "="*60)
    print("📦 安装核心依赖")
    print("="*60)
    
    core_packages = [
        "requests>=2.31.0",
        "urllib3>=2.0.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.3.0",
        "loguru>=0.7.0"
    ]
    
    success_count = 0
    for package in core_packages:
        if run_command(f"pip install {package}", f"安装 {package.split('>=')[0]}"):
            success_count += 1
    
    print(f"\n📊 核心依赖安装结果: {success_count}/{len(core_packages)}")
    return success_count == len(core_packages)

def install_database_drivers():
    """安装数据库驱动"""
    print("\n" + "="*60)
    print("🗄️ 安装数据库驱动")
    print("="*60)
    
    # 必需的数据库驱动
    required_drivers = [
        ("psycopg2-binary>=2.9.7", "PostgreSQL驱动"),
        ("pymysql>=1.1.0", "MySQL驱动"),
        ("pymongo>=4.5.0", "MongoDB驱动"),
        ("redis>=5.0.0", "Redis驱动"),
    ]
    
    # 可选的数据库驱动（可能需要额外配置）
    optional_drivers = [
        ("pyodbc>=4.0.39", "SQL Server驱动"),
        ("cx-Oracle>=8.3.0", "Oracle驱动"),
        ("elasticsearch>=8.9.0", "Elasticsearch驱动"),
        ("influxdb-client>=1.37.0", "InfluxDB驱动"),
        ("cassandra-driver>=3.28.0", "Cassandra驱动"),
        ("neo4j>=5.12.0", "Neo4j驱动"),
        ("clickhouse-driver>=0.2.6", "ClickHouse驱动"),
        ("duckdb>=0.8.1", "DuckDB驱动"),
    ]
    
    # 安装必需驱动
    print("\n🔧 安装必需数据库驱动:")
    required_success = 0
    for package, description in required_drivers:
        if run_command(f"pip install {package}", f"安装 {description}"):
            required_success += 1
    
    # 安装可选驱动
    print("\n🔧 安装可选数据库驱动:")
    optional_success = 0
    for package, description in optional_drivers:
        if run_command(f"pip install {package}", f"安装 {description}", optional=True):
            optional_success += 1
    
    print(f"\n📊 数据库驱动安装结果:")
    print(f"   必需驱动: {required_success}/{len(required_drivers)}")
    print(f"   可选驱动: {optional_success}/{len(optional_drivers)}")
    
    return required_success == len(required_drivers)

def install_async_drivers():
    """安装异步驱动"""
    print("\n" + "="*60)
    print("⚡ 安装异步数据库驱动")
    print("="*60)
    
    async_drivers = [
        ("asyncpg>=0.28.0", "PostgreSQL异步驱动"),
        ("aiomysql>=0.2.0", "MySQL异步驱动"),
        ("aioredis>=2.0.0", "Redis异步驱动"),
        ("motor>=3.3.0", "MongoDB异步驱动"),
        ("aiofiles>=23.2.1", "异步文件操作"),
    ]
    
    success_count = 0
    for package, description in async_drivers:
        if run_command(f"pip install {package}", f"安装 {description}", optional=True):
            success_count += 1
    
    print(f"\n📊 异步驱动安装结果: {success_count}/{len(async_drivers)}")
    return True  # 异步驱动是可选的

def install_development_tools():
    """安装开发工具"""
    print("\n" + "="*60)
    print("🛠️ 安装开发工具")
    print("="*60)
    
    dev_tools = [
        ("SQLAlchemy>=2.0.20", "ORM框架"),
        ("alembic>=1.12.0", "数据库迁移工具"),
        ("pandas>=2.1.0", "数据分析库"),
        ("fastapi>=0.103.0", "Web框架"),
        ("uvicorn>=0.23.0", "ASGI服务器"),
        ("pytest>=7.4.0", "测试框架"),
    ]
    
    success_count = 0
    for package, description in dev_tools:
        if run_command(f"pip install {package}", f"安装 {description}", optional=True):
            success_count += 1
    
    print(f"\n📊 开发工具安装结果: {success_count}/{len(dev_tools)}")
    return True  # 开发工具是可选的

def install_from_requirements():
    """从requirements文件安装"""
    print("\n" + "="*60)
    print("📋 从requirements文件安装")
    print("="*60)
    
    requirements_file = Path("database_requirements.txt")
    if requirements_file.exists():
        return run_command(
            f"pip install -r {requirements_file}",
            "从database_requirements.txt安装依赖"
        )
    else:
        print("⚠️ database_requirements.txt 文件不存在，跳过")
        return True

def check_installations():
    """检查安装结果"""
    print("\n" + "="*60)
    print("🔍 检查安装结果")
    print("="*60)
    
    test_imports = [
        ("sqlite3", "SQLite", True),
        ("psycopg2", "PostgreSQL", False),
        ("pymysql", "MySQL", False),
        ("pymongo", "MongoDB", False),
        ("redis", "Redis", False),
        ("pyodbc", "SQL Server", True),  # 可选
        ("cx_Oracle", "Oracle", True),   # 可选
        ("elasticsearch", "Elasticsearch", True),  # 可选
    ]
    
    success_count = 0
    total_required = 0
    
    for module, name, optional in test_imports:
        if not optional:
            total_required += 1
            
        try:
            __import__(module)
            print(f"✅ {name} 驱动可用")
            success_count += 1
        except ImportError:
            if optional:
                print(f"⚠️ {name} 驱动不可用 (可选)")
            else:
                print(f"❌ {name} 驱动不可用")
    
    print(f"\n📊 驱动可用性检查:")
    print(f"   总计可用: {success_count}/{len(test_imports)}")
    print(f"   必需可用: {min(success_count, total_required)}/{total_required}")
    
    return min(success_count, total_required) >= total_required * 0.8  # 80%成功率

def create_test_script():
    """创建测试脚本"""
    print("\n🧪 创建数据库连接测试脚本...")
    
    test_script = """#!/usr/bin/env python3
\"\"\"
数据库连接测试脚本
\"\"\"

def test_database_connections():
    print("🔍 测试数据库连接...")
    
    # SQLite测试
    try:
        import sqlite3
        conn = sqlite3.connect(':memory:')
        conn.close()
        print("✅ SQLite: 可用")
    except Exception as e:
        print(f"❌ SQLite: {e}")
    
    # PostgreSQL测试
    try:
        import psycopg2
        print("✅ PostgreSQL驱动: 已安装")
    except ImportError:
        print("❌ PostgreSQL驱动: 未安装")
    
    # MySQL测试
    try:
        import pymysql
        print("✅ MySQL驱动: 已安装")
    except ImportError:
        print("❌ MySQL驱动: 未安装")
    
    # MongoDB测试
    try:
        import pymongo
        print("✅ MongoDB驱动: 已安装")
    except ImportError:
        print("❌ MongoDB驱动: 未安装")
    
    # Redis测试
    try:
        import redis
        print("✅ Redis驱动: 已安装")
    except ImportError:
        print("❌ Redis驱动: 未安装")

if __name__ == "__main__":
    test_database_connections()
"""
    
    with open("test_database_connections.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: test_database_connections.py")

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    print(f"\n🖥️ 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   Python版本: {sys.version}")
    print(f"   工作目录: {os.getcwd()}")
    
    # 升级pip
    print("\n🔧 升级pip...")
    run_command("pip install --upgrade pip", "升级pip")
    
    # 安装步骤
    steps = [
        ("安装核心依赖", install_core_dependencies),
        ("安装数据库驱动", install_database_drivers),
        ("安装异步驱动", install_async_drivers),
        ("安装开发工具", install_development_tools),
        ("从requirements文件安装", install_from_requirements),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"⚠️ {step_name} 部分失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {str(e)}")
    
    # 检查安装结果
    check_success = check_installations()
    
    # 创建测试脚本
    create_test_script()
    
    # 总结
    print("\n" + "="*60)
    print("📊 安装总结")
    print("="*60)
    print(f"完成步骤: {success_count}/{len(steps)}")
    print(f"驱动检查: {'✅ 通过' if check_success else '⚠️ 部分通过'}")
    
    if check_success:
        print("\n🎉 数据库依赖安装完成！")
        print("\n🚀 下一步:")
        print("   1. 运行测试: python test_database_connections.py")
        print("   2. 启动服务: python real_connection_server.py")
        print("   3. 测试连接: 访问高级数据源管理平台")
    else:
        print("\n⚠️ 部分依赖安装失败，但核心功能应该可用")
        print("   可以继续使用SQLite等内置数据库")
    
    return check_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程发生异常: {str(e)}")
        sys.exit(1)
