# 🗄️ 数据库依赖安装指南

## 📋 概述

为了支持真实的数据库连接测试，需要安装相应的数据库驱动。本指南提供了完整的安装方法和故障排除。

## 🎯 快速安装

### 方法一：使用安装脚本 (推荐)

```bash
# 安装核心驱动
python install_core_drivers.py

# 检查安装状态
python check_database_drivers.py
```

### 方法二：手动安装核心驱动

```bash
# 一键安装核心驱动
pip install psycopg2-binary pymysql pymongo redis

# 可选驱动
pip install pyodbc elasticsearch duckdb
```

### 方法三：从requirements文件安装

```bash
# 安装所有依赖
pip install -r database_requirements.txt

# 或安装后端依赖
pip install -r backend/requirements.txt
```

## 📦 支持的数据库类型

### ✅ 核心数据库 (推荐安装)

| 数据库 | 驱动包 | 安装命令 | 状态 |
|--------|--------|----------|------|
| **SQLite** | 内置 | 无需安装 | ✅ 立即可用 |
| **PostgreSQL** | psycopg2-binary | `pip install psycopg2-binary` | 🔧 需要安装 |
| **MySQL** | pymysql | `pip install pymysql` | 🔧 需要安装 |
| **MongoDB** | pymongo | `pip install pymongo` | 🔧 需要安装 |
| **Redis** | redis | `pip install redis` | 🔧 需要安装 |

### 🔧 可选数据库

| 数据库 | 驱动包 | 安装命令 | 备注 |
|--------|--------|----------|------|
| **SQL Server** | pyodbc | `pip install pyodbc` | 需要ODBC驱动 |
| **Oracle** | cx-Oracle | `pip install cx-Oracle` | 需要Oracle Client |
| **Elasticsearch** | elasticsearch | `pip install elasticsearch` | 搜索引擎 |
| **InfluxDB** | influxdb-client | `pip install influxdb-client` | 时序数据库 |
| **DuckDB** | duckdb | `pip install duckdb` | 分析数据库 |
| **Cassandra** | cassandra-driver | `pip install cassandra-driver` | 分布式数据库 |
| **Neo4j** | neo4j | `pip install neo4j` | 图数据库 |
| **ClickHouse** | clickhouse-driver | `pip install clickhouse-driver` | 列式数据库 |

### ⚡ 异步驱动 (可选)

| 数据库 | 异步驱动 | 安装命令 |
|--------|----------|----------|
| **PostgreSQL** | asyncpg | `pip install asyncpg` |
| **MySQL** | aiomysql | `pip install aiomysql` |
| **Redis** | aioredis | `pip install aioredis` |
| **MongoDB** | motor | `pip install motor` |

## 🛠️ 安装步骤

### 第一步：检查Python环境

```bash
# 检查Python版本 (需要3.8+)
python --version

# 升级pip
python -m pip install --upgrade pip
```

### 第二步：安装核心驱动

```bash
# 方式1: 使用脚本
python install_core_drivers.py

# 方式2: 手动安装
pip install psycopg2-binary pymysql pymongo redis
```

### 第三步：验证安装

```bash
# 检查驱动状态
python check_database_drivers.py

# 或手动测试
python -c "import psycopg2; print('PostgreSQL OK')"
python -c "import pymysql; print('MySQL OK')"
python -c "import pymongo; print('MongoDB OK')"
python -c "import redis; print('Redis OK')"
```

### 第四步：启动测试服务器

```bash
# 启动真实连接测试服务器
python real_connection_server.py
```

## 🔧 故障排除

### 常见问题

#### 1. psycopg2 安装失败

**问题**: `error: Microsoft Visual C++ 14.0 is required`

**解决方案**:
```bash
# 使用二进制版本
pip install psycopg2-binary

# 或安装Visual C++ Build Tools
# 下载: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

#### 2. pyodbc 安装失败

**问题**: SQL Server驱动需要ODBC驱动

**解决方案**:
```bash
# Windows: 下载并安装 ODBC Driver 17 for SQL Server
# https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server

# 然后安装pyodbc
pip install pyodbc
```

#### 3. cx-Oracle 安装失败

**问题**: Oracle驱动需要Oracle Instant Client

**解决方案**:
```bash
# 1. 下载Oracle Instant Client
# https://www.oracle.com/database/technologies/instant-client.html

# 2. 设置环境变量
# Windows: 添加到PATH
# Linux: 设置LD_LIBRARY_PATH

# 3. 安装驱动
pip install cx-Oracle
```

#### 4. 网络问题

**问题**: pip安装超时或失败

**解决方案**:
```bash
# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ psycopg2-binary

# 或设置超时
pip install --timeout 300 psycopg2-binary
```

### 系统特定问题

#### Windows

```bash
# 可能需要安装Visual C++ Redistributable
# 下载: https://aka.ms/vs/17/release/vc_redist.x64.exe

# 使用conda (如果有)
conda install psycopg2 pymysql pymongo redis-py
```

#### Linux

```bash
# Ubuntu/Debian
sudo apt-get install python3-dev libpq-dev

# CentOS/RHEL
sudo yum install python3-devel postgresql-devel

# 然后安装Python包
pip install psycopg2-binary pymysql pymongo redis
```

#### macOS

```bash
# 使用Homebrew
brew install postgresql mysql mongodb redis

# 然后安装Python包
pip install psycopg2-binary pymysql pymongo redis
```

## 📊 验证安装

### 自动检查

```bash
# 运行完整检查
python check_database_drivers.py

# 输出示例:
# ✅ 已安装 SQLite        | 内置关系型数据库        | 版本: 3.39.0
# ✅ 已安装 PostgreSQL    | 开源关系型数据库        | 版本: 2.9.9
# ✅ 已安装 MySQL         | 流行关系型数据库        | 版本: 1.1.0
# ✅ 已安装 MongoDB       | 文档型数据库           | 版本: 4.6.0
# ✅ 已安装 Redis         | 内存键值数据库         | 版本: 5.0.1
```

### 手动测试

```python
# 测试SQLite (内置)
import sqlite3
conn = sqlite3.connect(':memory:')
print("SQLite OK")

# 测试PostgreSQL
import psycopg2
print("PostgreSQL driver OK")

# 测试MySQL
import pymysql
print("MySQL driver OK")

# 测试MongoDB
import pymongo
print("MongoDB driver OK")

# 测试Redis
import redis
print("Redis driver OK")
```

## 🎯 最小安装

如果只想快速测试，可以只安装最基本的驱动：

```bash
# 最小安装 (SQLite内置 + PostgreSQL)
pip install psycopg2-binary

# 或者只使用SQLite (无需安装)
# SQLite是Python内置的，立即可用
```

## 🚀 完整安装

如果想支持所有数据库类型：

```bash
# 安装所有驱动
pip install -r database_requirements.txt

# 或分步安装
pip install psycopg2-binary pymysql pymongo redis  # 核心
pip install pyodbc elasticsearch duckdb            # 可选
pip install asyncpg aiomysql aioredis motor        # 异步
```

## 📋 安装验证清单

- [ ] Python 3.8+ 已安装
- [ ] pip 已升级到最新版本
- [ ] SQLite 可用 (内置)
- [ ] PostgreSQL 驱动已安装
- [ ] MySQL 驱动已安装
- [ ] MongoDB 驱动已安装
- [ ] Redis 驱动已安装
- [ ] 真实连接测试服务器可启动
- [ ] 连接测试功能正常

## 🎉 完成后

安装完成后，您可以：

1. **启动真实连接测试服务器**:
   ```bash
   python real_connection_server.py
   ```

2. **使用高级数据源管理平台**:
   - 现在连接测试将使用真实验证
   - 错误信息将更加准确
   - 支持更多数据库类型

3. **测试连接功能**:
   - 尝试连接真实数据库
   - 验证错误检测功能
   - 体验完整的验证流程

---

**🎯 现在您的系统已具备完整的数据库连接验证能力！**
