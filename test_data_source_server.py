#!/usr/bin/env python3
"""
数据源功能测试服务器

模拟数据源管理API的功能测试。
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import threading
import time
import webbrowser

class DataSourceTestHandler(BaseHTTPRequestHandler):
    # 模拟数据存储
    data_sources = []
    next_id = 1
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            self.send_test_page()
        elif self.path == '/api/v1/health':
            self.send_json_response({
                "status": "healthy",
                "message": "数据源管理API测试服务器",
                "timestamp": datetime.now().isoformat()
            })
        elif self.path.startswith('/api/v1/data-sources'):
            self.handle_data_sources_get()
        elif self.path == '/api/v1/docs':
            self.send_api_docs()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/v1/auth/login':
            self.handle_login()
        elif self.path == '/api/v1/data-sources/':
            self.handle_create_data_source()
        elif self.path == '/api/v1/data-sources/test':
            self.handle_test_connection()
        elif self.path == '/api/v1/data-sources/query':
            self.handle_query_execution()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_PUT(self):
        """处理PUT请求"""
        if self.path.startswith('/api/v1/data-sources/'):
            self.handle_update_data_source()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_DELETE(self):
        """处理DELETE请求"""
        if self.path.startswith('/api/v1/data-sources/'):
            self.handle_delete_data_source()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def send_test_page(self):
        """发送测试页面"""
        html = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>数据源管理功能测试</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    background: #f5f5f5; line-height: 1.6; padding: 20px;
                }
                .container { max-width: 1200px; margin: 0 auto; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                         color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; 
                       box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .btn { background: #667eea; color: white; border: none; padding: 10px 20px; 
                      border-radius: 4px; cursor: pointer; margin: 5px; }
                .btn:hover { background: #5a6fd8; }
                .btn-danger { background: #ff4757; }
                .btn-danger:hover { background: #ff3742; }
                .form-group { margin: 15px 0; }
                .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
                .form-group input, .form-group select, .form-group textarea { 
                    width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
                .result { background: #f8f9fa; padding: 15px; border-radius: 4px; 
                         font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
                .success { color: #27ae60; }
                .error { color: #e74c3c; }
                .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
                .data-source-item { border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px; }
                .status-connected { color: #27ae60; }
                .status-disconnected { color: #e74c3c; }
                .status-testing { color: #f39c12; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🗄️ 数据源管理功能测试</h1>
                    <p>测试数据源的创建、连接、查询等功能</p>
                </div>

                <div class="grid">
                    <!-- 创建数据源 -->
                    <div class="card">
                        <h3>📝 创建数据源</h3>
                        <div class="form-group">
                            <label>数据源名称:</label>
                            <input type="text" id="dsName" value="测试SQLite数据源" />
                        </div>
                        <div class="form-group">
                            <label>描述:</label>
                            <textarea id="dsDescription">用于功能测试的SQLite数据库</textarea>
                        </div>
                        <div class="form-group">
                            <label>类型:</label>
                            <select id="dsType">
                                <option value="sqlite">SQLite</option>
                                <option value="postgresql">PostgreSQL</option>
                                <option value="mysql">MySQL</option>
                                <option value="mongodb">MongoDB</option>
                                <option value="redis">Redis</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>主机:</label>
                            <input type="text" id="dsHost" value="localhost" />
                        </div>
                        <div class="form-group">
                            <label>端口:</label>
                            <input type="number" id="dsPort" value="0" />
                        </div>
                        <div class="form-group">
                            <label>数据库:</label>
                            <input type="text" id="dsDatabase" value=":memory:" />
                        </div>
                        <button class="btn" onclick="createDataSource()">创建数据源</button>
                    </div>

                    <!-- 数据源列表 -->
                    <div class="card">
                        <h3>📋 数据源列表</h3>
                        <button class="btn" onclick="loadDataSources()">刷新列表</button>
                        <div id="dataSourceList" style="margin-top: 15px;"></div>
                    </div>
                </div>

                <!-- 连接测试 -->
                <div class="card">
                    <h3>🔗 连接测试</h3>
                    <button class="btn" onclick="testConnection()">测试SQLite连接</button>
                    <button class="btn" onclick="testPostgreSQLConnection()">测试PostgreSQL连接</button>
                    <div id="connectionResult" class="result" style="margin-top: 15px;"></div>
                </div>

                <!-- 查询执行 -->
                <div class="card">
                    <h3>🔍 查询执行</h3>
                    <div class="form-group">
                        <label>数据源ID:</label>
                        <input type="number" id="queryDataSourceId" value="1" />
                    </div>
                    <div class="form-group">
                        <label>SQL查询:</label>
                        <textarea id="sqlQuery">SELECT 1 as id, 'Hello World' as message, datetime('now') as created_at</textarea>
                    </div>
                    <button class="btn" onclick="executeQuery()">执行查询</button>
                    <div id="queryResult" class="result" style="margin-top: 15px;"></div>
                </div>

                <!-- API测试结果 -->
                <div class="card">
                    <h3>📊 API测试结果</h3>
                    <div id="apiResult" class="result"></div>
                </div>
            </div>

            <script>
                let token = null;

                // 自动登录
                async function autoLogin() {
                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                username: '<EMAIL>',
                                password: 'admin123'
                            })
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            token = data.access_token;
                            updateResult('✅ 自动登录成功');
                        }
                    } catch (error) {
                        updateResult('❌ 登录失败: ' + error.message);
                    }
                }

                // 创建数据源
                async function createDataSource() {
                    const data = {
                        name: document.getElementById('dsName').value,
                        description: document.getElementById('dsDescription').value,
                        type: document.getElementById('dsType').value,
                        host: document.getElementById('dsHost').value,
                        port: parseInt(document.getElementById('dsPort').value),
                        database: document.getElementById('dsDatabase').value,
                        username: 'test',
                        password: 'test',
                        is_active: true
                    };

                    try {
                        const response = await fetch('/api/v1/data-sources/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();
                        if (response.ok) {
                            updateResult(`✅ 数据源创建成功: ID=${result.id}, Name=${result.name}`);
                            loadDataSources();
                        } else {
                            updateResult(`❌ 创建失败: ${result.detail || result.message}`);
                        }
                    } catch (error) {
                        updateResult('❌ 请求失败: ' + error.message);
                    }
                }

                // 加载数据源列表
                async function loadDataSources() {
                    try {
                        const response = await fetch('/api/v1/data-sources/', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        const result = await response.json();
                        if (response.ok) {
                            displayDataSources(result.items);
                            updateResult(`✅ 加载数据源列表成功: 共${result.total}个`);
                        } else {
                            updateResult(`❌ 加载失败: ${result.detail}`);
                        }
                    } catch (error) {
                        updateResult('❌ 请求失败: ' + error.message);
                    }
                }

                // 显示数据源列表
                function displayDataSources(dataSources) {
                    const container = document.getElementById('dataSourceList');
                    if (dataSources.length === 0) {
                        container.innerHTML = '<p>暂无数据源</p>';
                        return;
                    }

                    let html = '';
                    dataSources.forEach(ds => {
                        const statusClass = `status-${ds.status}`;
                        html += `
                            <div class="data-source-item">
                                <strong>${ds.name}</strong> (${ds.type})
                                <span class="${statusClass}">● ${ds.status}</span>
                                <br>
                                <small>${ds.description || '无描述'}</small>
                                <br>
                                <button class="btn" onclick="testDataSourceConnection(${ds.id})">测试连接</button>
                                <button class="btn btn-danger" onclick="deleteDataSource(${ds.id})">删除</button>
                            </div>
                        `;
                    });
                    container.innerHTML = html;
                }

                // 测试连接
                async function testConnection() {
                    const data = {
                        type: 'sqlite',
                        host: '',
                        port: 0,
                        database: ':memory:',
                        username: '',
                        password: ''
                    };

                    try {
                        const response = await fetch('/api/v1/data-sources/test', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();
                        const resultDiv = document.getElementById('connectionResult');
                        if (result.success) {
                            resultDiv.innerHTML = `<span class="success">✅ ${result.message}</span>`;
                        } else {
                            resultDiv.innerHTML = `<span class="error">❌ ${result.message}</span>`;
                        }
                    } catch (error) {
                        document.getElementById('connectionResult').innerHTML = 
                            `<span class="error">❌ 请求失败: ${error.message}</span>`;
                    }
                }

                // 执行查询
                async function executeQuery() {
                    const data = {
                        data_source_id: parseInt(document.getElementById('queryDataSourceId').value),
                        query: document.getElementById('sqlQuery').value,
                        limit: 10
                    };

                    try {
                        const response = await fetch('/api/v1/data-sources/query', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();
                        const resultDiv = document.getElementById('queryResult');
                        
                        if (result.success) {
                            let html = `<span class="success">✅ 查询成功</span><br>`;
                            html += `执行时间: ${result.execution_time}秒<br>`;
                            html += `返回记录: ${result.total}条<br>`;
                            html += `列名: ${result.columns.join(', ')}<br>`;
                            html += `数据: ${JSON.stringify(result.data, null, 2)}`;
                            resultDiv.innerHTML = html;
                        } else {
                            resultDiv.innerHTML = `<span class="error">❌ ${result.message}</span>`;
                        }
                    } catch (error) {
                        document.getElementById('queryResult').innerHTML = 
                            `<span class="error">❌ 请求失败: ${error.message}</span>`;
                    }
                }

                // 删除数据源
                async function deleteDataSource(id) {
                    if (!confirm('确定要删除这个数据源吗？')) return;

                    try {
                        const response = await fetch(`/api/v1/data-sources/${id}`, {
                            method: 'DELETE',
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        const result = await response.json();
                        if (response.ok) {
                            updateResult(`✅ 删除成功: ${result.message}`);
                            loadDataSources();
                        } else {
                            updateResult(`❌ 删除失败: ${result.detail}`);
                        }
                    } catch (error) {
                        updateResult('❌ 请求失败: ' + error.message);
                    }
                }

                // 更新结果显示
                function updateResult(message) {
                    const resultDiv = document.getElementById('apiResult');
                    const timestamp = new Date().toLocaleTimeString();
                    resultDiv.innerHTML = `[${timestamp}] ${message}<br>` + resultDiv.innerHTML;
                }

                // 页面加载完成后自动登录
                window.onload = function() {
                    autoLogin();
                    setTimeout(loadDataSources, 1000);
                };
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def handle_login(self):
        """处理登录"""
        self.send_json_response({
            "access_token": "test_token_12345",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": 1,
                "email": "<EMAIL>",
                "name": "测试管理员",
                "role": "admin"
            }
        })
    
    def handle_create_data_source(self):
        """处理创建数据源"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            
            # 创建新数据源
            new_ds = {
                "id": self.next_id,
                "name": data["name"],
                "description": data.get("description", ""),
                "type": data["type"],
                "host": data.get("host", ""),
                "port": data.get("port", 0),
                "database": data.get("database", ""),
                "username": data.get("username", ""),
                "status": "disconnected",
                "is_active": data.get("is_active", True),
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "last_test_time": None,
                "last_test_result": None
            }
            
            self.data_sources.append(new_ds)
            self.next_id += 1
            
            self.send_json_response(new_ds)
            
        except Exception as e:
            self.send_json_response({
                "detail": f"创建失败: {str(e)}"
            }, status=400)
    
    def handle_data_sources_get(self):
        """处理获取数据源列表"""
        # 解析URL参数
        parsed_url = urllib.parse.urlparse(self.path)
        
        if parsed_url.path == '/api/v1/data-sources/':
            # 返回列表
            self.send_json_response({
                "items": self.data_sources,
                "total": len(self.data_sources),
                "page": 1,
                "size": 20,
                "pages": 1
            })
        else:
            # 获取单个数据源
            try:
                ds_id = int(parsed_url.path.split('/')[-1])
                ds = next((ds for ds in self.data_sources if ds["id"] == ds_id), None)
                if ds:
                    self.send_json_response(ds)
                else:
                    self.send_json_response({"detail": "数据源不存在"}, status=404)
            except ValueError:
                self.send_json_response({"detail": "无效的数据源ID"}, status=400)
    
    def handle_test_connection(self):
        """处理连接测试"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            
            # 模拟连接测试
            if data["type"] == "sqlite":
                result = {
                    "success": True,
                    "message": "SQLite连接测试成功",
                    "details": {"version": "3.39.0"},
                    "test_time": datetime.now().isoformat()
                }
            else:
                result = {
                    "success": False,
                    "message": f"暂不支持 {data['type']} 类型的连接测试",
                    "details": None,
                    "test_time": datetime.now().isoformat()
                }
            
            self.send_json_response(result)
            
        except Exception as e:
            self.send_json_response({
                "success": False,
                "message": f"测试失败: {str(e)}",
                "details": None,
                "test_time": datetime.now().isoformat()
            })
    
    def handle_query_execution(self):
        """处理查询执行"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            
            # 模拟查询执行
            mock_data = [
                {"id": 1, "message": "Hello World", "created_at": "2024-01-01 12:00:00"},
                {"id": 2, "message": "Test Data", "created_at": "2024-01-01 12:01:00"}
            ]
            
            result = {
                "success": True,
                "data": mock_data,
                "columns": ["id", "message", "created_at"],
                "total": len(mock_data),
                "execution_time": 0.025,
                "message": "查询执行成功"
            }
            
            self.send_json_response(result)
            
        except Exception as e:
            self.send_json_response({
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "execution_time": 0,
                "message": f"查询失败: {str(e)}"
            })
    
    def handle_delete_data_source(self):
        """处理删除数据源"""
        try:
            ds_id = int(self.path.split('/')[-1])
            self.data_sources = [ds for ds in self.data_sources if ds["id"] != ds_id]
            self.send_json_response({"message": "数据源删除成功"})
        except ValueError:
            self.send_json_response({"detail": "无效的数据源ID"}, status=400)
    
    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://localhost:8000')

def run_test_server():
    """启动测试服务器"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, DataSourceTestHandler)
    
    print("🚀 数据源管理功能测试服务器启动成功!")
    print("=" * 60)
    print(f"📱 访问地址: http://localhost:8000")
    print(f"🧪 功能测试: 数据源的创建、连接、查询等功能")
    print(f"📊 API端点: /api/v1/data-sources/")
    print("=" * 60)
    print("⚡ 正在自动打开浏览器...")
    print("按 Ctrl+C 停止服务器")
    print()
    
    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 测试服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_test_server()
