#!/usr/bin/env python3
"""
快速测试脚本

测试数据源管理系统的核心功能
"""

import sqlite3
import os
import json
import requests
from datetime import datetime

def print_header():
    """打印测试标题"""
    print("=" * 60)
    print("🧪 数据源管理系统快速测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def test_sqlite_availability():
    """测试SQLite可用性"""
    print("📁 测试SQLite可用性...")
    try:
        # 测试SQLite导入
        import sqlite3
        print("✅ SQLite模块导入成功")
        
        # 测试内存数据库
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version()")
        version = cursor.fetchone()[0]
        conn.close()
        print(f"✅ SQLite版本: {version}")
        
        # 测试文件数据库
        db_path = "test_database.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            conn.close()
            print(f"✅ 测试数据库文件存在，包含 {table_count} 个表")
        else:
            print("⚠️ 测试数据库文件不存在，将创建新文件")
            # 创建测试数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            cursor.execute("INSERT INTO test_table (name) VALUES ('测试数据')")
            conn.commit()
            conn.close()
            print("✅ 测试数据库文件已创建")
        
        return True
        
    except Exception as e:
        print(f"❌ SQLite测试失败: {e}")
        return False

def test_server_connection():
    """测试真实连接测试服务器"""
    print("\n🔧 测试真实连接测试服务器...")
    
    server_url = "http://localhost:8080"
    
    try:
        # 测试服务器状态
        response = requests.get(server_url, timeout=5)
        if response.status_code == 200:
            print("✅ 真实连接测试服务器运行正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到真实连接测试服务器")
        print("💡 请启动服务器: python real_connection_server.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ 服务器连接超时")
        return False
    except Exception as e:
        print(f"❌ 服务器测试异常: {e}")
        return False

def test_real_connection_api():
    """测试真实连接API"""
    print("\n🔍 测试真实连接API...")
    
    api_url = "http://localhost:8080/api/test-connection"
    
    # 测试SQLite连接
    test_data = {
        "type": "sqlite",
        "database": "test_database.db"
    }
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ SQLite连接测试API成功")
                print(f"   消息: {result.get('message')}")
                if result.get('details'):
                    details = result['details']
                    print(f"   版本: {details.get('version', 'Unknown')}")
                    print(f"   表数量: {details.get('table_count', 'Unknown')}")
                return True
            else:
                print("❌ SQLite连接测试API失败")
                print(f"   错误: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        return False
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False

def test_error_detection():
    """测试错误检测功能"""
    print("\n❌ 测试错误检测功能...")
    
    api_url = "http://localhost:8080/api/test-connection"
    
    # 测试不存在的文件
    test_data = {
        "type": "sqlite",
        "database": "不存在的文件.db"
    }
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if not result.get('success'):
                print("✅ 错误检测功能正常")
                print(f"   错误类型: {result.get('error_type')}")
                print(f"   错误消息: {result.get('message')}")
                if result.get('suggestion'):
                    print(f"   建议: {result.get('suggestion')}")
                return True
            else:
                print("❌ 错误检测功能异常 - 应该返回失败但返回了成功")
                return False
        else:
            print(f"❌ 错误检测测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 错误检测测试异常: {e}")
        return False

def test_database_drivers():
    """测试数据库驱动"""
    print("\n🗄️ 测试数据库驱动...")
    
    drivers = [
        ("sqlite3", "SQLite", True),
        ("psycopg2", "PostgreSQL", False),
        ("pymysql", "MySQL", False),
        ("pymongo", "MongoDB", False),
        ("redis", "Redis", False),
    ]
    
    available_count = 0
    required_count = 0
    
    for module, name, required in drivers:
        if required:
            required_count += 1
            
        try:
            __import__(module)
            print(f"✅ {name} 驱动可用")
            available_count += 1
        except ImportError:
            if required:
                print(f"❌ {name} 驱动不可用 (必需)")
            else:
                print(f"⚠️ {name} 驱动不可用 (可选)")
    
    print(f"\n📊 驱动统计: {available_count}/{len(drivers)} 可用")
    print(f"   必需驱动: {min(available_count, required_count)}/{required_count} 可用")
    
    return min(available_count, required_count) >= required_count

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📊 测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    print("\n🎯 总体评估:")
    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统功能完全正常")
        print("✅ 可以正常使用数据源管理功能")
    elif passed_tests >= total_tests * 0.8:
        print("✅ 大部分测试通过，系统基本可用")
        print("💡 建议修复失败的测试项")
    elif passed_tests >= total_tests * 0.5:
        print("⚠️ 部分测试通过，系统部分可用")
        print("🔧 需要解决一些问题")
    else:
        print("❌ 多数测试失败，系统需要修复")
        print("🛠️ 请检查环境配置和依赖安装")
    
    print("\n🚀 下一步建议:")
    if results.get("SQLite可用性", False):
        print("  1. 使用SQLite进行数据源测试")
    if results.get("服务器连接", False):
        print("  2. 在浏览器中测试数据源管理平台")
    if not results.get("服务器连接", True):
        print("  1. 启动真实连接测试服务器: python real_connection_server.py")
    if not results.get("数据库驱动", True):
        print("  2. 安装数据库驱动: python install_core_drivers.py")

def main():
    """主函数"""
    print_header()
    
    # 执行测试
    tests = {
        "SQLite可用性": test_sqlite_availability,
        "服务器连接": test_server_connection,
        "真实连接API": test_real_connection_api,
        "错误检测": test_error_detection,
        "数据库驱动": test_database_drivers,
    }
    
    results = {}
    
    for test_name, test_func in tests.items():
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成报告
    generate_test_report(results)
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
