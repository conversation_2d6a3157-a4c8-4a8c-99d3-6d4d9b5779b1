"""
健康检查API端点

提供系统健康状态检查的API接口。
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.db.base import get_db

router = APIRouter()


@router.get("/")
async def health_check():
    """
    基础健康检查。
    
    Returns:
        dict: 健康状态信息
    """
    return {
        "status": "healthy",
        "message": "API服务运行正常"
    }


@router.get("/db")
async def database_health_check(db: Session = Depends(get_db)):
    """
    数据库健康检查。
    
    Args:
        db: 数据库会话
        
    Returns:
        dict: 数据库健康状态信息
    """
    try:
        # 执行简单的数据库查询
        db.execute("SELECT 1")
        return {
            "status": "healthy",
            "message": "数据库连接正常"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"数据库连接异常: {str(e)}"
        }
