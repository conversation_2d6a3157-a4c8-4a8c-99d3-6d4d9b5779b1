"""
用户相关的Pydantic模式

定义用户数据的输入输出模式。
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr

from app.db.models.user import UserRole


class UserBase(BaseModel):
    """用户基础模式"""
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    language: str = "zh"
    theme: str = "light"
    timezone: str = "Asia/Shanghai"
    email_notifications: bool = True
    push_notifications: bool = True
    bio: Optional[str] = None


class UserCreate(UserBase):
    """用户创建模式"""
    password: str
    role: UserRole = UserRole.USER


class UserUpdate(BaseModel):
    """用户更新模式"""
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    language: Optional[str] = None
    theme: Optional[str] = None
    timezone: Optional[str] = None
    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    bio: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    avatar_url: Optional[str] = None


class UserInDBBase(UserBase):
    """数据库中的用户基础模式"""
    id: int
    role: UserRole
    is_active: bool
    is_verified: bool
    avatar_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class User(UserInDBBase):
    """用户输出模式"""
    pass


class UserInDB(UserInDBBase):
    """数据库中的用户模式（包含密码哈希）"""
    hashed_password: str
