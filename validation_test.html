<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源验证功能测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5; line-height: 1.6; padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px;
            text-align: center;
        }
        .card { 
            background: white; border-radius: 12px; padding: 25px; margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px; }
        .btn { 
            background: #667eea; color: white; border: none; padding: 12px 24px;
            border-radius: 8px; cursor: pointer; margin: 8px; font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .btn-danger { background: #ff4757; }
        .btn-success { background: #2ed573; }
        .btn-warning { background: #ffa502; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; padding: 12px; border: 2px solid #e1e8ed; border-radius: 8px;
            font-size: 14px; transition: border-color 0.3s ease;
        }
        .form-group input:focus, .form-group select:focus { 
            border-color: #667eea; outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-group input.error { border-color: #ff4757; box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1); }
        .form-group input.success { border-color: #2ed573; box-shadow: 0 0 0 3px rgba(46, 213, 115, 0.1); }
        .field-error { color: #ff4757; font-size: 12px; margin-top: 5px; }
        .required { color: #ff4757; }
        .result { 
            background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;
            font-family: 'Courier New', monospace; font-size: 13px; max-height: 400px; overflow-y: auto;
            border-left: 4px solid #667eea;
        }
        .success { color: #2ed573; font-weight: 600; }
        .error { color: #ff4757; font-weight: 600; }
        .info { color: #3742fa; font-weight: 600; }
        .driver-info { 
            background: #e8f4fd; padding: 15px; border-radius: 8px; margin: 10px 0;
            border-left: 4px solid #3742fa;
        }
        .feature-tag { 
            display: inline-block; background: #667eea; color: white; padding: 4px 8px;
            border-radius: 4px; font-size: 12px; margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 数据源验证功能测试</h1>
            <p>完整的连接验证流程演示</p>
            <div style="margin-top: 15px;">
                <span class="feature-tag">必填验证</span>
                <span class="feature-tag">连接测试</span>
                <span class="feature-tag">智能按钮</span>
                <span class="feature-tag">错误提示</span>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📝 基本信息</h3>
                <div class="form-group">
                    <label>数据源名称 <span class="required">*</span>:</label>
                    <input type="text" id="dsName" placeholder="例如: 测试SQLite数据库" 
                           onchange="validateField('dsName', true)" onblur="validateField('dsName', true)" />
                    <div id="dsNameError" class="field-error" style="display: none;"></div>
                </div>
                <div class="form-group">
                    <label>描述:</label>
                    <textarea id="dsDescription" placeholder="数据源用途描述"></textarea>
                </div>
                <div class="form-group">
                    <label>数据库类型 <span class="required">*</span>:</label>
                    <select id="dsType" onchange="updateConnectionForm()">
                        <option value="">请选择数据库类型</option>
                        <option value="sqlite">SQLite (推荐测试)</option>
                        <option value="postgresql">PostgreSQL</option>
                        <option value="mysql">MySQL</option>
                        <option value="mssql">SQL Server</option>
                        <option value="mongodb">MongoDB</option>
                        <option value="redis">Redis</option>
                    </select>
                </div>
            </div>

            <div class="card">
                <h3>🔗 连接配置</h3>
                <div id="connectionForm">
                    <p class="info">请先选择数据库类型</p>
                </div>
                <div style="margin-top: 20px;">
                    <button class="btn" onclick="testConnection()" id="testConnBtn" disabled>
                        🔍 测试连接
                    </button>
                    <button class="btn btn-success" onclick="createDataSource()" id="createBtn" disabled>
                        ✅ 创建数据源
                    </button>
                    <button class="btn btn-warning" onclick="resetForm()">
                        🔄 重置表单
                    </button>
                </div>
                <div id="connectionTestResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="card">
            <h3>📋 操作日志</h3>
            <div id="operationLog" class="result"></div>
        </div>
    </div>

    <script>
        // 数据库类型配置
        const dbConfigs = {
            'sqlite': {
                required_fields: ['database'],
                default_values: { database: 'C:\\Users\\<USER>\\test\\test\\test_database.db' },
                driver: '内置sqlite3模块',
                features: ['嵌入式数据库', 'ACID事务', 'JSON支持']
            },
            'postgresql': {
                required_fields: ['host', 'port', 'database', 'username', 'password'],
                default_values: { host: 'localhost', port: 5432, database: 'postgres', username: 'postgres' },
                driver: 'psycopg2-binary',
                features: ['ACID事务', 'JSON支持', '全文搜索']
            },
            'mysql': {
                required_fields: ['host', 'port', 'database', 'username', 'password'],
                default_values: { host: 'localhost', port: 3306, database: 'mysql', username: 'root' },
                driver: 'pymysql',
                features: ['InnoDB引擎', '分区表', 'JSON支持']
            },
            'mssql': {
                required_fields: ['host', 'port', 'database', 'username', 'password'],
                default_values: { host: 'localhost', port: 1433, database: 'master', username: 'sa' },
                driver: 'pyodbc + ODBC Driver 17',
                features: ['T-SQL', '列存储索引', '内存优化表']
            },
            'mongodb': {
                required_fields: ['host', 'port', 'database'],
                optional_fields: ['username', 'password'],
                default_values: { host: 'localhost', port: 27017, database: 'test' },
                driver: 'pymongo',
                features: ['文档存储', '聚合管道', '分片']
            },
            'redis': {
                required_fields: ['host', 'port'],
                optional_fields: ['password'],
                default_values: { host: 'localhost', port: 6379 },
                driver: 'redis-py',
                features: ['键值存储', '发布订阅', 'Lua脚本']
            }
        };

        // 字段标签映射
        const fieldLabels = {
            'host': '主机地址',
            'port': '端口',
            'database': '数据库',
            'username': '用户名',
            'password': '密码'
        };

        // 更新连接表单
        function updateConnectionForm() {
            const type = document.getElementById('dsType').value;
            const formDiv = document.getElementById('connectionForm');
            
            if (!type) {
                formDiv.innerHTML = '<p class="info">请先选择数据库类型</p>';
                updateButtonStates();
                return;
            }

            const config = dbConfigs[type];
            if (!config) {
                formDiv.innerHTML = '<p class="error">不支持的数据库类型</p>';
                return;
            }

            let html = '<div class="driver-info">';
            html += `<strong>驱动:</strong> ${config.driver}<br>`;
            html += `<strong>特性:</strong> ${config.features.join(', ')}`;
            html += '</div>';

            // 必需字段
            config.required_fields.forEach(field => {
                const label = fieldLabels[field] || field;
                const defaultValue = config.default_values[field] || '';
                const inputType = field === 'password' ? 'password' : (field === 'port' ? 'number' : 'text');
                
                html += `
                    <div class="form-group">
                        <label>${label} <span class="required">*</span>:</label>
                        <input type="${inputType}" id="ds${capitalize(field)}" value="${defaultValue}" 
                               onchange="validateField('ds${capitalize(field)}', true)" 
                               onblur="validateField('ds${capitalize(field)}', true)" required />
                        <div id="ds${capitalize(field)}Error" class="field-error" style="display: none;"></div>
                    </div>
                `;
            });

            // 可选字段
            if (config.optional_fields) {
                config.optional_fields.forEach(field => {
                    const label = fieldLabels[field] || field;
                    const inputType = field === 'password' ? 'password' : (field === 'port' ? 'number' : 'text');
                    
                    html += `
                        <div class="form-group">
                            <label>${label}:</label>
                            <input type="${inputType}" id="ds${capitalize(field)}" 
                                   onchange="validateField('ds${capitalize(field)}', false)" 
                                   onblur="validateField('ds${capitalize(field)}', false)" />
                            <div id="ds${capitalize(field)}Error" class="field-error" style="display: none;"></div>
                        </div>
                    `;
                });
            }

            formDiv.innerHTML = html;
            updateButtonStates();
        }

        // 首字母大写
        function capitalize(str) {
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // 验证字段
        function validateField(fieldId, isRequired) {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + 'Error');
            
            if (!field || !errorDiv) return true;
            
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';
            
            // 必填字段验证
            if (isRequired && !value) {
                isValid = false;
                errorMessage = '此字段为必填项';
            }
            
            // 端口号验证
            if (fieldId.toLowerCase().includes('port') && value) {
                const port = parseInt(value);
                if (isNaN(port) || port < 1 || port > 65535) {
                    isValid = false;
                    errorMessage = '端口号必须在1-65535之间';
                }
            }
            
            // 主机地址验证
            if (fieldId.toLowerCase().includes('host') && value) {
                const hostRegex = /^[a-zA-Z0-9.-]+$/;
                if (!hostRegex.test(value)) {
                    isValid = false;
                    errorMessage = '请输入有效的主机地址';
                }
            }
            
            // 更新UI状态
            if (isValid) {
                field.classList.remove('error');
                field.classList.add('success');
                errorDiv.style.display = 'none';
            } else {
                field.classList.remove('success');
                field.classList.add('error');
                errorDiv.textContent = errorMessage;
                errorDiv.style.display = 'block';
            }
            
            updateButtonStates();
            return isValid;
        }

        // 更新按钮状态
        function updateButtonStates() {
            const testBtn = document.getElementById('testConnBtn');
            const createBtn = document.getElementById('createBtn');
            
            const type = document.getElementById('dsType').value;
            const name = document.getElementById('dsName').value.trim();
            
            // 测试连接按钮
            if (type && name && hasMinimumConnectionInfo()) {
                testBtn.disabled = false;
                testBtn.textContent = '🔍 测试连接';
            } else {
                testBtn.disabled = true;
                testBtn.textContent = '🔍 测试连接';
            }
            
            // 创建按钮
            const connectionTested = testBtn.dataset.tested === 'true';
            if (type && name && connectionTested) {
                createBtn.disabled = false;
                createBtn.textContent = '✅ 创建数据源';
            } else {
                createBtn.disabled = true;
                createBtn.textContent = connectionTested ? '⏳ 请完善信息' : '⏳ 请先测试连接';
            }
        }

        // 检查最基本连接信息
        function hasMinimumConnectionInfo() {
            const type = document.getElementById('dsType').value;
            if (!type) return false;
            
            const config = dbConfigs[type];
            if (!config) return false;
            
            // 检查所有必填字段
            for (let field of config.required_fields) {
                const element = document.getElementById(`ds${capitalize(field)}`);
                if (!element || !element.value.trim()) {
                    return false;
                }
            }
            return true;
        }

        // 模拟测试连接
        function testConnection() {
            const type = document.getElementById('dsType').value;
            const testBtn = document.getElementById('testConnBtn');
            const resultDiv = document.getElementById('connectionTestResult');
            
            if (!type) {
                log('❌ 请先选择数据库类型');
                return;
            }
            
            // 更新按钮状态
            testBtn.disabled = true;
            testBtn.textContent = '🔄 正在测试...';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<span class="info">🔄 正在连接数据库，请稍候...</span>';
            
            // 模拟连接测试
            setTimeout(() => {
                const config = dbConfigs[type];
                
                // 模拟成功连接
                testBtn.dataset.tested = 'true';
                testBtn.textContent = '✅ 连接成功';
                testBtn.className = 'btn btn-success';
                
                let html = `<span class="success">✅ ${type.toUpperCase()}连接成功</span><br>`;
                html += '<div style="margin-top: 10px;"><strong>连接详情:</strong><br>';
                html += `<span class="info">驱动:</span> ${config.driver}<br>`;
                html += `<span class="info">特性:</span> ${config.features.join(', ')}<br>`;
                html += '</div>';
                
                resultDiv.innerHTML = html;
                
                log(`✅ ${type.toUpperCase()}连接测试成功`);
                
                testBtn.disabled = false;
                updateButtonStates();
            }, 2000);
        }

        // 模拟创建数据源
        function createDataSource() {
            const name = document.getElementById('dsName').value;
            const description = document.getElementById('dsDescription').value;
            const type = document.getElementById('dsType').value;

            if (!name || !type) {
                log('❌ 请填写数据源名称和类型');
                return;
            }
            
            const testBtn = document.getElementById('testConnBtn');
            if (testBtn.dataset.tested !== 'true') {
                log('❌ 请先通过连接测试');
                return;
            }

            // 模拟创建成功
            log(`✅ 创建成功: ${name} (类型: ${type})`);
            
            // 显示成功消息
            const successMsg = document.createElement('div');
            successMsg.style.cssText = 'background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 15px 0;';
            successMsg.innerHTML = `
                <strong>🎉 数据源创建成功！</strong><br>
                名称: ${name}<br>
                类型: ${type}<br>
                状态: connected<br>
                <small>这是一个演示，实际环境中会保存到数据库</small>
            `;
            
            const logDiv = document.getElementById('operationLog');
            logDiv.insertBefore(successMsg, logDiv.firstChild);
            
            // 重置表单
            resetForm();
            
            // 5秒后移除成功消息
            setTimeout(() => {
                if (successMsg.parentNode) {
                    successMsg.parentNode.removeChild(successMsg);
                }
            }, 5000);
        }

        // 重置表单
        function resetForm() {
            document.getElementById('dsName').value = '';
            document.getElementById('dsDescription').value = '';
            document.getElementById('dsType').value = '';
            
            const connectionForm = document.getElementById('connectionForm');
            connectionForm.innerHTML = '<p class="info">请先选择数据库类型</p>';
            
            const testBtn = document.getElementById('testConnBtn');
            const createBtn = document.getElementById('createBtn');
            
            testBtn.disabled = true;
            testBtn.textContent = '🔍 测试连接';
            testBtn.className = 'btn';
            testBtn.dataset.tested = 'false';
            
            createBtn.disabled = true;
            createBtn.textContent = '✅ 创建数据源';
            createBtn.className = 'btn btn-success';
            
            const resultDiv = document.getElementById('connectionTestResult');
            resultDiv.style.display = 'none';
            resultDiv.innerHTML = '';
            
            log('📝 表单已重置');
        }

        // 记录日志
        function log(message) {
            const logDiv = document.getElementById('operationLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML = `[${timestamp}] ${message}<br>` + logDiv.innerHTML;
        }

        // 页面加载完成
        window.onload = function() {
            log('🚀 数据源验证功能测试页面加载完成');
            log('💡 请选择SQLite进行快速测试');
            updateButtonStates();
        };
    </script>
</body>
</html>
