#!/usr/bin/env python3
"""
项目状态检查脚本

检查项目文件完整性和功能状态
"""

import os
from pathlib import Path
import json

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🗄️ 高级数据源管理平台 - 项目状态检查")
    print("=" * 60)
    print()

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    path = Path(filepath)
    if path.exists():
        size = path.stat().st_size
        print(f"✅ {description}")
        print(f"   📁 {filepath} ({size:,} bytes)")
        return True
    else:
        print(f"❌ {description}")
        print(f"   📁 {filepath} (文件不存在)")
        return False

def check_core_files():
    """检查核心文件"""
    print("📋 核心文件检查:")
    print("-" * 40)
    
    files = [
        ("advanced_datasource_manager.html", "高级数据源管理平台"),
        ("validation_test.html", "验证功能测试页面"),
        ("start_project.py", "项目启动脚本"),
        ("project_status.py", "项目状态检查脚本"),
    ]
    
    total = len(files)
    passed = 0
    
    for filepath, description in files:
        if check_file_exists(filepath, description):
            passed += 1
        print()
    
    print(f"📊 核心文件完整性: {passed}/{total} ({passed/total*100:.1f}%)")
    return passed == total

def check_documentation():
    """检查文档文件"""
    print("\n📚 文档文件检查:")
    print("-" * 40)
    
    docs = [
        ("高级数据源管理功能指南.md", "高级数据源管理功能指南"),
        ("数据源验证功能指南.md", "数据源验证功能指南"),
        ("数据源驱动测试指南.md", "数据源驱动测试指南"),
    ]
    
    total = len(docs)
    passed = 0
    
    for filepath, description in docs:
        if check_file_exists(filepath, description):
            passed += 1
        print()
    
    print(f"📊 文档完整性: {passed}/{total} ({passed/total*100:.1f}%)")
    return passed == total

def check_backend_structure():
    """检查后端结构"""
    print("\n🔧 后端结构检查:")
    print("-" * 40)
    
    backend_files = [
        ("backend/app/main.py", "FastAPI主应用"),
        ("backend/app/services/database_factory.py", "数据库工厂"),
        ("backend/app/db/models/data_source.py", "数据源模型"),
        ("backend/requirements.txt", "Python依赖"),
    ]
    
    total = len(backend_files)
    passed = 0
    
    for filepath, description in backend_files:
        if check_file_exists(filepath, description):
            passed += 1
        print()
    
    print(f"📊 后端结构完整性: {passed}/{total} ({passed/total*100:.1f}%)")
    return passed == total

def check_test_data():
    """检查测试数据"""
    print("\n🧪 测试数据检查:")
    print("-" * 40)
    
    test_files = [
        ("test_database.db", "SQLite测试数据库"),
        ("create_test_database.py", "测试数据库创建脚本"),
    ]
    
    total = len(test_files)
    passed = 0
    
    for filepath, description in test_files:
        if check_file_exists(filepath, description):
            passed += 1
        print()
    
    print(f"📊 测试数据完整性: {passed}/{total} ({passed/total*100:.1f}%)")
    return passed == total

def analyze_html_features():
    """分析HTML文件功能"""
    print("\n🔍 功能分析:")
    print("-" * 40)
    
    # 分析高级数据源管理平台
    advanced_path = Path("advanced_datasource_manager.html")
    if advanced_path.exists():
        content = advanced_path.read_text(encoding='utf-8')
        
        features = {
            "数据库类型支持": ["sqlite", "postgresql", "mysql", "mssql", "mongodb", "redis", "oracle"],
            "核心功能": ["创建数据源", "编辑数据源", "删除数据源", "测试连接", "批量操作"],
            "验证功能": ["字段验证", "连接验证", "实时验证", "错误提示"],
            "界面特性": ["响应式设计", "模态框", "动画效果", "状态指示"]
        }
        
        print("✅ 高级数据源管理平台功能:")
        for category, items in features.items():
            found_items = [item for item in items if item.lower().replace(" ", "") in content.lower().replace(" ", "")]
            print(f"   📋 {category}: {len(found_items)}/{len(items)} 项")
            for item in found_items:
                print(f"      ✓ {item}")
        print()
    
    # 分析验证测试页面
    validation_path = Path("validation_test.html")
    if validation_path.exists():
        content = validation_path.read_text(encoding='utf-8')
        
        validation_features = [
            "必填验证", "格式验证", "实时验证", "错误提示", 
            "成功提示", "按钮状态", "表单重置"
        ]
        
        found_features = [f for f in validation_features if f.lower().replace(" ", "") in content.lower().replace(" ", "")]
        
        print("✅ 验证功能测试页面:")
        print(f"   📋 验证功能: {len(found_features)}/{len(validation_features)} 项")
        for feature in found_features:
            print(f"      ✓ {feature}")
        print()

def generate_project_summary():
    """生成项目总结"""
    print("\n📊 项目总结:")
    print("-" * 40)
    
    # 统计文件数量
    html_files = list(Path(".").glob("*.html"))
    py_files = list(Path(".").glob("*.py"))
    md_files = list(Path(".").glob("*.md"))
    
    print(f"📁 HTML文件: {len(html_files)} 个")
    for f in html_files:
        print(f"   • {f.name}")
    
    print(f"\n🐍 Python文件: {len(py_files)} 个")
    for f in py_files:
        print(f"   • {f.name}")
    
    print(f"\n📚 文档文件: {len(md_files)} 个")
    for f in md_files:
        print(f"   • {f.name}")
    
    # 计算总体完成度
    total_checks = 4  # 核心文件、文档、后端、测试数据
    passed_checks = 0
    
    if check_core_files():
        passed_checks += 1
    if check_documentation():
        passed_checks += 1
    if check_backend_structure():
        passed_checks += 1
    if check_test_data():
        passed_checks += 1
    
    completion = passed_checks / total_checks * 100
    
    print(f"\n🎯 项目完成度: {completion:.1f}%")
    
    if completion >= 90:
        print("🎉 项目状态: 优秀")
    elif completion >= 70:
        print("✅ 项目状态: 良好")
    elif completion >= 50:
        print("⚠️ 项目状态: 需要改进")
    else:
        print("❌ 项目状态: 需要大量工作")

def show_quick_start():
    """显示快速开始指南"""
    print("\n🚀 快速开始:")
    print("-" * 40)
    print("1. 运行启动脚本:")
    print("   python start_project.py")
    print()
    print("2. 选择启动方式:")
    print("   [1] 高级数据源管理平台 (推荐)")
    print("   [2] 验证功能测试页面")
    print()
    print("3. 直接访问:")
    print("   高级平台: file:///C:/Users/<USER>/test/test/advanced_datasource_manager.html")
    print("   验证测试: file:///C:/Users/<USER>/test/test/validation_test.html")
    print()
    print("💡 建议从SQLite数据库开始测试！")

def main():
    """主函数"""
    print_header()
    
    # 执行各项检查
    check_core_files()
    check_documentation()
    check_backend_structure()
    check_test_data()
    
    # 功能分析
    analyze_html_features()
    
    # 项目总结
    generate_project_summary()
    
    # 快速开始
    show_quick_start()
    
    print("\n" + "=" * 60)
    print("✅ 项目状态检查完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
