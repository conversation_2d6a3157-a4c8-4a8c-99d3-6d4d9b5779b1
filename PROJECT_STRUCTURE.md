# 项目结构说明

## 📁 项目目录结构

```
bi_platform/
├── backend/                 # 后端代码
│   ├── app/                # 应用核心代码
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── db/            # 数据库模型
│   │   ├── schemas/       # Pydantic模式
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── requirements.txt   # Python依赖
│   ├── Dockerfile        # Docker配置
│   └── pyproject.toml    # Python项目配置
├── frontend/              # 前端代码
│   ├── src/              # 源代码
│   │   ├── components/   # UI组件
│   │   ├── pages/        # 页面组件
│   │   ├── services/     # API服务
│   │   ├── store/        # Redux状态管理
│   │   ├── types/        # TypeScript类型
│   │   └── utils/        # 工具函数
│   ├── package.json      # Node.js依赖
│   ├── vite.config.ts    # Vite配置
│   ├── tsconfig.json     # TypeScript配置
│   ├── Dockerfile        # Docker配置
│   └── nginx.conf        # Nginx配置
├── docs/                 # 项目文档
│   ├── development_summary.md      # 开发总结
│   ├── project_specification.md    # 项目规范
│   └── requirements_specification.md # 需求规格
├── docker-compose.yml    # Docker Compose配置
├── init.sql             # 数据库初始化脚本
├── start.sh             # Linux/Mac启动脚本
├── start.bat            # Windows启动脚本
├── start.ps1            # PowerShell启动脚本
├── README.md            # 项目说明
├── GETTING_STARTED.md   # 快速开始指南
├── PROJECT_STRUCTURE.md # 项目结构说明（本文件）
└── LICENSE              # 许可证
```

## 🚀 启动方式

### Windows用户
```cmd
start.bat          # 批处理脚本
```
或
```powershell
.\start.ps1         # PowerShell脚本
```

### Linux/Mac用户
```bash
chmod +x start.sh
./start.sh          # Bash脚本
```

## 📋 核心文件说明

### 后端核心文件
- `backend/app/main.py` - FastAPI应用入口
- `backend/app/core/config.py` - 应用配置
- `backend/app/db/base.py` - 数据库配置
- `backend/app/api/api_v1/` - API路由定义
- `backend/requirements.txt` - Python依赖包

### 前端核心文件
- `frontend/src/main.tsx` - React应用入口
- `frontend/src/App.tsx` - 主应用组件
- `frontend/src/store/index.ts` - Redux store配置
- `frontend/package.json` - Node.js依赖包
- `frontend/vite.config.ts` - 构建配置

### 配置文件
- `docker-compose.yml` - 容器编排配置
- `init.sql` - 数据库初始化脚本
- `backend/.env.example` - 后端环境变量模板
- `frontend/.eslintrc.js` - 前端代码规范配置

## 🔧 开发环境

### 后端开发
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

## 📚 文档说明

- `README.md` - 项目总体介绍和快速开始
- `GETTING_STARTED.md` - 详细的启动指南
- `docs/development_summary.md` - 开发过程总结
- `docs/project_specification.md` - 项目技术规范
- `docs/requirements_specification.md` - 需求规格说明

## 🎯 主要功能模块

### 后端模块
1. **用户认证** (`app/api/api_v1/endpoints/auth.py`)
2. **用户管理** (`app/api/api_v1/endpoints/users.py`)
3. **数据源管理** (预留)
4. **自然语言分析** (预留)
5. **数据可视化** (预留)

### 前端模块
1. **认证页面** (`src/pages/Auth/`)
2. **仪表板** (`src/pages/Dashboard/`)
3. **数据源管理** (`src/pages/DataSources/`)
4. **自然语言分析** (`src/pages/Analysis/`)
5. **数据可视化** (`src/pages/Visualization/`)
6. **数字化大屏** (`src/pages/BigScreen/`)
7. **系统设置** (`src/pages/Settings/`)

## 🔄 开发工作流

1. **启动开发环境**: 使用提供的启动脚本
2. **修改代码**: 在对应的模块目录下修改
3. **测试功能**: 访问前端页面或API文档
4. **提交代码**: 使用Git进行版本控制

## 📦 部署说明

### Docker部署（推荐）
```bash
docker-compose up -d --build
```

### 手动部署
1. 部署后端：配置Python环境，安装依赖，启动服务
2. 部署前端：构建React应用，配置Nginx
3. 配置数据库：PostgreSQL + Redis

## 🆘 获取帮助

- 查看 `README.md` 了解项目概况
- 查看 `GETTING_STARTED.md` 获取启动帮助
- 查看 `docs/` 目录获取详细文档
- 检查日志文件排查问题
