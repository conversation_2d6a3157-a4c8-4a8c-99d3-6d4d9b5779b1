"""
认证中间件

处理API请求的用户认证。
"""

from typing import Optional
from fastapi import HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi import Request
from jose import JWTError, jwt

from app.core.config import settings
from app.core.security import verify_token


class AuthMiddleware:
    """认证中间件类"""
    
    def __init__(self):
        self.security = HTTPBearer()
    
    async def verify_token(self, request: Request) -> Optional[dict]:
        """
        验证请求中的JWT令牌。
        
        Args:
            request: FastAPI请求对象
            
        Returns:
            Optional[dict]: 用户信息或None
            
        Raises:
            HTTPException: 认证失败时
        """
        # 检查是否是公开端点
        if self._is_public_endpoint(request.url.path):
            return None
        
        # 获取Authorization头
        authorization = request.headers.get("Authorization")
        if not authorization:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="缺少认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 解析Bearer令牌
        try:
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的认证方案",
                    headers={"WWW-Authenticate": "Bearer"},
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证格式",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 验证JWT令牌
        try:
            payload = jwt.decode(
                token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
            )
            user_id = payload.get("sub")
            if user_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的令牌",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            return {
                "user_id": int(user_id),
                "token": token,
                "payload": payload
            }
            
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌验证失败",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    def _is_public_endpoint(self, path: str) -> bool:
        """
        检查是否是公开端点（不需要认证）。
        
        Args:
            path: 请求路径
            
        Returns:
            bool: 是否是公开端点
        """
        public_endpoints = [
            "/",
            "/health",
            "/api/v1/health",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/docs",
            "/api/v1/redoc",
            "/api/v1/openapi.json",
            "/favicon.ico"
        ]
        
        # 检查精确匹配
        if path in public_endpoints:
            return True
        
        # 检查路径前缀
        public_prefixes = [
            "/docs",
            "/redoc",
            "/static"
        ]
        
        for prefix in public_prefixes:
            if path.startswith(prefix):
                return True
        
        return False
    
    def require_role(self, required_roles: list) -> callable:
        """
        创建角色检查装饰器。
        
        Args:
            required_roles: 需要的角色列表
            
        Returns:
            callable: 装饰器函数
        """
        def decorator(func):
            async def wrapper(*args, **kwargs):
                # 这里应该从数据库获取用户角色信息
                # 暂时返回原函数
                return await func(*args, **kwargs)
            return wrapper
        return decorator


# 全局认证中间件实例
auth_middleware = AuthMiddleware()
