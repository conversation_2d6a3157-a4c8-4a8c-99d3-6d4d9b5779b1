#!/bin/bash

# 自然语言BI数据分析平台启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🚀 自然语言BI数据分析平台启动脚本" $BLUE
print_message "=================================" $BLUE

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    print_message "❌ Docker未安装，请先安装Docker" $RED
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    print_message "❌ Docker Compose未安装，请先安装Docker Compose" $RED
    exit 1
fi

# 检查.env文件是否存在
if [ ! -f "backend/.env" ]; then
    print_message "📝 创建后端环境配置文件..." $YELLOW
    cp backend/.env.example backend/.env
    print_message "✅ 已创建 backend/.env 文件，请根据需要修改配置" $GREEN
fi

# 停止现有容器
print_message "🛑 停止现有容器..." $YELLOW
docker-compose down

# 构建并启动服务
print_message "🔨 构建并启动服务..." $YELLOW
docker-compose up -d --build

# 等待服务启动
print_message "⏳ 等待服务启动..." $YELLOW
sleep 10

# 检查服务状态
print_message "🔍 检查服务状态..." $YELLOW
docker-compose ps

# 等待数据库就绪
print_message "⏳ 等待数据库就绪..." $YELLOW
until docker-compose exec -T postgres pg_isready -U postgres; do
    sleep 2
done

# 运行数据库迁移（如果需要）
print_message "🗄️ 运行数据库迁移..." $YELLOW
# docker-compose exec backend alembic upgrade head

print_message "✅ 服务启动完成！" $GREEN
print_message "=================================" $BLUE
print_message "📱 前端地址: http://localhost" $GREEN
print_message "🔧 后端API: http://localhost:8000" $GREEN
print_message "📚 API文档: http://localhost:8000/api/v1/docs" $GREEN
print_message "🗄️ 数据库: localhost:5432" $GREEN
print_message "🔴 Redis: localhost:6379" $GREEN
print_message "=================================" $BLUE

# 显示默认登录信息
print_message "🔑 默认登录信息:" $YELLOW
print_message "   邮箱: <EMAIL>" $YELLOW
print_message "   密码: admin123" $YELLOW
print_message "=================================" $BLUE

# 显示有用的命令
print_message "💡 有用的命令:" $BLUE
print_message "   查看日志: docker-compose logs -f" $BLUE
print_message "   停止服务: docker-compose down" $BLUE
print_message "   重启服务: docker-compose restart" $BLUE
print_message "   进入后端: docker-compose exec backend bash" $BLUE
print_message "   进入数据库: docker-compose exec postgres psql -U postgres -d bi_platform" $BLUE

print_message "🎉 启动完成，祝您使用愉快！" $GREEN
