<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级数据源管理平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f8fafc; line-height: 1.6; color: #334155;
        }
        .container { max-width: 1600px; margin: 0 auto; padding: 20px; }
        
        /* 头部样式 */
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 30px; border-radius: 16px; margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        
        /* 卡片样式 */
        .card { 
            background: white; border-radius: 16px; padding: 30px; margin: 20px 0;
            box-shadow: 0 4px 25px rgba(0,0,0,0.08); border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        .card:hover { transform: translateY(-2px); box-shadow: 0 8px 35px rgba(0,0,0,0.12); }
        
        /* 按钮样式 */
        .btn { 
            background: #667eea; color: white; border: none; padding: 12px 24px;
            border-radius: 10px; cursor: pointer; margin: 6px; font-size: 14px;
            font-weight: 600; transition: all 0.3s ease; display: inline-flex;
            align-items: center; gap: 8px; text-decoration: none;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
        .btn:disabled { background: #cbd5e1; cursor: not-allowed; transform: none; }
        .btn-primary { background: #3b82f6; }
        .btn-primary:hover { background: #2563eb; }
        .btn-success { background: #10b981; }
        .btn-success:hover { background: #059669; }
        .btn-danger { background: #ef4444; }
        .btn-danger:hover { background: #dc2626; }
        .btn-warning { background: #f59e0b; }
        .btn-warning:hover { background: #d97706; }
        .btn-secondary { background: #6b7280; }
        .btn-secondary:hover { background: #4b5563; }
        .btn-sm { padding: 8px 16px; font-size: 12px; }
        .btn-lg { padding: 16px 32px; font-size: 16px; }
        
        /* 表单样式 */
        .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .form-group { margin: 20px 0; }
        .form-group label { 
            display: block; margin-bottom: 8px; font-weight: 600; color: #374151;
            font-size: 14px;
        }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; padding: 14px 16px; border: 2px solid #e5e7eb; border-radius: 10px;
            font-size: 14px; transition: all 0.3s ease; background: #fafafa;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { 
            border-color: #667eea; outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        .form-group input.error { border-color: #ef4444; background: #fef2f2; }
        .form-group input.success { border-color: #10b981; background: #f0fdf4; }
        .form-group input.warning { border-color: #f59e0b; background: #fffbeb; }
        
        /* 验证提示样式 */
        .field-error { color: #ef4444; font-size: 12px; margin-top: 6px; display: flex; align-items: center; gap: 4px; }
        .field-success { color: #10b981; font-size: 12px; margin-top: 6px; display: flex; align-items: center; gap: 4px; }
        .field-warning { color: #f59e0b; font-size: 12px; margin-top: 6px; display: flex; align-items: center; gap: 4px; }
        .required { color: #ef4444; }
        
        /* 标签和状态 */
        .badge { 
            display: inline-flex; align-items: center; gap: 4px; padding: 4px 12px;
            border-radius: 20px; font-size: 12px; font-weight: 600;
        }
        .badge-success { background: #dcfce7; color: #166534; }
        .badge-danger { background: #fee2e2; color: #991b1b; }
        .badge-warning { background: #fef3c7; color: #92400e; }
        .badge-info { background: #dbeafe; color: #1e40af; }
        .badge-secondary { background: #f1f5f9; color: #475569; }
        
        /* 数据源列表样式 */
        .datasource-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px; }
        .datasource-item { 
            background: white; border: 2px solid #e5e7eb; border-radius: 16px; padding: 24px;
            transition: all 0.3s ease; position: relative; overflow: hidden;
        }
        .datasource-item:hover { border-color: #667eea; transform: translateY(-2px); }
        .datasource-item.editing { border-color: #f59e0b; background: #fffbeb; }
        .datasource-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px; }
        .datasource-title { font-size: 18px; font-weight: 700; color: #1f2937; margin-bottom: 4px; }
        .datasource-type { font-size: 14px; color: #6b7280; }
        .datasource-actions { display: flex; gap: 8px; flex-wrap: wrap; }
        .datasource-details { margin: 16px 0; }
        .datasource-meta { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 12px; margin-top: 16px; }
        .meta-item { text-align: center; padding: 12px; background: #f8fafc; border-radius: 8px; }
        .meta-label { font-size: 12px; color: #6b7280; margin-bottom: 4px; }
        .meta-value { font-size: 14px; font-weight: 600; color: #374151; }
        
        /* 模态框样式 */
        .modal { 
            display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 1000; backdrop-filter: blur(4px);
        }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { 
            background: white; border-radius: 20px; padding: 40px; max-width: 800px; width: 90%;
            max-height: 90vh; overflow-y: auto; box-shadow: 0 25px 50px rgba(0,0,0,0.25);
        }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
        .modal-title { font-size: 24px; font-weight: 700; color: #1f2937; }
        .modal-close { 
            background: none; border: none; font-size: 24px; cursor: pointer;
            color: #6b7280; padding: 8px; border-radius: 8px;
        }
        .modal-close:hover { background: #f3f4f6; color: #374151; }
        
        /* 连接测试样式 */
        .connection-test { 
            background: #f8fafc; border: 2px solid #e5e7eb; border-radius: 12px; 
            padding: 20px; margin: 20px 0;
        }
        .connection-test.testing { border-color: #3b82f6; background: #eff6ff; }
        .connection-test.success { border-color: #10b981; background: #f0fdf4; }
        .connection-test.error { border-color: #ef4444; background: #fef2f2; }
        
        /* 进度条 */
        .progress { 
            width: 100%; height: 8px; background: #e5e7eb; border-radius: 4px; 
            overflow: hidden; margin: 16px 0;
        }
        .progress-bar { 
            height: 100%; background: #667eea; border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        /* 工具提示 */
        .tooltip { position: relative; display: inline-block; }
        .tooltip .tooltiptext {
            visibility: hidden; width: 200px; background: #1f2937; color: white;
            text-align: center; border-radius: 8px; padding: 8px 12px; font-size: 12px;
            position: absolute; z-index: 1; bottom: 125%; left: 50%;
            margin-left: -100px; opacity: 0; transition: opacity 0.3s;
        }
        .tooltip:hover .tooltiptext { visibility: visible; opacity: 1; }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .form-grid { grid-template-columns: 1fr; }
            .datasource-grid { grid-template-columns: 1fr; }
            .modal-content { padding: 20px; }
        }
        
        /* 动画 */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .slide-in { animation: slideIn 0.3s ease-out; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .pulse { animation: pulse 2s infinite; }
        
        /* 操作日志 */
        .log-container { 
            background: #1f2937; color: #e5e7eb; border-radius: 12px; 
            padding: 20px; font-family: 'Courier New', monospace; font-size: 13px;
            max-height: 300px; overflow-y: auto;
        }
        .log-entry { margin: 4px 0; padding: 4px 0; }
        .log-timestamp { color: #9ca3af; }
        .log-success { color: #34d399; }
        .log-error { color: #f87171; }
        .log-warning { color: #fbbf24; }
        .log-info { color: #60a5fa; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header slide-in">
            <h1>🗄️ 高级数据源管理平台</h1>
            <p>企业级数据源管理解决方案 - 支持创建、编辑、验证、监控</p>
            <div style="margin-top: 20px;">
                <span class="badge badge-info">✨ 实时验证</span>
                <span class="badge badge-info">🔄 在线编辑</span>
                <span class="badge badge-info">🔍 连接监控</span>
                <span class="badge badge-info">📊 状态统计</span>
                <span class="badge badge-info">🛡️ 安全加密</span>
            </div>
        </div>

        <!-- 操作面板 -->
        <div class="card slide-in">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;">
                <div>
                    <h2 style="margin-bottom: 8px;">数据源管理</h2>
                    <p style="color: #6b7280;">管理您的所有数据源连接</p>
                </div>
                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <button class="btn btn-primary btn-lg" onclick="openCreateModal()">
                        ➕ 创建数据源
                    </button>
                    <button class="btn btn-secondary" onclick="refreshDataSources()">
                        🔄 刷新列表
                    </button>
                    <button class="btn btn-secondary" onclick="testAllConnections()">
                        🔍 批量测试
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计面板 -->
        <div class="form-grid slide-in">
            <div class="card">
                <div class="meta-item">
                    <div class="meta-label">总数据源</div>
                    <div class="meta-value" id="totalCount">0</div>
                </div>
            </div>
            <div class="card">
                <div class="meta-item">
                    <div class="meta-label">在线数据源</div>
                    <div class="meta-value" style="color: #10b981;" id="onlineCount">0</div>
                </div>
            </div>
            <div class="card">
                <div class="meta-item">
                    <div class="meta-label">离线数据源</div>
                    <div class="meta-value" style="color: #ef4444;" id="offlineCount">0</div>
                </div>
            </div>
            <div class="card">
                <div class="meta-item">
                    <div class="meta-label">数据库类型</div>
                    <div class="meta-value" id="typeCount">0</div>
                </div>
            </div>
        </div>

        <!-- 数据源列表 -->
        <div class="card slide-in">
            <h3 style="margin-bottom: 20px;">数据源列表</h3>
            <div id="dataSourceGrid" class="datasource-grid">
                <!-- 数据源项目将在这里动态生成 -->
            </div>
            <div id="emptyState" style="text-align: center; padding: 60px 20px; color: #6b7280; display: none;">
                <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
                <h3 style="margin-bottom: 8px;">暂无数据源</h3>
                <p>点击"创建数据源"按钮开始添加您的第一个数据源</p>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="card slide-in">
            <h3 style="margin-bottom: 20px;">操作日志</h3>
            <div id="operationLog" class="log-container"></div>
        </div>
    </div>

    <!-- 创建/编辑数据源模态框 -->
    <div id="dataSourceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">创建数据源</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            
            <form id="dataSourceForm" onsubmit="return false;">
                <input type="hidden" id="editingId" value="">
                
                <!-- 基本信息 -->
                <div class="form-group">
                    <label>数据源名称 <span class="required">*</span></label>
                    <input type="text" id="dsName" placeholder="例如: 生产环境MySQL" required>
                    <div id="dsNameError" class="field-error" style="display: none;"></div>
                    <div id="dsNameSuccess" class="field-success" style="display: none;"></div>
                </div>
                
                <div class="form-group">
                    <label>描述</label>
                    <textarea id="dsDescription" placeholder="详细描述数据源的用途和特点" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label>数据库类型 <span class="required">*</span></label>
                    <select id="dsType" onchange="updateConnectionForm()" required>
                        <option value="">请选择数据库类型</option>
                        <option value="sqlite">SQLite - 嵌入式数据库</option>
                        <option value="postgresql">PostgreSQL - 开源关系型数据库</option>
                        <option value="mysql">MySQL - 流行的关系型数据库</option>
                        <option value="mssql">SQL Server - 微软企业级数据库</option>
                        <option value="mongodb">MongoDB - 文档型NoSQL数据库</option>
                        <option value="redis">Redis - 内存键值数据库</option>
                        <option value="oracle">Oracle - 企业级数据库</option>
                    </select>
                </div>
                
                <!-- 连接配置 -->
                <div id="connectionConfig">
                    <h4 style="margin: 30px 0 20px 0; color: #374151;">连接配置</h4>
                    <div id="connectionForm">
                        <p style="color: #6b7280; text-align: center; padding: 40px;">请先选择数据库类型</p>
                    </div>
                </div>
                
                <!-- 连接测试 -->
                <div id="connectionTest" class="connection-test" style="display: none;">
                    <h4 style="margin-bottom: 16px;">连接测试</h4>
                    <div id="testProgress" class="progress" style="display: none;">
                        <div class="progress-bar" style="width: 0%;"></div>
                    </div>
                    <div id="testResult"></div>
                    <button type="button" class="btn btn-primary" onclick="testConnection()" id="testBtn">
                        🔍 测试连接
                    </button>
                </div>
                
                <!-- 高级选项 -->
                <div id="advancedOptions" style="display: none;">
                    <h4 style="margin: 30px 0 20px 0; color: #374151;">高级选项</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>连接超时 (秒)</label>
                            <input type="number" id="dsTimeout" value="30" min="5" max="300">
                        </div>
                        <div class="form-group">
                            <label>最大连接数</label>
                            <input type="number" id="dsMaxConnections" value="10" min="1" max="100">
                        </div>
                        <div class="form-group">
                            <label>SSL模式</label>
                            <select id="dsSslMode">
                                <option value="disable">禁用</option>
                                <option value="require">必需</option>
                                <option value="prefer">首选</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>字符编码</label>
                            <select id="dsCharset">
                                <option value="utf8">UTF-8</option>
                                <option value="utf8mb4">UTF-8MB4</option>
                                <option value="latin1">Latin1</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">
                        取消
                    </button>
                    <button type="button" class="btn btn-warning" onclick="toggleAdvancedOptions()" id="advancedBtn">
                        ⚙️ 高级选项
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveDataSource()" id="saveBtn" disabled>
                        💾 保存数据源
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let dataSources = [];
        let editingDataSource = null;
        let connectionTested = false;
        
        // 数据库配置
        const dbConfigs = {
            'sqlite': {
                name: 'SQLite',
                icon: '📁',
                required_fields: ['database'],
                optional_fields: [],
                default_values: { 
                    database: 'C:\\Users\\<USER>\\test\\test\\test_database.db',
                    timeout: 30
                },
                driver: '内置sqlite3模块',
                features: ['嵌入式数据库', 'ACID事务', 'JSON支持', '零配置'],
                port: 0
            },
            'postgresql': {
                name: 'PostgreSQL',
                icon: '🐘',
                required_fields: ['host', 'port', 'database', 'username', 'password'],
                optional_fields: ['schema'],
                default_values: { 
                    host: 'localhost', 
                    port: 5432, 
                    database: 'postgres', 
                    username: 'postgres',
                    timeout: 30,
                    schema: 'public'
                },
                driver: 'psycopg2-binary',
                features: ['ACID事务', 'JSON支持', '全文搜索', '并行查询', '扩展性强'],
                port: 5432
            },
            'mysql': {
                name: 'MySQL',
                icon: '🐬',
                required_fields: ['host', 'port', 'database', 'username', 'password'],
                optional_fields: ['charset'],
                default_values: { 
                    host: 'localhost', 
                    port: 3306, 
                    database: 'mysql', 
                    username: 'root',
                    timeout: 30,
                    charset: 'utf8mb4'
                },
                driver: 'pymysql',
                features: ['InnoDB引擎', '分区表', 'JSON支持', '窗口函数', '高性能'],
                port: 3306
            },
            'mssql': {
                name: 'SQL Server',
                icon: '🏢',
                required_fields: ['host', 'port', 'database', 'username', 'password'],
                optional_fields: ['instance'],
                default_values: { 
                    host: 'localhost', 
                    port: 1433, 
                    database: 'master', 
                    username: 'sa',
                    timeout: 30
                },
                driver: 'pyodbc + ODBC Driver 17',
                features: ['T-SQL', '列存储索引', '内存优化表', 'Always Encrypted'],
                port: 1433
            },
            'mongodb': {
                name: 'MongoDB',
                icon: '🍃',
                required_fields: ['host', 'port', 'database'],
                optional_fields: ['username', 'password', 'authSource'],
                default_values: { 
                    host: 'localhost', 
                    port: 27017, 
                    database: 'test',
                    timeout: 30,
                    authSource: 'admin'
                },
                driver: 'pymongo',
                features: ['文档存储', '聚合管道', '分片', '副本集', '灵活模式'],
                port: 27017
            },
            'redis': {
                name: 'Redis',
                icon: '🔴',
                required_fields: ['host', 'port'],
                optional_fields: ['password', 'database'],
                default_values: { 
                    host: 'localhost', 
                    port: 6379,
                    timeout: 30,
                    database: 0
                },
                driver: 'redis-py',
                features: ['键值存储', '发布订阅', 'Lua脚本', '集群模式', '内存存储'],
                port: 6379
            },
            'oracle': {
                name: 'Oracle',
                icon: '🏛️',
                required_fields: ['host', 'port', 'service_name', 'username', 'password'],
                optional_fields: ['sid'],
                default_values: { 
                    host: 'localhost', 
                    port: 1521, 
                    service_name: 'ORCL', 
                    username: 'system',
                    timeout: 30
                },
                driver: 'cx_Oracle',
                features: ['企业级', 'PL/SQL', '分区', 'RAC集群', '高可用'],
                port: 1521
            }
        };
        
        // 字段标签映射
        const fieldLabels = {
            'host': '主机地址',
            'port': '端口',
            'database': '数据库',
            'username': '用户名',
            'password': '密码',
            'schema': '模式',
            'charset': '字符集',
            'instance': '实例名',
            'authSource': '认证数据库',
            'service_name': '服务名',
            'sid': 'SID'
        };
        
        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            log(`❌ JavaScript错误: ${message} (行 ${lineno})`, 'error');
            console.error('Global error:', { message, source, lineno, colno, error });
            return false;
        };

        window.addEventListener('unhandledrejection', function(event) {
            log(`❌ Promise错误: ${event.reason}`, 'error');
            console.error('Unhandled promise rejection:', event.reason);
        });

        // 页面初始化
        window.onload = function() {
            try {
                log('🚀 高级数据源管理平台启动', 'info');
                loadDataSources();
                updateStatistics();
                log('✅ 平台初始化完成', 'success');
            } catch (error) {
                log(`❌ 平台初始化失败: ${error.message}`, 'error');
                console.error('Initialization error:', error);
            }
        };

        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('operationLog');
            const timestamp = new Date().toLocaleTimeString();
            const icons = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                ${icons[type]} ${message}
            `;

            logContainer.insertBefore(logEntry, logContainer.firstChild);

            // 限制日志条数
            while (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.lastChild);
            }
        }

        // 加载数据源列表
        function loadDataSources() {
            // 模拟数据源数据
            dataSources = [
                {
                    id: 1,
                    name: '生产环境MySQL',
                    description: '主要业务数据库，存储用户和订单信息',
                    type: 'mysql',
                    host: 'prod-mysql.company.com',
                    port: 3306,
                    database: 'production',
                    username: 'app_user',
                    status: 'connected',
                    lastTested: new Date(Date.now() - 300000),
                    createdAt: new Date(Date.now() - 86400000 * 30),
                    tables: 156,
                    size: '2.3GB'
                },
                {
                    id: 2,
                    name: '测试SQLite数据库',
                    description: '本地测试数据库，用于开发和调试',
                    type: 'sqlite',
                    database: 'C:\\Users\\<USER>\\test\\test\\test_database.db',
                    status: 'connected',
                    lastTested: new Date(Date.now() - 60000),
                    createdAt: new Date(Date.now() - 86400000 * 7),
                    tables: 5,
                    size: '2.5MB'
                },
                {
                    id: 3,
                    name: 'Redis缓存服务器',
                    description: '会话存储和缓存服务',
                    type: 'redis',
                    host: 'redis.company.com',
                    port: 6379,
                    status: 'disconnected',
                    lastTested: new Date(Date.now() - 3600000),
                    createdAt: new Date(Date.now() - 86400000 * 14),
                    keys: 12450,
                    memory: '256MB'
                }
            ];

            renderDataSources();
            updateStatistics();
            log(`📊 加载了 ${dataSources.length} 个数据源`, 'success');
        }

        // 渲染数据源列表
        function renderDataSources() {
            const grid = document.getElementById('dataSourceGrid');
            const emptyState = document.getElementById('emptyState');

            if (dataSources.length === 0) {
                grid.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            grid.style.display = 'grid';
            emptyState.style.display = 'none';

            grid.innerHTML = dataSources.map(ds => {
                const config = dbConfigs[ds.type];
                const statusBadge = ds.status === 'connected'
                    ? '<span class="badge badge-success">🟢 在线</span>'
                    : '<span class="badge badge-danger">🔴 离线</span>';

                const lastTestedText = ds.lastTested
                    ? `${Math.floor((Date.now() - ds.lastTested.getTime()) / 60000)} 分钟前`
                    : '从未测试';

                return `
                    <div class="datasource-item slide-in" data-id="${ds.id}">
                        <div class="datasource-header">
                            <div>
                                <div class="datasource-title">
                                    ${config.icon} ${ds.name}
                                </div>
                                <div class="datasource-type">${config.name}</div>
                            </div>
                            <div style="text-align: right;">
                                ${statusBadge}
                            </div>
                        </div>

                        <div class="datasource-details">
                            <p style="color: #6b7280; margin-bottom: 12px;">${ds.description}</p>

                            <div class="datasource-meta">
                                <div class="meta-item">
                                    <div class="meta-label">最后测试</div>
                                    <div class="meta-value">${lastTestedText}</div>
                                </div>
                                ${ds.tables ? `
                                    <div class="meta-item">
                                        <div class="meta-label">表数量</div>
                                        <div class="meta-value">${ds.tables}</div>
                                    </div>
                                ` : ''}
                                ${ds.keys ? `
                                    <div class="meta-item">
                                        <div class="meta-label">键数量</div>
                                        <div class="meta-value">${ds.keys}</div>
                                    </div>
                                ` : ''}
                                ${ds.size ? `
                                    <div class="meta-item">
                                        <div class="meta-label">大小</div>
                                        <div class="meta-value">${ds.size}</div>
                                    </div>
                                ` : ''}
                                ${ds.memory ? `
                                    <div class="meta-item">
                                        <div class="meta-label">内存</div>
                                        <div class="meta-value">${ds.memory}</div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>

                        <div class="datasource-actions">
                            <button class="btn btn-sm btn-primary" onclick="testSingleConnection(${ds.id})">
                                🔍 测试连接
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="editDataSource(${ds.id})">
                                ✏️ 编辑
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="duplicateDataSource(${ds.id})">
                                📋 复制
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteDataSource(${ds.id})">
                                🗑️ 删除
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新统计信息
        function updateStatistics() {
            const total = dataSources.length;
            const online = dataSources.filter(ds => ds.status === 'connected').length;
            const offline = total - online;
            const types = new Set(dataSources.map(ds => ds.type)).size;

            document.getElementById('totalCount').textContent = total;
            document.getElementById('onlineCount').textContent = online;
            document.getElementById('offlineCount').textContent = offline;
            document.getElementById('typeCount').textContent = types;
        }

        // 打开创建模态框
        function openCreateModal() {
            try {
                log('📝 尝试打开创建数据源对话框...', 'info');

                editingDataSource = null;
                connectionTested = false;

                // 重置模态框状态
                document.getElementById('modalTitle').textContent = '创建数据源';
                document.getElementById('editingId').value = '';

                // 重置表单
                const form = document.getElementById('dataSourceForm');
                if (form) {
                    form.reset();
                }

                // 重置连接配置
                const connectionForm = document.getElementById('connectionForm');
                if (connectionForm) {
                    connectionForm.innerHTML = '<p style="color: #6b7280; text-align: center; padding: 40px;">请先选择数据库类型</p>';
                }

                // 隐藏连接测试和高级选项
                const connectionTest = document.getElementById('connectionTest');
                if (connectionTest) {
                    connectionTest.style.display = 'none';
                }

                const advancedOptions = document.getElementById('advancedOptions');
                if (advancedOptions) {
                    advancedOptions.style.display = 'none';
                }

                const advancedBtn = document.getElementById('advancedBtn');
                if (advancedBtn) {
                    advancedBtn.textContent = '⚙️ 高级选项';
                }

                // 更新保存按钮状态
                updateSaveButtonState();

                // 显示模态框
                const modal = document.getElementById('dataSourceModal');
                if (modal) {
                    modal.classList.add('show');
                    log('✅ 创建数据源对话框已打开', 'success');

                    // 检查模态框是否立即消失
                    setTimeout(() => {
                        if (!modal.classList.contains('show')) {
                            log('❌ 模态框意外关闭了！', 'error');
                        }
                    }, 100);
                } else {
                    log('❌ 找不到模态框元素', 'error');
                }

            } catch (error) {
                log(`❌ 打开创建数据源对话框时发生错误: ${error.message}`, 'error');
                console.error('openCreateModal error:', error);
            }
        }

        // 关闭模态框
        function closeModal() {
            try {
                const modal = document.getElementById('dataSourceModal');
                if (modal) {
                    modal.classList.remove('show');

                    if (editingDataSource) {
                        log('❌ 取消编辑数据源', 'warning');
                    } else {
                        log('❌ 取消创建数据源', 'warning');
                    }

                    // 重置状态
                    editingDataSource = null;
                    connectionTested = false;
                } else {
                    log('❌ 找不到模态框元素', 'error');
                }
            } catch (error) {
                log(`❌ 关闭模态框时发生错误: ${error.message}`, 'error');
                console.error('closeModal error:', error);
            }
        }

        // 编辑数据源
        function editDataSource(id) {
            const dataSource = dataSources.find(ds => ds.id === id);
            if (!dataSource) return;

            editingDataSource = dataSource;
            connectionTested = true; // 编辑时假设连接已测试

            document.getElementById('modalTitle').textContent = '编辑数据源';
            document.getElementById('editingId').value = id;
            document.getElementById('dsName').value = dataSource.name;
            document.getElementById('dsDescription').value = dataSource.description || '';
            document.getElementById('dsType').value = dataSource.type;

            updateConnectionForm();

            // 填充连接信息
            const config = dbConfigs[dataSource.type];
            if (config) {
                config.required_fields.concat(config.optional_fields || []).forEach(field => {
                    const element = document.getElementById(`ds${capitalize(field)}`);
                    if (element && dataSource[field] !== undefined) {
                        element.value = dataSource[field];
                    }
                });
            }

            updateSaveButtonState();
            document.getElementById('dataSourceModal').classList.add('show');
            log(`✏️ 开始编辑数据源: ${dataSource.name}`, 'info');
        }

        // 更新连接表单
        function updateConnectionForm() {
            const type = document.getElementById('dsType').value;
            const formDiv = document.getElementById('connectionForm');
            const testDiv = document.getElementById('connectionTest');

            if (!type) {
                formDiv.innerHTML = '<p style="color: #6b7280; text-align: center; padding: 40px;">请先选择数据库类型</p>';
                testDiv.style.display = 'none';
                updateSaveButtonState();
                return;
            }

            const config = dbConfigs[type];
            if (!config) {
                formDiv.innerHTML = '<p style="color: #ef4444; text-align: center; padding: 40px;">不支持的数据库类型</p>';
                return;
            }

            // 显示驱动信息
            let html = `
                <div style="background: #f0f9ff; border: 2px solid #0ea5e9; border-radius: 12px; padding: 20px; margin-bottom: 24px;">
                    <h4 style="color: #0c4a6e; margin-bottom: 12px;">${config.icon} ${config.name} 配置</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 16px;">
                        <div>
                            <strong>驱动:</strong> ${config.driver}
                        </div>
                        <div>
                            <strong>默认端口:</strong> ${config.port || '无'}
                        </div>
                    </div>
                    <div>
                        <strong>特性:</strong> ${config.features.join(' • ')}
                    </div>
                </div>
            `;

            // 生成表单字段
            html += '<div class="form-grid">';

            // 必需字段
            config.required_fields.forEach(field => {
                const label = fieldLabels[field] || field;
                const defaultValue = config.default_values[field] || '';
                const inputType = getInputType(field);

                html += `
                    <div class="form-group">
                        <label>${label} <span class="required">*</span></label>
                        <input type="${inputType}" id="ds${capitalize(field)}" value="${defaultValue}"
                               onchange="validateField('ds${capitalize(field)}', true)"
                               onblur="validateField('ds${capitalize(field)}', true)"
                               placeholder="请输入${label}" required />
                        <div id="ds${capitalize(field)}Error" class="field-error" style="display: none;"></div>
                        <div id="ds${capitalize(field)}Success" class="field-success" style="display: none;"></div>
                    </div>
                `;
            });

            // 可选字段
            if (config.optional_fields && config.optional_fields.length > 0) {
                config.optional_fields.forEach(field => {
                    const label = fieldLabels[field] || field;
                    const defaultValue = config.default_values[field] || '';
                    const inputType = getInputType(field);

                    html += `
                        <div class="form-group">
                            <label>${label}</label>
                            <input type="${inputType}" id="ds${capitalize(field)}" value="${defaultValue}"
                                   onchange="validateField('ds${capitalize(field)}', false)"
                                   onblur="validateField('ds${capitalize(field)}', false)"
                                   placeholder="请输入${label} (可选)" />
                            <div id="ds${capitalize(field)}Error" class="field-error" style="display: none;"></div>
                            <div id="ds${capitalize(field)}Success" class="field-success" style="display: none;"></div>
                        </div>
                    `;
                });
            }

            html += '</div>';
            formDiv.innerHTML = html;
            testDiv.style.display = 'block';

            // 重置连接测试状态
            if (!editingDataSource) {
                connectionTested = false;
            }
            updateSaveButtonState();

            log(`🔧 加载 ${config.name} 连接配置`, 'info');
        }

        // 获取输入类型
        function getInputType(field) {
            if (field === 'password') return 'password';
            if (field === 'port') return 'number';
            if (field.includes('email')) return 'email';
            return 'text';
        }

        // 首字母大写
        function capitalize(str) {
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // 验证字段
        function validateField(fieldId, isRequired) {
            try {
                const field = document.getElementById(fieldId);
                const errorDiv = document.getElementById(fieldId + 'Error');
                const successDiv = document.getElementById(fieldId + 'Success');

                if (!field) return true;

                const value = field.value.trim();
                let isValid = true;
                let message = '';

                // 清除之前的状态
                field.classList.remove('error', 'success', 'warning');
                if (errorDiv) errorDiv.style.display = 'none';
                if (successDiv) successDiv.style.display = 'none';

                // 必填字段验证
                if (isRequired && !value) {
                    isValid = false;
                    message = '此字段为必填项';
                }

                // 特定字段验证
                if (value) {
                    if (fieldId.toLowerCase().includes('port')) {
                        const port = parseInt(value);
                        if (isNaN(port) || port < 1 || port > 65535) {
                            isValid = false;
                            message = '端口号必须在1-65535之间';
                        }
                    } else if (fieldId.toLowerCase().includes('host')) {
                        // 更宽松的主机地址验证，允许更多有效字符
                        const hostRegex = /^[a-zA-Z0-9._-]+(\.[a-zA-Z0-9._-]+)*$/;
                        if (!hostRegex.test(value) && value !== 'localhost') {
                            // 检查是否是IP地址
                            const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
                            if (!ipRegex.test(value)) {
                                isValid = false;
                                message = '请输入有效的主机地址或IP地址';
                            }
                        }
                    } else if (fieldId.toLowerCase().includes('email')) {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test(value)) {
                            isValid = false;
                            message = '请输入有效的邮箱地址';
                        }
                    } else if (fieldId.toLowerCase().includes('database') && fieldId.toLowerCase().includes('sqlite')) {
                        if (!value.endsWith('.db') && value !== ':memory:') {
                            field.classList.add('warning');
                            if (successDiv) {
                                successDiv.innerHTML = '⚠️ 建议使用.db扩展名或:memory:';
                                successDiv.style.display = 'block';
                            }
                        }
                    }
                }

                // 更新UI状态
                if (isValid) {
                    field.classList.add('success');
                    if (successDiv && !successDiv.innerHTML.includes('⚠️')) {
                        successDiv.innerHTML = '✅ 格式正确';
                        successDiv.style.display = 'block';
                    }
                } else {
                    field.classList.add('error');
                    if (errorDiv) {
                        errorDiv.innerHTML = `❌ ${message}`;
                        errorDiv.style.display = 'block';
                    }
                }

                updateSaveButtonState();
                return isValid;

            } catch (error) {
                log(`❌ 字段验证异常: ${error.message}`, 'error');
                console.error('validateField error:', error);
                return true; // 发生错误时返回true，避免阻止用户操作
            }
        }

        // 更新保存按钮状态
        function updateSaveButtonState() {
            const saveBtn = document.getElementById('saveBtn');
            const name = document.getElementById('dsName').value.trim();
            const type = document.getElementById('dsType').value;

            // 检查基本信息
            if (!name || !type) {
                saveBtn.disabled = true;
                saveBtn.textContent = '💾 请完善基本信息';
                return;
            }

            // 检查必填字段
            const config = dbConfigs[type];
            if (config) {
                let allValid = true;
                for (let field of config.required_fields) {
                    const element = document.getElementById(`ds${capitalize(field)}`);
                    if (!element || !element.value.trim()) {
                        allValid = false;
                        break;
                    }
                }

                if (!allValid) {
                    saveBtn.disabled = true;
                    saveBtn.textContent = '💾 请完善必填字段';
                    return;
                }
            }

            // 检查连接测试
            if (!editingDataSource && !connectionTested) {
                saveBtn.disabled = true;
                saveBtn.textContent = '💾 请先测试连接';
                return;
            }

            // 全部通过
            saveBtn.disabled = false;
            saveBtn.textContent = editingDataSource ? '💾 更新数据源' : '💾 保存数据源';
        }

        // 测试连接 - 真实连接测试
        async function testConnection() {
            const type = document.getElementById('dsType').value;
            const testBtn = document.getElementById('testBtn');
            const testResult = document.getElementById('testResult');
            const testProgress = document.getElementById('testProgress');
            const progressBar = testProgress.querySelector('.progress-bar');

            if (!type) {
                log('❌ 请先选择数据库类型', 'error');
                return;
            }

            // 收集连接参数
            const config = dbConfigs[type];
            const connectionData = { type };

            config.required_fields.concat(config.optional_fields || []).forEach(field => {
                const element = document.getElementById(`ds${capitalize(field)}`);
                if (element && element.value.trim()) {
                    connectionData[field] = field === 'port' ? parseInt(element.value) : element.value;
                }
            });

            // 更新UI状态
            testBtn.disabled = true;
            testBtn.textContent = '🔄 正在测试...';
            testResult.innerHTML = '';
            testProgress.style.display = 'block';
            document.getElementById('connectionTest').className = 'connection-test testing';

            // 进度条动画
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 85) progress = 85;
                progressBar.style.width = progress + '%';
            }, 300);

            try {
                log(`🔍 开始真实连接测试: ${config.name}`, 'info');

                // 真实连接测试API调用
                const response = await fetch('http://localhost:8080/api/test-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(connectionData)
                });

                clearInterval(progressInterval);
                progressBar.style.width = '100%';

                const result = await response.json();

                if (result.success) {
                    connectionTested = true;
                    testBtn.textContent = '✅ 连接成功';
                    testBtn.className = 'btn btn-success';
                    document.getElementById('connectionTest').className = 'connection-test success';

                    let html = `
                        <div style="color: #10b981; font-weight: 600; margin-bottom: 12px;">
                            ✅ ${result.message}
                        </div>
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 12px;">
                            🔗 ${result.mode} • ${new Date(result.test_time).toLocaleString()}
                        </div>
                    `;

                    if (result.details) {
                        html += `
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px; font-size: 13px; margin-bottom: 12px;">
                        `;

                        Object.keys(result.details).forEach(key => {
                            if (key !== 'features' && key !== 'password') {
                                const value = result.details[key];
                                const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                                html += `<div><strong>${label}:</strong> ${value}</div>`;
                            }
                        });

                        html += '</div>';

                        if (result.details.features) {
                            html += `
                                <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 12px; font-size: 13px;">
                                    <strong>特性支持:</strong> ${result.details.features.join(' • ')}
                                </div>
                            `;
                        }
                    }

                    testResult.innerHTML = html;
                    log(`✅ ${result.message}`, 'success');

                } else {
                    connectionTested = false;
                    testBtn.textContent = '❌ 连接失败';
                    testBtn.className = 'btn btn-danger';
                    document.getElementById('connectionTest').className = 'connection-test error';

                    let html = `
                        <div style="color: #ef4444; font-weight: 600; margin-bottom: 12px;">
                            ❌ ${result.message}
                        </div>
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 12px;">
                            🔗 ${result.mode} • ${new Date(result.test_time).toLocaleString()}
                        </div>
                    `;

                    if (result.error_type) {
                        html += `
                            <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 12px; font-size: 13px; margin-bottom: 12px;">
                                <strong>错误类型:</strong> ${result.error_type}
                            </div>
                        `;
                    }

                    if (result.suggestion) {
                        html += `
                            <div style="background: #fffbeb; border: 1px solid #fed7aa; border-radius: 8px; padding: 12px; font-size: 13px;">
                                <strong>💡 建议:</strong> ${result.suggestion}
                            </div>
                        `;
                    } else {
                        html += `
                            <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 12px; font-size: 13px;">
                                <strong>排查建议:</strong>
                                <ul style="margin: 8px 0 0 20px;">
                                    <li>检查网络连接是否正常</li>
                                    <li>确认数据库服务是否启动</li>
                                    <li>验证连接参数是否正确</li>
                                    <li>检查防火墙设置</li>
                                </ul>
                            </div>
                        `;
                    }

                    testResult.innerHTML = html;
                    log(`❌ ${result.message}`, 'error');
                }

            } catch (error) {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';

                connectionTested = false;
                testBtn.textContent = '❌ 测试失败';
                testBtn.className = 'btn btn-danger';
                document.getElementById('connectionTest').className = 'connection-test error';

                let errorMessage = '';
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = '无法连接到真实连接测试服务器，请先启动服务器';
                } else {
                    errorMessage = `请求失败: ${error.message}`;
                }

                testResult.innerHTML = `
                    <div style="color: #ef4444; font-weight: 600; margin-bottom: 12px;">
                        ❌ 连接测试异常
                    </div>
                    <div style="color: #6b7280; margin-bottom: 12px;">
                        ${errorMessage}
                    </div>
                    <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 12px; font-size: 13px;">
                        <strong>解决方案:</strong>
                        <ol style="margin: 8px 0 0 20px;">
                            <li>启动真实连接测试服务器: <code>python real_connection_server.py</code></li>
                            <li>确保服务器运行在 http://localhost:8080</li>
                            <li>检查浏览器是否允许跨域请求</li>
                        </ol>
                    </div>
                `;

                log(`❌ 连接测试异常: ${errorMessage}`, 'error');
            } finally {
                setTimeout(() => {
                    testProgress.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 1000);

                testBtn.disabled = false;
                updateSaveButtonState();
            }
        }

        // 获取版本字符串
        function getVersionString(type) {
            const versions = {
                'sqlite': 'SQLite 3.39.0',
                'postgresql': 'PostgreSQL 15.2',
                'mysql': 'MySQL 8.0.35',
                'mssql': 'SQL Server 2022',
                'mongodb': 'MongoDB 7.0.4',
                'redis': 'Redis 7.2.3',
                'oracle': 'Oracle 19c'
            };
            return versions[type] || 'Unknown';
        }

        // 保存数据源
        function saveDataSource() {
            const name = document.getElementById('dsName').value.trim();
            const description = document.getElementById('dsDescription').value.trim();
            const type = document.getElementById('dsType').value;

            if (!name || !type) {
                log('❌ 请填写完整的基本信息', 'error');
                return;
            }

            // 收集连接配置
            const config = dbConfigs[type];
            const dataSource = {
                name,
                description,
                type,
                status: 'connected',
                lastTested: new Date(),
                createdAt: editingDataSource ? editingDataSource.createdAt : new Date()
            };

            // 收集连接参数
            config.required_fields.concat(config.optional_fields || []).forEach(field => {
                const element = document.getElementById(`ds${capitalize(field)}`);
                if (element && element.value.trim()) {
                    dataSource[field] = field === 'port' ? parseInt(element.value) : element.value;
                }
            });

            // 收集高级选项
            const timeout = document.getElementById('dsTimeout');
            const maxConnections = document.getElementById('dsMaxConnections');
            const sslMode = document.getElementById('dsSslMode');
            const charset = document.getElementById('dsCharset');

            if (timeout && timeout.value) dataSource.timeout = parseInt(timeout.value);
            if (maxConnections && maxConnections.value) dataSource.maxConnections = parseInt(maxConnections.value);
            if (sslMode && sslMode.value) dataSource.sslMode = sslMode.value;
            if (charset && charset.value) dataSource.charset = charset.value;

            if (editingDataSource) {
                // 更新现有数据源
                dataSource.id = editingDataSource.id;
                const index = dataSources.findIndex(ds => ds.id === editingDataSource.id);
                if (index !== -1) {
                    dataSources[index] = { ...dataSources[index], ...dataSource };
                    log(`✅ 数据源 "${name}" 更新成功`, 'success');
                }
            } else {
                // 创建新数据源
                dataSource.id = Math.max(...dataSources.map(ds => ds.id), 0) + 1;

                // 模拟一些统计数据
                if (type === 'sqlite') {
                    dataSource.tables = 5;
                    dataSource.size = '2.5MB';
                } else if (type === 'redis') {
                    dataSource.keys = Math.floor(Math.random() * 10000 + 1000);
                    dataSource.memory = Math.floor(Math.random() * 500 + 100) + 'MB';
                } else {
                    dataSource.tables = Math.floor(Math.random() * 200 + 50);
                    dataSource.size = (Math.random() * 5 + 0.5).toFixed(1) + 'GB';
                }

                dataSources.push(dataSource);
                log(`✅ 数据源 "${name}" 创建成功`, 'success');
            }

            renderDataSources();
            updateStatistics();
            closeModal();
        }

        // 删除数据源
        function deleteDataSource(id) {
            const dataSource = dataSources.find(ds => ds.id === id);
            if (!dataSource) return;

            if (!confirm(`确定要删除数据源 "${dataSource.name}" 吗？\n\n此操作不可撤销！`)) {
                return;
            }

            const index = dataSources.findIndex(ds => ds.id === id);
            if (index !== -1) {
                dataSources.splice(index, 1);
                renderDataSources();
                updateStatistics();
                log(`🗑️ 数据源 "${dataSource.name}" 已删除`, 'warning');
            }
        }

        // 复制数据源
        function duplicateDataSource(id) {
            const dataSource = dataSources.find(ds => ds.id === id);
            if (!dataSource) return;

            const newDataSource = {
                ...dataSource,
                id: Math.max(...dataSources.map(ds => ds.id), 0) + 1,
                name: dataSource.name + ' (副本)',
                status: 'disconnected',
                createdAt: new Date(),
                lastTested: null
            };

            dataSources.push(newDataSource);
            renderDataSources();
            updateStatistics();
            log(`📋 数据源 "${dataSource.name}" 已复制`, 'success');
        }

        // 测试单个连接
        function testSingleConnection(id) {
            const dataSource = dataSources.find(ds => ds.id === id);
            if (!dataSource) return;

            const item = document.querySelector(`[data-id="${id}"]`);
            const originalContent = item.innerHTML;

            // 显示测试状态
            item.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div class="pulse" style="font-size: 24px; margin-bottom: 16px;">🔄</div>
                    <div style="font-weight: 600; margin-bottom: 8px;">正在测试连接...</div>
                    <div style="color: #6b7280;">请稍候</div>
                </div>
            `;

            // 模拟测试
            setTimeout(() => {
                const success = Math.random() > 0.3; // 70% 成功率
                dataSource.status = success ? 'connected' : 'disconnected';
                dataSource.lastTested = new Date();

                renderDataSources();
                updateStatistics();

                if (success) {
                    log(`✅ 数据源 "${dataSource.name}" 连接测试成功`, 'success');
                } else {
                    log(`❌ 数据源 "${dataSource.name}" 连接测试失败`, 'error');
                }
            }, 2000 + Math.random() * 2000);
        }

        // 批量测试所有连接
        function testAllConnections() {
            if (dataSources.length === 0) {
                log('⚠️ 没有可测试的数据源', 'warning');
                return;
            }

            log(`🔍 开始批量测试 ${dataSources.length} 个数据源`, 'info');

            dataSources.forEach((ds, index) => {
                setTimeout(() => {
                    testSingleConnection(ds.id);
                }, index * 1000); // 错开测试时间
            });
        }

        // 刷新数据源列表
        function refreshDataSources() {
            log('🔄 刷新数据源列表', 'info');
            renderDataSources();
            updateStatistics();
        }

        // 切换高级选项
        function toggleAdvancedOptions() {
            const advancedDiv = document.getElementById('advancedOptions');
            const advancedBtn = document.getElementById('advancedBtn');

            if (advancedDiv.style.display === 'none' || !advancedDiv.style.display) {
                advancedDiv.style.display = 'block';
                advancedBtn.textContent = '⚙️ 隐藏高级选项';
                log('⚙️ 显示高级选项', 'info');
            } else {
                advancedDiv.style.display = 'none';
                advancedBtn.textContent = '⚙️ 高级选项';
                log('⚙️ 隐藏高级选项', 'info');
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+N 创建新数据源
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                openCreateModal();
            }

            // ESC 关闭模态框
            if (e.key === 'Escape') {
                closeModal();
            }

            // Ctrl+R 刷新列表
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                refreshDataSources();
            }
        });

        // 点击模态框外部关闭
        document.getElementById('dataSourceModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 表单提交处理
        document.getElementById('dataSourceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveDataSource();
        });

        // 名称字段实时验证
        document.addEventListener('DOMContentLoaded', function() {
            const nameField = document.getElementById('dsName');
            if (nameField) {
                nameField.addEventListener('input', function() {
                    validateField('dsName', true);
                });
            }
        });
    </script>
</body>
</html>
