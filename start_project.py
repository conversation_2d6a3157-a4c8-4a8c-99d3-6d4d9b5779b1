#!/usr/bin/env python3
"""
高级数据源管理平台启动脚本

提供多种启动方式：
1. 高级数据源管理平台 (推荐)
2. 验证功能测试页面
3. 后端API服务器
4. 完整项目 (前端+后端)
"""

import os
import sys
import time
import webbrowser
import threading
import subprocess
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🗄️  高级数据源管理平台启动器  🗄️                      ║
    ║                                                              ║
    ║        企业级数据源管理解决方案                               ║
    ║        支持创建、编辑、验证、监控                             ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """打印菜单选项"""
    menu = """
    请选择启动方式：
    
    🎯 推荐选项：
    [1] 高级数据源管理平台 (完整功能)
    [2] 验证功能测试页面 (轻量级)
    
    🔧 开发选项：
    [3] 后端API服务器
    [4] 前端开发服务器
    [5] 完整项目 (前端+后端)
    
    📚 文档选项：
    [6] 查看功能指南
    [7] 查看项目文档
    
    [0] 退出
    
    """
    print(menu)

def open_browser_delayed(url, delay=2):
    """延迟打开浏览器"""
    time.sleep(delay)
    webbrowser.open(url)

def start_advanced_manager():
    """启动高级数据源管理平台"""
    print("🚀 启动高级数据源管理平台...")
    
    html_path = Path("advanced_datasource_manager.html")
    if not html_path.exists():
        print("❌ 找不到 advanced_datasource_manager.html 文件")
        return False
    
    # 获取绝对路径
    abs_path = html_path.absolute()
    url = f"file:///{abs_path.as_posix()}"
    
    print(f"📱 访问地址: {url}")
    print("🔧 功能特性:")
    print("   ✅ 支持7种数据库类型")
    print("   ✅ 实时连接验证")
    print("   ✅ 在线编辑功能")
    print("   ✅ 批量操作支持")
    print("   ✅ 现代化界面")
    print()
    print("⚡ 正在打开浏览器...")
    
    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser_delayed, args=(url,))
    browser_thread.daemon = True
    browser_thread.start()
    
    print("✅ 高级数据源管理平台已启动")
    print("💡 提示: 建议从SQLite开始测试")
    return True

def start_validation_test():
    """启动验证功能测试页面"""
    print("🔍 启动验证功能测试页面...")
    
    html_path = Path("validation_test.html")
    if not html_path.exists():
        print("❌ 找不到 validation_test.html 文件")
        return False
    
    abs_path = html_path.absolute()
    url = f"file:///{abs_path.as_posix()}"
    
    print(f"📱 访问地址: {url}")
    print("🔧 功能特性:")
    print("   ✅ 字段验证演示")
    print("   ✅ 连接测试模拟")
    print("   ✅ 智能按钮控制")
    print("   ✅ 错误提示系统")
    print()
    print("⚡ 正在打开浏览器...")
    
    browser_thread = threading.Thread(target=open_browser_delayed, args=(url,))
    browser_thread.daemon = True
    browser_thread.start()
    
    print("✅ 验证功能测试页面已启动")
    return True

def start_backend_server():
    """启动后端API服务器"""
    print("🔧 启动后端API服务器...")
    
    backend_path = Path("backend")
    if not backend_path.exists():
        print("❌ 找不到 backend 目录")
        return False
    
    # 检查是否有虚拟环境
    venv_path = backend_path / "venv"
    if venv_path.exists():
        if sys.platform == "win32":
            python_cmd = str(venv_path / "Scripts" / "python.exe")
        else:
            python_cmd = str(venv_path / "bin" / "python")
    else:
        python_cmd = "python"
    
    # 检查主应用文件
    main_file = backend_path / "app" / "main.py"
    if not main_file.exists():
        print("❌ 找不到后端主应用文件")
        return False
    
    try:
        print("📡 启动FastAPI服务器...")
        print("📱 API地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print()
        
        # 启动服务器
        os.chdir(backend_path)
        subprocess.run([python_cmd, "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"])
        
    except KeyboardInterrupt:
        print("\n👋 后端服务器已停止")
    except Exception as e:
        print(f"❌ 启动后端服务器失败: {e}")
        return False
    
    return True

def start_frontend_server():
    """启动前端开发服务器"""
    print("🎨 启动前端开发服务器...")
    
    frontend_path = Path("frontend")
    if not frontend_path.exists():
        print("❌ 找不到 frontend 目录")
        return False
    
    package_json = frontend_path / "package.json"
    if not package_json.exists():
        print("❌ 找不到 package.json 文件")
        return False
    
    try:
        print("📦 安装依赖...")
        os.chdir(frontend_path)
        subprocess.run(["npm", "install"], check=True)
        
        print("🚀 启动开发服务器...")
        print("📱 访问地址: http://localhost:5173")
        print()
        
        subprocess.run(["npm", "run", "dev"])
        
    except KeyboardInterrupt:
        print("\n👋 前端服务器已停止")
    except Exception as e:
        print(f"❌ 启动前端服务器失败: {e}")
        return False
    
    return True

def start_full_project():
    """启动完整项目"""
    print("🚀 启动完整项目 (前端+后端)...")
    
    # 检查Docker
    try:
        subprocess.run(["docker", "--version"], check=True, capture_output=True)
        subprocess.run(["docker-compose", "--version"], check=True, capture_output=True)
    except:
        print("❌ 请先安装 Docker 和 Docker Compose")
        return False
    
    compose_file = Path("docker-compose.yml")
    if not compose_file.exists():
        print("❌ 找不到 docker-compose.yml 文件")
        return False
    
    try:
        print("🐳 启动Docker容器...")
        print("📱 前端地址: http://localhost:3000")
        print("📡 后端地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print()
        
        subprocess.run(["docker-compose", "up", "--build"])
        
    except KeyboardInterrupt:
        print("\n👋 正在停止容器...")
        subprocess.run(["docker-compose", "down"])
    except Exception as e:
        print(f"❌ 启动完整项目失败: {e}")
        return False
    
    return True

def show_documentation():
    """显示功能指南"""
    print("📚 功能指南:")
    print()
    
    docs = [
        ("高级数据源管理功能指南.md", "🗄️ 高级数据源管理功能指南"),
        ("数据源验证功能指南.md", "🔐 数据源验证功能指南"),
        ("数据源驱动测试指南.md", "🔧 数据源驱动测试指南"),
    ]
    
    for filename, title in docs:
        if Path(filename).exists():
            print(f"   ✅ {title}")
        else:
            print(f"   ❌ {title} (文件不存在)")
    
    print()
    print("💡 提示: 可以使用文本编辑器或Markdown查看器打开这些文件")

def show_project_docs():
    """显示项目文档"""
    print("📖 项目文档:")
    print()
    
    docs = [
        ("README.md", "📋 项目说明"),
        ("PROJECT_STRUCTURE.md", "🏗️ 项目结构"),
        ("docs/project_specification.md", "📝 项目规范"),
        ("docs/requirements_specification.md", "📋 需求规范"),
        ("docs/development_summary.md", "🔧 开发总结"),
    ]
    
    for filename, title in docs:
        if Path(filename).exists():
            print(f"   ✅ {title}")
        else:
            print(f"   ❌ {title} (文件不存在)")
    
    print()
    print("💡 提示: 建议从 README.md 开始阅读")

def main():
    """主函数"""
    print_banner()
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选项 [0-7]: ").strip()
            
            if choice == "0":
                print("👋 感谢使用高级数据源管理平台！")
                break
            elif choice == "1":
                start_advanced_manager()
                input("\n按回车键返回主菜单...")
            elif choice == "2":
                start_validation_test()
                input("\n按回车键返回主菜单...")
            elif choice == "3":
                start_backend_server()
                input("\n按回车键返回主菜单...")
            elif choice == "4":
                start_frontend_server()
                input("\n按回车键返回主菜单...")
            elif choice == "5":
                start_full_project()
                input("\n按回车键返回主菜单...")
            elif choice == "6":
                show_documentation()
                input("\n按回车键返回主菜单...")
            elif choice == "7":
                show_project_docs()
                input("\n按回车键返回主菜单...")
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 感谢使用高级数据源管理平台！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("\n按回车键返回主菜单...")

if __name__ == "__main__":
    main()
