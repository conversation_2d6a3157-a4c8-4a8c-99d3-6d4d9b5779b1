"""
API依赖项

定义API路由中使用的依赖项，如用户认证、权限检查等。
"""

from typing import Generator

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.core import security
from app.core.config import settings
from app.db.base import get_db
from app.db.models.user import User, UserRole
from app.schemas.auth import TokenPayload
from app.services.user_service import UserService

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)


def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    获取当前用户。
    
    Args:
        db: 数据库会话
        token: JWT令牌
        
    Returns:
        User: 当前用户
        
    Raises:
        HTTPException: 当令牌无效或用户不存在时
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无法验证凭据",
        )
    
    user_service = UserService(db)
    user = user_service.get_user(token_data.sub)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前活跃用户。
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前活跃用户
        
    Raises:
        HTTPException: 当用户未激活时
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    return current_user


def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前活跃的超级管理员用户。
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前超级管理员用户
        
    Raises:
        HTTPException: 当用户不是超级管理员时
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    if current_user.role != UserRole.SUPER_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级管理员权限"
        )
    return current_user


def is_admin_or_superuser(user: User) -> bool:
    """
    检查用户是否是管理员或超级管理员。
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否是管理员或超级管理员
    """
    return user.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]


def is_analyst_or_above(user: User) -> bool:
    """
    检查用户是否是分析师或更高级别。
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否是分析师或更高级别
    """
    return user.role in [UserRole.ANALYST, UserRole.ADMIN, UserRole.SUPER_ADMIN]
