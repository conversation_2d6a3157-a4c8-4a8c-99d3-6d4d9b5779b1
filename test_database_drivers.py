#!/usr/bin/env python3
"""
数据源驱动测试服务器

测试各种数据库驱动的连接和查询功能。
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import threading
import time
import webbrowser

# 全局数据存储
data_sources = []
next_id = 1

class DatabaseDriverTestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_test_page()
        elif self.path == '/api/v1/data-sources/':
            self.handle_get_list()
        elif self.path == '/api/v1/data-sources/types':
            self.handle_get_types()
        elif self.path.startswith('/api/v1/data-sources/types/'):
            self.handle_get_type_requirements()
        elif '/tables' in self.path:
            self.handle_get_tables()
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        if self.path == '/api/v1/auth/login':
            self.handle_login()
        elif self.path == '/api/v1/data-sources/':
            self.handle_create()
        elif self.path == '/api/v1/data-sources/test':
            self.handle_test_connection()
        elif self.path == '/api/v1/data-sources/query':
            self.handle_query()
        else:
            self.send_response(404)
            self.end_headers()

    def do_DELETE(self):
        if self.path.startswith('/api/v1/data-sources/'):
            self.handle_delete()
        else:
            self.send_response(404)
            self.end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def handle_login(self):
        self.send_json_response({
            "access_token": "test_token_12345",
            "token_type": "bearer"
        })

    def handle_get_types(self):
        """获取支持的数据库类型"""
        types = [
            "postgresql", "mysql", "sqlserver", "mssql",
            "mongodb", "redis", "sqlite", "oracle"
        ]
        self.send_json_response(types)

    def handle_get_type_requirements(self):
        """获取数据库类型要求"""
        path_parts = self.path.split('/')
        db_type = path_parts[4]  # /api/v1/data-sources/types/{type}/requirements

        requirements = {
            "postgresql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 5432,
                "supports_ssl": True,
                "driver": "psycopg2-binary"
            },
            "mysql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 3306,
                "supports_ssl": True,
                "driver": "pymysql"
            },
            "mssql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 1433,
                "supports_ssl": True,
                "driver": "pyodbc"
            },
            "mongodb": {
                "required_fields": ["host", "port", "database"],
                "optional_fields": ["username", "password"],
                "default_port": 27017,
                "supports_ssl": True,
                "driver": "pymongo"
            },
            "redis": {
                "required_fields": ["host", "port"],
                "optional_fields": ["password"],
                "default_port": 6379,
                "supports_ssl": False,
                "driver": "redis"
            },
            "sqlite": {
                "required_fields": ["database"],
                "default_port": 0,
                "supports_ssl": False,
                "driver": "built-in"
            }
        }

        self.send_json_response(requirements.get(db_type, {}))

    def handle_create(self):
        global data_sources, next_id

        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        new_ds = {
            "id": next_id,
            "name": data["name"],
            "description": data.get("description", ""),
            "type": data["type"],
            "host": data.get("host", ""),
            "port": data.get("port", 0),
            "database": data.get("database", ""),
            "username": data.get("username", ""),
            "status": "disconnected",
            "created_at": datetime.now().isoformat(),
            "tables": []  # 模拟表列表
        }

        # 根据数据库类型添加模拟表
        if data["type"] in ["postgresql", "mysql", "mssql"]:
            new_ds["tables"] = ["users", "orders", "products", "categories"]
        elif data["type"] == "mongodb":
            new_ds["tables"] = ["users", "orders", "products", "logs"]
        elif data["type"] == "redis":
            new_ds["tables"] = ["user:*", "session:*", "cache:*"]
        elif data["type"] == "sqlite":
            new_ds["tables"] = ["users", "settings"]

        data_sources.append(new_ds)
        next_id += 1

        print(f"✅ 创建数据源: {new_ds['name']} (ID: {new_ds['id']}, 类型: {new_ds['type']})")

        self.send_json_response(new_ds)

    def handle_get_list(self):
        global data_sources

        self.send_json_response({
            "items": data_sources,
            "total": len(data_sources)
        })

    def handle_test_connection(self):
        """测试数据库连接"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        db_type = data["type"]

        # 模拟不同数据库的连接测试
        if db_type == "postgresql":
            result = {
                "success": True,
                "message": "PostgreSQL连接成功",
                "details": {
                    "version": "PostgreSQL 15.0",
                    "driver": "psycopg2-binary",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["ACID事务", "JSON支持", "全文搜索", "并行查询"]
                }
            }
        elif db_type == "mysql":
            result = {
                "success": True,
                "message": "MySQL连接成功",
                "details": {
                    "version": "MySQL 8.0.35",
                    "driver": "pymysql",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["InnoDB引擎", "分区表", "JSON支持", "窗口函数"]
                }
            }
        elif db_type == "mssql":
            result = {
                "success": True,
                "message": "SQL Server连接成功",
                "details": {
                    "version": "Microsoft SQL Server 2022",
                    "driver": "pyodbc + ODBC Driver 17",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["T-SQL", "列存储索引", "内存优化表", "Always Encrypted"]
                }
            }
        elif db_type == "mongodb":
            result = {
                "success": True,
                "message": "MongoDB连接成功",
                "details": {
                    "version": "MongoDB 7.0",
                    "driver": "pymongo",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["文档存储", "聚合管道", "分片", "副本集"]
                }
            }
        elif db_type == "redis":
            result = {
                "success": True,
                "message": "Redis连接成功",
                "details": {
                    "version": "Redis 7.2",
                    "driver": "redis-py",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["键值存储", "发布订阅", "Lua脚本", "集群模式"]
                }
            }
        elif db_type == "sqlite":
            result = {
                "success": True,
                "message": "SQLite连接成功",
                "details": {
                    "version": "SQLite 3.39.0",
                    "driver": "内置sqlite3模块",
                    "database": data.get("database"),
                    "features": ["嵌入式数据库", "ACID事务", "全文搜索", "JSON支持"]
                }
            }
        else:
            result = {
                "success": False,
                "message": f"暂不支持 {db_type} 类型的连接测试"
            }

        result["test_time"] = datetime.now().isoformat()
        self.send_json_response(result)

    def handle_get_tables(self):
        """获取数据源表列表"""
        path_parts = self.path.split('/')
        ds_id = int(path_parts[4])  # /api/v1/data-sources/{id}/tables

        # 查找数据源
        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({"detail": "数据源不存在"}, status=404)
            return

        # 根据数据库类型返回不同的表结构
        if ds["type"] in ["postgresql", "mysql", "mssql"]:
            tables_with_schema = []
            for table in ds["tables"]:
                tables_with_schema.append({
                    "table_name": table,
                    "table_type": "BASE TABLE",
                    "row_count": 1000 + len(table) * 100,  # 模拟行数
                    "size": f"{len(table) * 10}MB"
                })
        elif ds["type"] == "mongodb":
            tables_with_schema = []
            for collection in ds["tables"]:
                tables_with_schema.append({
                    "collection_name": collection,
                    "document_count": 500 + len(collection) * 50,
                    "size": f"{len(collection) * 5}MB",
                    "indexes": ["_id_", f"{collection}_index"]
                })
        else:
            tables_with_schema = ds["tables"]

        self.send_json_response({
            "data_source_id": ds_id,
            "data_source_name": ds["name"],
            "tables": tables_with_schema,
            "total": len(tables_with_schema)
        })

    def handle_query(self):
        """执行查询"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        ds_id = data["data_source_id"]
        query = data["query"]

        # 查找数据源
        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({
                "success": False,
                "message": "数据源不存在"
            })
            return

        # 根据数据库类型模拟查询结果
        if ds["type"] in ["postgresql", "mysql", "mssql", "sqlite"]:
            mock_data = [
                {"id": 1, "name": "张三", "email": "<EMAIL>", "created_at": "2024-01-01"},
                {"id": 2, "name": "李四", "email": "<EMAIL>", "created_at": "2024-01-02"},
                {"id": 3, "name": "王五", "email": "<EMAIL>", "created_at": "2024-01-03"}
            ]
            columns = ["id", "name", "email", "created_at"]
        elif ds["type"] == "mongodb":
            mock_data = [
                {"_id": "507f1f77bcf86cd799439011", "name": "张三", "age": 25, "city": "北京"},
                {"_id": "507f1f77bcf86cd799439012", "name": "李四", "age": 30, "city": "上海"},
                {"_id": "507f1f77bcf86cd799439013", "name": "王五", "age": 28, "city": "广州"}
            ]
            columns = ["_id", "name", "age", "city"]
        elif ds["type"] == "redis":
            mock_data = [
                {"key": "user:1001", "type": "hash", "value": "{'name': '张三', 'age': 25}"},
                {"key": "user:1002", "type": "hash", "value": "{'name': '李四', 'age': 30}"},
                {"key": "session:abc123", "type": "string", "value": "active"}
            ]
            columns = ["key", "type", "value"]
        else:
            mock_data = []
            columns = []

        self.send_json_response({
            "success": True,
            "data": mock_data,
            "columns": columns,
            "total": len(mock_data),
            "execution_time": 0.025,
            "message": f"在 {ds['type']} 数据源上执行查询成功"
        })

    def handle_delete(self):
        global data_sources

        ds_id = int(self.path.split('/')[-1])
        original_count = len(data_sources)
        data_sources = [ds for ds in data_sources if ds["id"] != ds_id]

        if len(data_sources) < original_count:
            self.send_json_response({"message": f"数据源 ID {ds_id} 删除成功"})
        else:
            self.send_json_response({"detail": "数据源不存在"}, status=404)