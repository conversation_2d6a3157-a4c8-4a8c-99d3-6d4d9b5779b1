#!/usr/bin/env python3
"""
数据源驱动测试服务器

测试各种数据库驱动的连接和查询功能。
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import threading
import time
import webbrowser
import sys
import os

# 添加backend路径以便导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from app.services.database_factory import DatabaseFactory, test_database_connection, execute_database_query
    from app.db.models.data_source import DataSourceType
    REAL_MODE = True
    print("✅ 已加载真实数据库驱动")
except ImportError as e:
    print(f"⚠️ 无法加载数据库驱动，使用模拟模式: {e}")
    REAL_MODE = False

# 全局数据存储
data_sources = []
next_id = 1

class DatabaseDriverTestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_test_page()
        elif self.path == '/api/v1/data-sources/':
            self.handle_get_list()
        elif self.path == '/api/v1/data-sources/types':
            self.handle_get_types()
        elif self.path.startswith('/api/v1/data-sources/types/'):
            self.handle_get_type_requirements()
        elif '/tables' in self.path:
            self.handle_get_tables()
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        if self.path == '/api/v1/auth/login':
            self.handle_login()
        elif self.path == '/api/v1/data-sources/':
            self.handle_create()
        elif self.path == '/api/v1/data-sources/test':
            self.handle_test_connection()
        elif self.path == '/api/v1/data-sources/query':
            self.handle_query()
        else:
            self.send_response(404)
            self.end_headers()

    def do_DELETE(self):
        if self.path.startswith('/api/v1/data-sources/'):
            self.handle_delete()
        else:
            self.send_response(404)
            self.end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def handle_login(self):
        self.send_json_response({
            "access_token": "test_token_12345",
            "token_type": "bearer"
        })

    def handle_get_types(self):
        """获取支持的数据库类型"""
        types = [
            "postgresql", "mysql", "sqlserver", "mssql",
            "mongodb", "redis", "sqlite", "oracle"
        ]
        self.send_json_response(types)

    def handle_get_type_requirements(self):
        """获取数据库类型要求"""
        path_parts = self.path.split('/')
        db_type = path_parts[4]  # /api/v1/data-sources/types/{type}/requirements

        requirements = {
            "postgresql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 5432,
                "supports_ssl": True,
                "driver": "psycopg2-binary",
                "features": ["ACID事务", "JSON支持", "全文搜索", "并行查询"]
            },
            "mysql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 3306,
                "supports_ssl": True,
                "driver": "pymysql",
                "features": ["InnoDB引擎", "分区表", "JSON支持", "窗口函数"]
            },
            "mssql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 1433,
                "supports_ssl": True,
                "driver": "pyodbc + ODBC Driver 17",
                "features": ["T-SQL", "列存储索引", "内存优化表", "Always Encrypted"]
            },
            "mongodb": {
                "required_fields": ["host", "port", "database"],
                "optional_fields": ["username", "password"],
                "default_port": 27017,
                "supports_ssl": True,
                "driver": "pymongo",
                "features": ["文档存储", "聚合管道", "分片", "副本集"]
            },
            "redis": {
                "required_fields": ["host", "port"],
                "optional_fields": ["password"],
                "default_port": 6379,
                "supports_ssl": False,
                "driver": "redis-py",
                "features": ["键值存储", "发布订阅", "Lua脚本", "集群模式"]
            },
            "sqlite": {
                "required_fields": ["database"],
                "default_port": 0,
                "supports_ssl": False,
                "driver": "内置sqlite3模块",
                "features": ["嵌入式数据库", "ACID事务", "全文搜索", "JSON支持"]
            }
        }

        self.send_json_response(requirements.get(db_type, {}))

    def handle_create(self):
        global data_sources, next_id

        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        new_ds = {
            "id": next_id,
            "name": data["name"],
            "description": data.get("description", ""),
            "type": data["type"],
            "host": data.get("host", ""),
            "port": data.get("port", 0),
            "database": data.get("database", ""),
            "username": data.get("username", ""),
            "status": "disconnected",
            "created_at": datetime.now().isoformat(),
            "tables": []  # 模拟表列表
        }

        # 保存真实配置用于连接测试
        if REAL_MODE:
            real_config = {
                "host": data.get("host"),
                "port": data.get("port"),
                "database": data.get("database"),
                "username": data.get("username"),
                "password": data.get("password")
            }
            # 移除空值
            real_config = {k: v for k, v in real_config.items() if v is not None and v != ""}
            new_ds["real_config"] = real_config

            # 尝试连接测试以获取真实表列表
            try:
                db_type_map = {
                    "postgresql": DataSourceType.POSTGRESQL,
                    "mysql": DataSourceType.MYSQL,
                    "mssql": DataSourceType.MSSQL,
                    "mongodb": DataSourceType.MONGODB,
                    "redis": DataSourceType.REDIS,
                    "sqlite": DataSourceType.SQLITE
                }

                db_type_enum = db_type_map.get(data["type"])
                if db_type_enum:
                    from app.services.database_factory import create_database_adapter
                    adapter = create_database_adapter(db_type_enum, real_config)
                    if adapter:
                        tables = adapter.get_tables()
                        new_ds["tables"] = tables[:10]  # 限制数量
                        new_ds["status"] = "connected"
                        print(f"🔗 成功连接并获取 {len(tables)} 个表/集合")
                    else:
                        print(f"⚠️ 无法创建适配器，使用模拟表列表")
                        self._add_mock_tables(new_ds, data["type"])
                else:
                    print(f"⚠️ 不支持的数据库类型，使用模拟表列表")
                    self._add_mock_tables(new_ds, data["type"])
            except Exception as e:
                print(f"⚠️ 连接测试失败，使用模拟表列表: {str(e)}")
                self._add_mock_tables(new_ds, data["type"])
        else:
            # 模拟模式
            self._add_mock_tables(new_ds, data["type"])

        data_sources.append(new_ds)
        next_id += 1

        print(f"✅ 创建数据源: {new_ds['name']} (ID: {new_ds['id']}, 类型: {new_ds['type']}, 状态: {new_ds['status']})")

        self.send_json_response(new_ds)

    def _add_mock_tables(self, ds, db_type):
        """添加模拟表列表"""
        if db_type in ["postgresql", "mysql", "mssql"]:
            ds["tables"] = ["users", "orders", "products", "categories", "inventory"]
        elif db_type == "mongodb":
            ds["tables"] = ["users", "orders", "products", "logs", "sessions"]
        elif db_type == "redis":
            ds["tables"] = ["user:*", "session:*", "cache:*", "queue:*"]
        elif db_type == "sqlite":
            ds["tables"] = ["users", "settings", "logs"]

    def handle_get_list(self):
        global data_sources

        self.send_json_response({
            "items": data_sources,
            "total": len(data_sources)
        })

    def handle_test_connection(self):
        """测试数据库连接"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        db_type = data["type"]

        if REAL_MODE:
            # 使用真实的数据库驱动进行连接测试
            try:
                # 转换为DataSourceType枚举
                if db_type == "mssql":
                    db_type_enum = DataSourceType.MSSQL
                elif db_type == "postgresql":
                    db_type_enum = DataSourceType.POSTGRESQL
                elif db_type == "mysql":
                    db_type_enum = DataSourceType.MYSQL
                elif db_type == "mongodb":
                    db_type_enum = DataSourceType.MONGODB
                elif db_type == "redis":
                    db_type_enum = DataSourceType.REDIS
                elif db_type == "sqlite":
                    db_type_enum = DataSourceType.SQLITE
                else:
                    raise ValueError(f"不支持的数据库类型: {db_type}")

                # 构建配置
                config = {
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "database": data.get("database"),
                    "username": data.get("username"),
                    "password": data.get("password")
                }

                # 移除空值
                config = {k: v for k, v in config.items() if v is not None and v != ""}

                print(f"🔗 正在测试 {db_type} 连接...")
                print(f"📋 配置: {config}")

                # 执行真实连接测试
                result = test_database_connection(db_type_enum, config)
                result["test_time"] = datetime.now().isoformat()
                result["mode"] = "真实连接"

                if result["success"]:
                    print(f"✅ {db_type} 连接测试成功")
                else:
                    print(f"❌ {db_type} 连接测试失败: {result['message']}")

            except Exception as e:
                print(f"❌ 连接测试异常: {str(e)}")
                result = {
                    "success": False,
                    "message": f"连接测试失败: {str(e)}",
                    "details": {"error": str(e), "type": type(e).__name__},
                    "test_time": datetime.now().isoformat(),
                    "mode": "真实连接"
                }
        else:
            # 回退到模拟模式
            result = self._simulate_connection_test(db_type, data)

        self.send_json_response(result)

    def _simulate_connection_test(self, db_type, data):
        """模拟连接测试（当真实驱动不可用时）"""
        # 模拟不同数据库的连接测试
        if db_type == "postgresql":
            result = {
                "success": True,
                "message": "PostgreSQL连接成功（模拟）",
                "details": {
                    "version": "PostgreSQL 15.0 (模拟)",
                    "driver": "psycopg2-binary",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["ACID事务", "JSON支持", "全文搜索", "并行查询"],
                    "performance": "高性能OLTP和OLAP"
                }
            }
        elif db_type == "mysql":
            result = {
                "success": True,
                "message": "MySQL连接成功（模拟）",
                "details": {
                    "version": "MySQL 8.0.35 (模拟)",
                    "driver": "pymysql",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["InnoDB引擎", "分区表", "JSON支持", "窗口函数"],
                    "performance": "高并发Web应用优化"
                }
            }
        elif db_type == "mssql":
            result = {
                "success": True,
                "message": "SQL Server连接成功（模拟）",
                "details": {
                    "version": "Microsoft SQL Server 2022 (模拟)",
                    "driver": "pyodbc + ODBC Driver 17",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["T-SQL", "列存储索引", "内存优化表", "Always Encrypted"],
                    "performance": "企业级数据仓库"
                }
            }
        elif db_type == "mongodb":
            result = {
                "success": True,
                "message": "MongoDB连接成功（模拟）",
                "details": {
                    "version": "MongoDB 7.0 (模拟)",
                    "driver": "pymongo",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["文档存储", "聚合管道", "分片", "副本集"],
                    "performance": "大数据和实时分析"
                }
            }
        elif db_type == "redis":
            result = {
                "success": True,
                "message": "Redis连接成功（模拟）",
                "details": {
                    "version": "Redis 7.2 (模拟)",
                    "driver": "redis-py",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["键值存储", "发布订阅", "Lua脚本", "集群模式"],
                    "performance": "内存数据库，毫秒级响应"
                }
            }
        elif db_type == "sqlite":
            result = {
                "success": True,
                "message": "SQLite连接成功（模拟）",
                "details": {
                    "version": "SQLite 3.39.0 (模拟)",
                    "driver": "内置sqlite3模块",
                    "database": data.get("database"),
                    "features": ["嵌入式数据库", "ACID事务", "全文搜索", "JSON支持"],
                    "performance": "轻量级，适合小型应用"
                }
            }
        else:
            result = {
                "success": False,
                "message": f"暂不支持 {db_type} 类型的连接测试"
            }

        result["test_time"] = datetime.now().isoformat()
        result["mode"] = "模拟连接"
        return result

    def handle_get_tables(self):
        """获取数据源表列表"""
        path_parts = self.path.split('/')
        ds_id = int(path_parts[4])  # /api/v1/data-sources/{id}/tables

        # 查找数据源
        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({"detail": "数据源不存在"}, status=404)
            return

        if REAL_MODE and hasattr(ds, 'real_config'):
            # 使用真实的数据库驱动获取表列表
            try:
                from app.services.database_factory import create_database_adapter

                # 转换为DataSourceType枚举
                db_type_map = {
                    "postgresql": DataSourceType.POSTGRESQL,
                    "mysql": DataSourceType.MYSQL,
                    "mssql": DataSourceType.MSSQL,
                    "mongodb": DataSourceType.MONGODB,
                    "redis": DataSourceType.REDIS,
                    "sqlite": DataSourceType.SQLITE
                }

                db_type_enum = db_type_map.get(ds["type"])
                if not db_type_enum:
                    raise ValueError(f"不支持的数据库类型: {ds['type']}")

                print(f"📊 正在获取 {ds['type']} 表列表...")

                # 创建适配器并获取表列表
                adapter = create_database_adapter(db_type_enum, ds['real_config'])
                if not adapter:
                    raise Exception("无法创建数据库适配器")

                tables = adapter.get_tables()

                # 获取详细信息
                tables_with_schema = []
                for table in tables[:20]:  # 限制数量避免过多请求
                    try:
                        schema = adapter.get_table_schema(table)
                        if ds["type"] in ["postgresql", "mysql", "mssql", "sqlite"]:
                            tables_with_schema.append({
                                "table_name": table,
                                "table_type": "BASE TABLE",
                                "column_count": len(schema),
                                "columns": [col["column_name"] for col in schema[:5]],  # 前5列
                                "mode": "真实数据"
                            })
                        elif ds["type"] == "mongodb":
                            tables_with_schema.append({
                                "collection_name": table,
                                "field_count": len(schema),
                                "sample_fields": [col["column_name"] for col in schema[:5]],
                                "mode": "真实数据"
                            })
                        elif ds["type"] == "redis":
                            tables_with_schema.append({
                                "key_pattern": table,
                                "key_type": "pattern",
                                "mode": "真实数据"
                            })
                    except Exception as e:
                        print(f"⚠️ 获取表 {table} 结构失败: {str(e)}")
                        tables_with_schema.append({
                            "table_name": table,
                            "error": str(e),
                            "mode": "真实数据"
                        })

                print(f"✅ 成功获取 {len(tables)} 个表/集合")

                response = {
                    "data_source_id": ds_id,
                    "data_source_name": ds["name"],
                    "tables": tables_with_schema,
                    "total": len(tables_with_schema),
                    "mode": "真实数据",
                    "database_type": ds["type"]
                }

            except Exception as e:
                print(f"❌ 获取表列表失败: {str(e)}")
                response = {
                    "data_source_id": ds_id,
                    "data_source_name": ds["name"],
                    "tables": [],
                    "total": 0,
                    "error": str(e),
                    "mode": "真实数据"
                }
        else:
            # 使用模拟数据
            response = self._simulate_table_list(ds_id, ds)

        self.send_json_response(response)

    def _simulate_table_list(self, ds_id, ds):
        """模拟表列表"""
        # 根据数据库类型返回不同的表结构
        if ds["type"] in ["postgresql", "mysql", "mssql"]:
            tables_with_schema = []
            for table in ds["tables"]:
                tables_with_schema.append({
                    "table_name": table,
                    "table_type": "BASE TABLE",
                    "row_count": 1000 + len(table) * 100,  # 模拟行数
                    "size": f"{len(table) * 10}MB",
                    "columns": ["id", "name", "created_at", "updated_at"],
                    "mode": "模拟数据"
                })
        elif ds["type"] == "mongodb":
            tables_with_schema = []
            for collection in ds["tables"]:
                tables_with_schema.append({
                    "collection_name": collection,
                    "document_count": 500 + len(collection) * 50,
                    "size": f"{len(collection) * 5}MB",
                    "indexes": ["_id_", f"{collection}_index"],
                    "sample_fields": ["_id", "name", "createdAt"],
                    "mode": "模拟数据"
                })
        elif ds["type"] == "redis":
            tables_with_schema = []
            for pattern in ds["tables"]:
                tables_with_schema.append({
                    "key_pattern": pattern,
                    "key_count": 100 + len(pattern) * 10,
                    "memory_usage": f"{len(pattern) * 2}MB",
                    "key_type": "hash" if "user" in pattern else "string",
                    "mode": "模拟数据"
                })
        else:
            tables_with_schema = ds["tables"]

        return {
            "data_source_id": ds_id,
            "data_source_name": ds["name"],
            "tables": tables_with_schema,
            "total": len(tables_with_schema),
            "mode": "模拟数据"
        }

    def handle_query(self):
        """执行查询"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        ds_id = data["data_source_id"]
        query = data["query"]
        limit = data.get("limit", 100)

        # 查找数据源
        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({
                "success": False,
                "message": "数据源不存在"
            })
            return

        if REAL_MODE and hasattr(ds, 'real_config'):
            # 使用真实的数据库驱动执行查询
            try:
                # 转换为DataSourceType枚举
                db_type_map = {
                    "postgresql": DataSourceType.POSTGRESQL,
                    "mysql": DataSourceType.MYSQL,
                    "mssql": DataSourceType.MSSQL,
                    "mongodb": DataSourceType.MONGODB,
                    "redis": DataSourceType.REDIS,
                    "sqlite": DataSourceType.SQLITE
                }

                db_type_enum = db_type_map.get(ds["type"])
                if not db_type_enum:
                    raise ValueError(f"不支持的数据库类型: {ds['type']}")

                print(f"🔍 正在执行 {ds['type']} 查询...")
                print(f"📝 查询语句: {query}")

                # 执行真实查询
                start_time = datetime.now()
                result = execute_database_query(
                    db_type=db_type_enum,
                    config=ds['real_config'],
                    query=query,
                    limit=limit
                )
                end_time = datetime.now()

                execution_time = (end_time - start_time).total_seconds()

                if result["success"]:
                    print(f"✅ 查询执行成功，返回 {result['total']} 条记录")
                    response = {
                        "success": True,
                        "data": result["data"],
                        "columns": result["columns"],
                        "total": result["total"],
                        "execution_time": execution_time,
                        "message": f"在 {ds['type']} 数据源上执行查询成功",
                        "mode": "真实查询",
                        "query_info": {
                            "original_query": query,
                            "database_type": ds['type'],
                            "execution_time": f"{execution_time:.3f}秒"
                        }
                    }
                else:
                    print(f"❌ 查询执行失败: {result.get('error', '未知错误')}")
                    response = {
                        "success": False,
                        "data": [],
                        "columns": [],
                        "total": 0,
                        "execution_time": execution_time,
                        "message": f"查询执行失败: {result.get('error', '未知错误')}",
                        "mode": "真实查询"
                    }

            except Exception as e:
                print(f"❌ 查询执行异常: {str(e)}")
                response = {
                    "success": False,
                    "data": [],
                    "columns": [],
                    "total": 0,
                    "execution_time": 0,
                    "message": f"查询执行失败: {str(e)}",
                    "mode": "真实查询",
                    "error_details": {"error": str(e), "type": type(e).__name__}
                }
        else:
            # 使用模拟数据
            response = self._simulate_query_execution(ds, query)

        self.send_json_response(response)

    def _simulate_query_execution(self, ds, query):
        """模拟查询执行"""
        # 根据数据库类型模拟查询结果
        if ds["type"] in ["postgresql", "mysql", "mssql", "sqlite"]:
            mock_data = [
                {"id": 1, "name": "张三", "email": "<EMAIL>", "created_at": "2024-01-01", "status": "active"},
                {"id": 2, "name": "李四", "email": "<EMAIL>", "created_at": "2024-01-02", "status": "active"},
                {"id": 3, "name": "王五", "email": "<EMAIL>", "created_at": "2024-01-03", "status": "inactive"}
            ]
            columns = ["id", "name", "email", "created_at", "status"]
        elif ds["type"] == "mongodb":
            mock_data = [
                {"_id": "507f1f77bcf86cd799439011", "name": "张三", "age": 25, "city": "北京", "tags": ["developer", "python"]},
                {"_id": "507f1f77bcf86cd799439012", "name": "李四", "age": 30, "city": "上海", "tags": ["designer", "ui"]},
                {"_id": "507f1f77bcf86cd799439013", "name": "王五", "age": 28, "city": "广州", "tags": ["manager", "team"]}
            ]
            columns = ["_id", "name", "age", "city", "tags"]
        elif ds["type"] == "redis":
            mock_data = [
                {"key": "user:1001", "type": "hash", "value": "{'name': '张三', 'age': 25}", "ttl": 3600},
                {"key": "user:1002", "type": "hash", "value": "{'name': '李四', 'age': 30}", "ttl": 3600},
                {"key": "session:abc123", "type": "string", "value": "active", "ttl": 1800}
            ]
            columns = ["key", "type", "value", "ttl"]
        else:
            mock_data = []
            columns = []

        return {
            "success": True,
            "data": mock_data,
            "columns": columns,
            "total": len(mock_data),
            "execution_time": 0.025,
            "message": f"在 {ds['type']} 数据源上执行查询成功（模拟）",
            "mode": "模拟查询",
            "query_info": {
                "original_query": query,
                "optimized": True,
                "execution_plan": f"使用 {ds['type']} 优化器（模拟）"
            }
        }

    def handle_delete(self):
        global data_sources

        ds_id = int(self.path.split('/')[-1])
        original_count = len(data_sources)
        data_sources = [ds for ds in data_sources if ds["id"] != ds_id]

        if len(data_sources) < original_count:
            self.send_json_response({"message": f"数据源 ID {ds_id} 删除成功"})
        else:
            self.send_json_response({"detail": "数据源不存在"}, status=404)

    def send_test_page(self):
        """发送测试页面"""
        html = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>数据源驱动测试平台</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    background: #f5f5f5; line-height: 1.6; padding: 20px;
                }
                .container { max-width: 1400px; margin: 0 auto; }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px;
                    text-align: center;
                }
                .card {
                    background: white; border-radius: 12px; padding: 25px; margin: 20px 0;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1); transition: transform 0.3s ease;
                }
                .card:hover { transform: translateY(-2px); }
                .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px; }
                .btn {
                    background: #667eea; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; margin: 8px; font-size: 14px;
                    transition: all 0.3s ease;
                }
                .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
                .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
                .btn-danger { background: #ff4757; }
                .btn-danger:hover { background: #ff3742; }
                .btn-success { background: #2ed573; }
                .btn-success:hover { background: #26d467; }
                .btn-warning { background: #ffa502; }
                .btn-warning:hover { background: #ff9500; }
                .form-group { margin: 15px 0; }
                .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; }
                .form-group input, .form-group select, .form-group textarea {
                    width: 100%; padding: 12px; border: 2px solid #e1e8ed; border-radius: 8px;
                    font-size: 14px; transition: border-color 0.3s ease;
                }
                .form-group input:focus, .form-group select:focus {
                    border-color: #667eea; outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
                .form-group input.error, .form-group select.error {
                    border-color: #ff4757; box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
                }
                .form-group input.success, .form-group select.success {
                    border-color: #2ed573; box-shadow: 0 0 0 3px rgba(46, 213, 115, 0.1);
                }
                .field-error { color: #ff4757; font-size: 12px; margin-top: 5px; }
                .field-success { color: #2ed573; font-size: 12px; margin-top: 5px; }
                .required { color: #ff4757; }
                .result {
                    background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;
                    font-family: 'Courier New', monospace; font-size: 13px; max-height: 400px; overflow-y: auto;
                    border-left: 4px solid #667eea;
                }
                .success { color: #2ed573; font-weight: 600; }
                .error { color: #ff4757; font-weight: 600; }
                .warning { color: #ffa502; font-weight: 600; }
                .info { color: #3742fa; font-weight: 600; }
                .data-source-item {
                    border: 2px solid #e1e8ed; padding: 20px; margin: 15px 0; border-radius: 12px;
                    transition: all 0.3s ease;
                }
                .data-source-item:hover { border-color: #667eea; }
                .status-connected { color: #2ed573; }
                .status-disconnected { color: #ff4757; }
                .feature-tag {
                    display: inline-block; background: #667eea; color: white; padding: 4px 8px;
                    border-radius: 4px; font-size: 12px; margin: 2px;
                }
                .driver-info {
                    background: #e8f4fd; padding: 15px; border-radius: 8px; margin: 10px 0;
                    border-left: 4px solid #3742fa;
                }
                .table-list {
                    display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;
                    margin: 15px 0;
                }
                .table-item {
                    background: #f1f3f4; padding: 10px; border-radius: 6px; text-align: center;
                    cursor: pointer; transition: background 0.3s ease;
                }
                .table-item:hover { background: #e8eaed; }
                .tabs { display: flex; border-bottom: 2px solid #e1e8ed; margin-bottom: 20px; }
                .tab {
                    padding: 12px 24px; cursor: pointer; border-bottom: 2px solid transparent;
                    transition: all 0.3s ease;
                }
                .tab.active { border-bottom-color: #667eea; color: #667eea; font-weight: 600; }
                .tab-content { display: none; }
                .tab-content.active { display: block; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🗄️ 数据源驱动测试平台</h1>
                    <p>支持多种数据库类型的精准适配和连接测试</p>
                    <div style="margin-top: 15px;">
                        <span class="feature-tag">PostgreSQL</span>
                        <span class="feature-tag">MySQL</span>
                        <span class="feature-tag">SQL Server</span>
                        <span class="feature-tag">MongoDB</span>
                        <span class="feature-tag">Redis</span>
                        <span class="feature-tag">SQLite</span>
                    </div>
                </div>

                <div class="tabs">
                    <div class="tab active" onclick="switchTab('create')">创建数据源</div>
                    <div class="tab" onclick="switchTab('list')">数据源列表</div>
                    <div class="tab" onclick="switchTab('test')">连接测试</div>
                    <div class="tab" onclick="switchTab('query')">查询测试</div>
                    <div class="tab" onclick="switchTab('drivers')">驱动信息</div>
                </div>

                <!-- 创建数据源 -->
                <div id="create-tab" class="tab-content active">
                    <div class="grid">
                        <div class="card">
                            <h3>📝 创建新数据源</h3>
                            <div class="form-group">
                                <label>数据源名称:</label>
                                <input type="text" id="dsName" placeholder="例如: 生产环境MySQL" />
                            </div>
                            <div class="form-group">
                                <label>描述:</label>
                                <textarea id="dsDescription" placeholder="数据源用途描述"></textarea>
                            </div>
                            <div class="form-group">
                                <label>数据库类型:</label>
                                <select id="dsType" onchange="updateConnectionForm()">
                                    <option value="">请选择数据库类型</option>
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="mysql">MySQL</option>
                                    <option value="mssql">SQL Server</option>
                                    <option value="mongodb">MongoDB</option>
                                    <option value="redis">Redis</option>
                                    <option value="sqlite">SQLite</option>
                                </select>
                            </div>
                        </div>

                        <div class="card">
                            <h3>🔗 连接配置</h3>
                            <div id="connectionForm">
                                <p class="info">请先选择数据库类型</p>
                            </div>
                            <div style="margin-top: 20px;">
                                <button class="btn" onclick="testCreateConnection()" id="testConnBtn" disabled>
                                    🔍 测试连接
                                </button>
                                <button class="btn btn-success" onclick="createDataSource()" id="createBtn" disabled>
                                    ✅ 创建数据源
                                </button>
                                <button class="btn btn-warning" onclick="resetCreateForm()" id="resetBtn">
                                    🔄 重置表单
                                </button>
                            </div>
                            <div id="connectionTestResult" class="result" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>📋 操作日志</h3>
                        <div id="createResult" class="result"></div>
                    </div>
                </div>

                <!-- 数据源列表 -->
                <div id="list-tab" class="tab-content">
                    <div class="card">
                        <h3>📊 数据源管理</h3>
                        <button class="btn" onclick="loadDataSources()">刷新列表</button>
                        <button class="btn" onclick="loadAllTables()">加载所有表信息</button>
                        <div id="dataSourceList"></div>
                    </div>
                </div>

                <!-- 连接测试 -->
                <div id="test-tab" class="tab-content">
                    <div class="card">
                        <h3>🔗 连接测试</h3>
                        <div class="grid">
                            <div>
                                <div class="form-group">
                                    <label>测试类型:</label>
                                    <select id="testType" onchange="updateTestForm()">
                                        <option value="postgresql">PostgreSQL</option>
                                        <option value="mysql">MySQL</option>
                                        <option value="mssql">SQL Server</option>
                                        <option value="mongodb">MongoDB</option>
                                        <option value="redis">Redis</option>
                                        <option value="sqlite">SQLite</option>
                                    </select>
                                </div>
                                <div id="testForm"></div>
                                <button class="btn" onclick="testConnection()">测试连接</button>
                            </div>
                            <div>
                                <h4>连接结果</h4>
                                <div id="testResult" class="result"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 查询测试 -->
                <div id="query-tab" class="tab-content">
                    <div class="card">
                        <h3>🔍 查询测试</h3>
                        <div class="form-group">
                            <label>选择数据源:</label>
                            <select id="queryDataSource">
                                <option value="">请选择数据源</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>SQL查询:</label>
                            <textarea id="sqlQuery" rows="4" placeholder="输入SQL查询语句"></textarea>
                        </div>
                        <button class="btn" onclick="executeQuery()">执行查询</button>
                        <div id="queryResult" class="result"></div>
                    </div>
                </div>

                <!-- 驱动信息 -->
                <div id="drivers-tab" class="tab-content">
                    <div class="card">
                        <h3>🔧 数据库驱动信息</h3>
                        <button class="btn" onclick="loadDriverInfo()">加载驱动信息</button>
                        <div id="driverInfo"></div>
                    </div>
                </div>
            </div>

            <script>
                let token = null;
                let supportedTypes = [];

                // 自动登录
                async function autoLogin() {
                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ username: 'admin', password: 'admin' })
                        });
                        const data = await response.json();
                        token = data.access_token;
                        log('✅ 自动登录成功', 'createResult');
                        loadSupportedTypes();
                    } catch (error) {
                        log('❌ 登录失败: ' + error.message, 'createResult');
                    }
                }

                // 加载支持的数据库类型
                async function loadSupportedTypes() {
                    try {
                        const response = await fetch('/api/v1/data-sources/types', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });
                        supportedTypes = await response.json();
                        log(`📋 支持的数据库类型: ${supportedTypes.join(', ')}`, 'createResult');
                    } catch (error) {
                        log('❌ 加载数据库类型失败: ' + error.message, 'createResult');
                    }
                }

                // 切换标签页
                function switchTab(tabName) {
                    // 隐藏所有标签内容
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    document.querySelectorAll('.tab').forEach(tab => {
                        tab.classList.remove('active');
                    });

                    // 显示选中的标签
                    document.getElementById(tabName + '-tab').classList.add('active');
                    event.target.classList.add('active');

                    // 加载对应数据
                    if (tabName === 'list') {
                        loadDataSources();
                    } else if (tabName === 'test') {
                        updateTestForm();
                    } else if (tabName === 'query') {
                        loadDataSourcesForQuery();
                    }
                }

                // 记录日志
                function log(message, targetId = 'createResult') {
                    const resultDiv = document.getElementById(targetId);
                    const timestamp = new Date().toLocaleTimeString();
                    resultDiv.innerHTML = `[${timestamp}] ${message}<br>` + resultDiv.innerHTML;
                }

                // 页面加载完成后自动登录
                window.onload = function() {
                    autoLogin();
                    updateTestForm();
                    updateButtonStates();

                    // 为数据源名称添加验证
                    const nameField = document.getElementById('dsName');
                    if (nameField) {
                        nameField.addEventListener('input', function() {
                            updateButtonStates();
                        });
                    }
                };
            </script>
        </body>
        </html>
        """

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))


def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://localhost:8000')


def run_server():
    """启动测试服务器"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, DatabaseDriverTestHandler)

    print("🚀 数据源驱动测试平台启动成功!")
    print("=" * 60)
    print(f"📱 访问地址: http://localhost:8000")
    print(f"🗄️ 支持数据库: PostgreSQL, MySQL, SQL Server, MongoDB, Redis, SQLite")
    print(f"🔧 功能特性: 精准适配, 连接测试, 查询执行, 表结构分析")
    print("=" * 60)
    print("⚡ 正在自动打开浏览器...")
    print("按 Ctrl+C 停止服务器")
    print()

    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 数据源驱动测试平台已停止")
        httpd.server_close()


if __name__ == '__main__':
    run_server()

class DatabaseDriverTestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_test_page()
        elif self.path == '/api/v1/data-sources/':
            self.handle_get_list()
        elif self.path == '/api/v1/data-sources/types':
            self.handle_get_types()
        elif self.path.startswith('/api/v1/data-sources/types/'):
            self.handle_get_type_requirements()
        elif '/tables' in self.path:
            self.handle_get_tables()
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        if self.path == '/api/v1/auth/login':
            self.handle_login()
        elif self.path == '/api/v1/data-sources/':
            self.handle_create()
        elif self.path == '/api/v1/data-sources/test':
            self.handle_test_connection()
        elif self.path == '/api/v1/data-sources/query':
            self.handle_query()
        else:
            self.send_response(404)
            self.end_headers()

    def do_DELETE(self):
        if self.path.startswith('/api/v1/data-sources/'):
            self.handle_delete()
        else:
            self.send_response(404)
            self.end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def handle_login(self):
        self.send_json_response({
            "access_token": "test_token_12345",
            "token_type": "bearer"
        })

    def handle_get_types(self):
        """获取支持的数据库类型"""
        types = [
            "postgresql", "mysql", "sqlserver", "mssql",
            "mongodb", "redis", "sqlite", "oracle"
        ]
        self.send_json_response(types)

    def handle_get_type_requirements(self):
        """获取数据库类型要求"""
        path_parts = self.path.split('/')
        db_type = path_parts[4]  # /api/v1/data-sources/types/{type}/requirements

        requirements = {
            "postgresql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 5432,
                "supports_ssl": True,
                "driver": "psycopg2-binary"
            },
            "mysql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 3306,
                "supports_ssl": True,
                "driver": "pymysql"
            },
            "mssql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 1433,
                "supports_ssl": True,
                "driver": "pyodbc"
            },
            "mongodb": {
                "required_fields": ["host", "port", "database"],
                "optional_fields": ["username", "password"],
                "default_port": 27017,
                "supports_ssl": True,
                "driver": "pymongo"
            },
            "redis": {
                "required_fields": ["host", "port"],
                "optional_fields": ["password"],
                "default_port": 6379,
                "supports_ssl": False,
                "driver": "redis"
            },
            "sqlite": {
                "required_fields": ["database"],
                "default_port": 0,
                "supports_ssl": False,
                "driver": "built-in"
            }
        }

        self.send_json_response(requirements.get(db_type, {}))

    def handle_create(self):
        global data_sources, next_id

        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        new_ds = {
            "id": next_id,
            "name": data["name"],
            "description": data.get("description", ""),
            "type": data["type"],
            "host": data.get("host", ""),
            "port": data.get("port", 0),
            "database": data.get("database", ""),
            "username": data.get("username", ""),
            "status": "disconnected",
            "created_at": datetime.now().isoformat(),
            "tables": []  # 模拟表列表
        }

        # 根据数据库类型添加模拟表
        if data["type"] in ["postgresql", "mysql", "mssql"]:
            new_ds["tables"] = ["users", "orders", "products", "categories"]
        elif data["type"] == "mongodb":
            new_ds["tables"] = ["users", "orders", "products", "logs"]
        elif data["type"] == "redis":
            new_ds["tables"] = ["user:*", "session:*", "cache:*"]
        elif data["type"] == "sqlite":
            new_ds["tables"] = ["users", "settings"]

        data_sources.append(new_ds)
        next_id += 1

        print(f"✅ 创建数据源: {new_ds['name']} (ID: {new_ds['id']}, 类型: {new_ds['type']})")

        self.send_json_response(new_ds)

    def handle_get_list(self):
        global data_sources

        self.send_json_response({
            "items": data_sources,
            "total": len(data_sources)
        })

    def handle_test_connection(self):
        """测试数据库连接"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        db_type = data["type"]

        # 模拟不同数据库的连接测试
        if db_type == "postgresql":
            result = {
                "success": True,
                "message": "PostgreSQL连接成功",
                "details": {
                    "version": "PostgreSQL 15.0",
                    "driver": "psycopg2-binary",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["ACID事务", "JSON支持", "全文搜索", "并行查询"]
                }
            }
        elif db_type == "mysql":
            result = {
                "success": True,
                "message": "MySQL连接成功",
                "details": {
                    "version": "MySQL 8.0.35",
                    "driver": "pymysql",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["InnoDB引擎", "分区表", "JSON支持", "窗口函数"]
                }
            }
        elif db_type == "mssql":
            result = {
                "success": True,
                "message": "SQL Server连接成功",
                "details": {
                    "version": "Microsoft SQL Server 2022",
                    "driver": "pyodbc + ODBC Driver 17",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["T-SQL", "列存储索引", "内存优化表", "Always Encrypted"]
                }
            }
        elif db_type == "mongodb":
            result = {
                "success": True,
                "message": "MongoDB连接成功",
                "details": {
                    "version": "MongoDB 7.0",
                    "driver": "pymongo",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["文档存储", "聚合管道", "分片", "副本集"]
                }
            }
        elif db_type == "redis":
            result = {
                "success": True,
                "message": "Redis连接成功",
                "details": {
                    "version": "Redis 7.2",
                    "driver": "redis-py",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["键值存储", "发布订阅", "Lua脚本", "集群模式"]
                }
            }
        elif db_type == "sqlite":
            result = {
                "success": True,
                "message": "SQLite连接成功",
                "details": {
                    "version": "SQLite 3.39.0",
                    "driver": "内置sqlite3模块",
                    "database": data.get("database"),
                    "features": ["嵌入式数据库", "ACID事务", "全文搜索", "JSON支持"]
                }
            }
        else:
            result = {
                "success": False,
                "message": f"暂不支持 {db_type} 类型的连接测试"
            }

        result["test_time"] = datetime.now().isoformat()
        self.send_json_response(result)

    def handle_get_tables(self):
        """获取数据源表列表"""
        path_parts = self.path.split('/')
        ds_id = int(path_parts[4])  # /api/v1/data-sources/{id}/tables

        # 查找数据源
        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({"detail": "数据源不存在"}, status=404)
            return

        # 根据数据库类型返回不同的表结构
        if ds["type"] in ["postgresql", "mysql", "mssql"]:
            tables_with_schema = []
            for table in ds["tables"]:
                tables_with_schema.append({
                    "table_name": table,
                    "table_type": "BASE TABLE",
                    "row_count": 1000 + len(table) * 100,  # 模拟行数
                    "size": f"{len(table) * 10}MB"
                })
        elif ds["type"] == "mongodb":
            tables_with_schema = []
            for collection in ds["tables"]:
                tables_with_schema.append({
                    "collection_name": collection,
                    "document_count": 500 + len(collection) * 50,
                    "size": f"{len(collection) * 5}MB",
                    "indexes": ["_id_", f"{collection}_index"]
                })
        else:
            tables_with_schema = ds["tables"]

        self.send_json_response({
            "data_source_id": ds_id,
            "data_source_name": ds["name"],
            "tables": tables_with_schema,
            "total": len(tables_with_schema)
        })

    def handle_query(self):
        """执行查询"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        ds_id = data["data_source_id"]
        query = data["query"]

        # 查找数据源
        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({
                "success": False,
                "message": "数据源不存在"
            })
            return

        # 根据数据库类型模拟查询结果
        if ds["type"] in ["postgresql", "mysql", "mssql", "sqlite"]:
            mock_data = [
                {"id": 1, "name": "张三", "email": "<EMAIL>", "created_at": "2024-01-01"},
                {"id": 2, "name": "李四", "email": "<EMAIL>", "created_at": "2024-01-02"},
                {"id": 3, "name": "王五", "email": "<EMAIL>", "created_at": "2024-01-03"}
            ]
            columns = ["id", "name", "email", "created_at"]
        elif ds["type"] == "mongodb":
            mock_data = [
                {"_id": "507f1f77bcf86cd799439011", "name": "张三", "age": 25, "city": "北京"},
                {"_id": "507f1f77bcf86cd799439012", "name": "李四", "age": 30, "city": "上海"},
                {"_id": "507f1f77bcf86cd799439013", "name": "王五", "age": 28, "city": "广州"}
            ]
            columns = ["_id", "name", "age", "city"]
        elif ds["type"] == "redis":
            mock_data = [
                {"key": "user:1001", "type": "hash", "value": "{'name': '张三', 'age': 25}"},
                {"key": "user:1002", "type": "hash", "value": "{'name': '李四', 'age': 30}"},
                {"key": "session:abc123", "type": "string", "value": "active"}
            ]
            columns = ["key", "type", "value"]
        else:
            mock_data = []
            columns = []

        self.send_json_response({
            "success": True,
            "data": mock_data,
            "columns": columns,
            "total": len(mock_data),
            "execution_time": 0.025,
            "message": f"在 {ds['type']} 数据源上执行查询成功"
        })

    def handle_delete(self):
        global data_sources

        ds_id = int(self.path.split('/')[-1])
        original_count = len(data_sources)
        data_sources = [ds for ds in data_sources if ds["id"] != ds_id]

        if len(data_sources) < original_count:
            self.send_json_response({"message": f"数据源 ID {ds_id} 删除成功"})
        else:
            self.send_json_response({"detail": "数据源不存在"}, status=404)