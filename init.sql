-- 初始化数据库脚本

-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS bi_platform;

-- 使用数据库
-- \c bi_platform;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建初始超级管理员用户（将在应用启动时通过代码创建）
-- 这里只是预留，实际用户创建在应用代码中处理

-- 创建一些示例数据表（用于演示）
CREATE TABLE IF NOT EXISTS sample_sales (
    id SERIAL PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    sales_amount DECIMAL(10, 2) NOT NULL,
    sales_date DATE NOT NULL,
    region VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例销售数据
INSERT INTO sample_sales (product_name, category, sales_amount, sales_date, region) VALUES
('iPhone 15', '电子产品', 8999.00, '2024-01-01', '北京'),
('MacBook Pro', '电子产品', 15999.00, '2024-01-01', '上海'),
('AirPods Pro', '电子产品', 1999.00, '2024-01-02', '广州'),
('iPad Air', '电子产品', 4999.00, '2024-01-02', '深圳'),
('Apple Watch', '电子产品', 2999.00, '2024-01-03', '杭州'),
('Nike运动鞋', '服装', 899.00, '2024-01-03', '北京'),
('Adidas T恤', '服装', 299.00, '2024-01-04', '上海'),
('Levi\'s牛仔裤', '服装', 599.00, '2024-01-04', '广州'),
('星巴克咖啡', '食品', 35.00, '2024-01-05', '深圳'),
('哈根达斯冰淇淋', '食品', 68.00, '2024-01-05', '杭州');

-- 创建示例用户数据表
CREATE TABLE IF NOT EXISTS sample_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    age INTEGER,
    gender VARCHAR(10),
    city VARCHAR(50),
    registration_date DATE NOT NULL,
    last_login_date TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例用户数据
INSERT INTO sample_users (username, email, age, gender, city, registration_date, last_login_date) VALUES
('张三', '<EMAIL>', 25, '男', '北京', '2024-01-01', '2024-01-15 10:30:00'),
('李四', '<EMAIL>', 30, '女', '上海', '2024-01-02', '2024-01-15 09:45:00'),
('王五', '<EMAIL>', 28, '男', '广州', '2024-01-03', '2024-01-14 16:20:00'),
('赵六', '<EMAIL>', 35, '女', '深圳', '2024-01-04', '2024-01-14 14:10:00'),
('钱七', '<EMAIL>', 22, '男', '杭州', '2024-01-05', '2024-01-13 11:55:00'),
('孙八', '<EMAIL>', 27, '女', '成都', '2024-01-06', '2024-01-13 08:30:00'),
('周九', '<EMAIL>', 32, '男', '武汉', '2024-01-07', '2024-01-12 15:40:00'),
('吴十', '<EMAIL>', 29, '女', '西安', '2024-01-08', '2024-01-12 13:25:00');

-- 创建示例订单数据表
CREATE TABLE IF NOT EXISTS sample_orders (
    id SERIAL PRIMARY KEY,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    user_id INTEGER REFERENCES sample_users(id),
    product_name VARCHAR(100) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    order_status VARCHAR(20) DEFAULT '待处理',
    order_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例订单数据
INSERT INTO sample_orders (order_number, user_id, product_name, quantity, unit_price, total_amount, order_status, order_date) VALUES
('ORD20240101001', 1, 'iPhone 15', 1, 8999.00, 8999.00, '已完成', '2024-01-01 10:30:00'),
('ORD20240101002', 2, 'MacBook Pro', 1, 15999.00, 15999.00, '已完成', '2024-01-01 14:20:00'),
('ORD20240102001', 3, 'AirPods Pro', 2, 1999.00, 3998.00, '已完成', '2024-01-02 09:15:00'),
('ORD20240102002', 4, 'iPad Air', 1, 4999.00, 4999.00, '处理中', '2024-01-02 16:45:00'),
('ORD20240103001', 5, 'Apple Watch', 1, 2999.00, 2999.00, '已完成', '2024-01-03 11:30:00'),
('ORD20240103002', 6, 'Nike运动鞋', 1, 899.00, 899.00, '已完成', '2024-01-03 15:20:00'),
('ORD20240104001', 7, 'Adidas T恤', 3, 299.00, 897.00, '已完成', '2024-01-04 12:10:00'),
('ORD20240104002', 8, 'Levi\'s牛仔裤', 2, 599.00, 1198.00, '待发货', '2024-01-04 17:30:00');

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_sample_sales_date ON sample_sales(sales_date);
CREATE INDEX IF NOT EXISTS idx_sample_sales_region ON sample_sales(region);
CREATE INDEX IF NOT EXISTS idx_sample_sales_category ON sample_sales(category);
CREATE INDEX IF NOT EXISTS idx_sample_users_city ON sample_users(city);
CREATE INDEX IF NOT EXISTS idx_sample_users_registration_date ON sample_users(registration_date);
CREATE INDEX IF NOT EXISTS idx_sample_orders_order_date ON sample_orders(order_date);
CREATE INDEX IF NOT EXISTS idx_sample_orders_status ON sample_orders(order_status);
