/**
 * 系统设置页面组件
 */

import React, { useState } from 'react'
import {
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Switch,
  Select,
  Upload,
  Avatar,
  Space,
  Typography,
  Divider,
  message,
} from 'antd'
import {
  UserOutlined,
  UploadOutlined,
  LockOutlined,
  BellOutlined,
  GlobalOutlined,
} from '@ant-design/icons'

import { useAppSelector } from '@/store/hooks'

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

const Settings: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth)
  const [activeTab, setActiveTab] = useState('profile')
  const [profileForm] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [notificationForm] = Form.useForm()
  const [systemForm] = Form.useForm()

  // 初始化表单数据
  React.useEffect(() => {
    if (user) {
      profileForm.setFieldsValue({
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        phone: user.phone,
        department: user.department,
        position: user.position,
        bio: user.bio,
      })

      notificationForm.setFieldsValue({
        email_notifications: user.email_notifications,
        push_notifications: user.push_notifications,
      })

      systemForm.setFieldsValue({
        language: user.language,
        theme: user.theme,
        timezone: user.timezone,
      })
    }
  }, [user, profileForm, notificationForm, systemForm])

  const handleProfileSubmit = async (values: any) => {
    try {
      console.log('更新个人资料:', values)
      message.success('个人资料更新成功')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const handlePasswordSubmit = async (values: any) => {
    try {
      console.log('修改密码:', values)
      message.success('密码修改成功')
      passwordForm.resetFields()
    } catch (error) {
      message.error('密码修改失败')
    }
  }

  const handleNotificationSubmit = async (values: any) => {
    try {
      console.log('更新通知设置:', values)
      message.success('通知设置更新成功')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const handleSystemSubmit = async (values: any) => {
    try {
      console.log('更新系统设置:', values)
      message.success('系统设置更新成功')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功')
    } else if (info.file.status === 'error') {
      message.error('头像上传失败')
    }
  }

  return (
    <div>
      <Title level={2}>系统设置</Title>
      
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <UserOutlined />
                个人资料
              </Space>
            }
            key="profile"
          >
            <div style={{ maxWidth: 600 }}>
              <div style={{ marginBottom: 24, textAlign: 'center' }}>
                <Avatar
                  size={100}
                  src={user?.avatar_url}
                  icon={<UserOutlined />}
                  style={{ marginBottom: 16 }}
                />
                <div>
                  <Upload
                    name="avatar"
                    showUploadList={false}
                    action="/api/v1/users/avatar"
                    onChange={handleAvatarUpload}
                  >
                    <Button icon={<UploadOutlined />}>更换头像</Button>
                  </Upload>
                </div>
              </div>

              <Form
                form={profileForm}
                layout="vertical"
                onFinish={handleProfileSubmit}
              >
                <Form.Item
                  name="username"
                  label="用户名"
                  rules={[{ required: true, message: '请输入用户名' }]}
                >
                  <Input disabled />
                </Form.Item>

                <Form.Item
                  name="email"
                  label="邮箱"
                  rules={[
                    { required: true, message: '请输入邮箱' },
                    { type: 'email', message: '请输入有效的邮箱地址' },
                  ]}
                >
                  <Input />
                </Form.Item>

                <Form.Item name="full_name" label="姓名">
                  <Input placeholder="请输入姓名" />
                </Form.Item>

                <Form.Item name="phone" label="手机号">
                  <Input placeholder="请输入手机号" />
                </Form.Item>

                <Form.Item name="department" label="部门">
                  <Input placeholder="请输入部门" />
                </Form.Item>

                <Form.Item name="position" label="职位">
                  <Input placeholder="请输入职位" />
                </Form.Item>

                <Form.Item name="bio" label="个人简介">
                  <Input.TextArea rows={4} placeholder="请输入个人简介" />
                </Form.Item>

                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    保存更改
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <LockOutlined />
                安全设置
              </Space>
            }
            key="security"
          >
            <div style={{ maxWidth: 600 }}>
              <Title level={4}>修改密码</Title>
              <Form
                form={passwordForm}
                layout="vertical"
                onFinish={handlePasswordSubmit}
              >
                <Form.Item
                  name="current_password"
                  label="当前密码"
                  rules={[{ required: true, message: '请输入当前密码' }]}
                >
                  <Input.Password placeholder="请输入当前密码" />
                </Form.Item>

                <Form.Item
                  name="new_password"
                  label="新密码"
                  rules={[
                    { required: true, message: '请输入新密码' },
                    { min: 6, message: '密码至少6位字符' },
                  ]}
                >
                  <Input.Password placeholder="请输入新密码" />
                </Form.Item>

                <Form.Item
                  name="confirm_password"
                  label="确认新密码"
                  dependencies={['new_password']}
                  rules={[
                    { required: true, message: '请确认新密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('new_password') === value) {
                          return Promise.resolve()
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'))
                      },
                    }),
                  ]}
                >
                  <Input.Password placeholder="请再次输入新密码" />
                </Form.Item>

                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    修改密码
                  </Button>
                </Form.Item>
              </Form>

              <Divider />

              <Title level={4}>两步验证</Title>
              <div style={{ marginBottom: 16 }}>
                <Space>
                  <Text>两步验证状态:</Text>
                  <Text type="secondary">未启用</Text>
                  <Button type="link">启用</Button>
                </Space>
              </div>
              <Text type="secondary">
                启用两步验证可以为您的账户提供额外的安全保护
              </Text>
            </div>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <BellOutlined />
                通知设置
              </Space>
            }
            key="notifications"
          >
            <div style={{ maxWidth: 600 }}>
              <Form
                form={notificationForm}
                layout="vertical"
                onFinish={handleNotificationSubmit}
              >
                <Form.Item
                  name="email_notifications"
                  label="邮件通知"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Text type="secondary" style={{ marginLeft: 8 }}>
                  接收重要系统通知和数据分析结果
                </Text>

                <Divider />

                <Form.Item
                  name="push_notifications"
                  label="推送通知"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Text type="secondary" style={{ marginLeft: 8 }}>
                  接收浏览器推送通知
                </Text>

                <Divider />

                <Form.Item
                  name="data_alerts"
                  label="数据告警"
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch />
                </Form.Item>
                <Text type="secondary" style={{ marginLeft: 8 }}>
                  当数据异常时接收告警通知
                </Text>

                <Divider />

                <Form.Item
                  name="report_notifications"
                  label="报告通知"
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch />
                </Form.Item>
                <Text type="secondary" style={{ marginLeft: 8 }}>
                  定期接收数据分析报告
                </Text>

                <Form.Item style={{ marginTop: 24 }}>
                  <Button type="primary" htmlType="submit">
                    保存设置
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <GlobalOutlined />
                系统偏好
              </Space>
            }
            key="system"
          >
            <div style={{ maxWidth: 600 }}>
              <Form
                form={systemForm}
                layout="vertical"
                onFinish={handleSystemSubmit}
              >
                <Form.Item
                  name="language"
                  label="语言"
                  rules={[{ required: true, message: '请选择语言' }]}
                >
                  <Select>
                    <Option value="zh">中文</Option>
                    <Option value="en">English</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="theme"
                  label="主题"
                  rules={[{ required: true, message: '请选择主题' }]}
                >
                  <Select>
                    <Option value="light">浅色主题</Option>
                    <Option value="dark">深色主题</Option>
                    <Option value="auto">跟随系统</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="timezone"
                  label="时区"
                  rules={[{ required: true, message: '请选择时区' }]}
                >
                  <Select>
                    <Option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</Option>
                    <Option value="America/New_York">America/New_York (UTC-5)</Option>
                    <Option value="Europe/London">Europe/London (UTC+0)</Option>
                    <Option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="date_format"
                  label="日期格式"
                  initialValue="YYYY-MM-DD"
                >
                  <Select>
                    <Option value="YYYY-MM-DD">2024-01-01</Option>
                    <Option value="MM/DD/YYYY">01/01/2024</Option>
                    <Option value="DD/MM/YYYY">01/01/2024</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="number_format"
                  label="数字格式"
                  initialValue="1,234.56"
                >
                  <Select>
                    <Option value="1,234.56">1,234.56</Option>
                    <Option value="1.234,56">1.234,56</Option>
                    <Option value="1 234.56">1 234.56</Option>
                  </Select>
                </Form.Item>

                <Form.Item style={{ marginTop: 24 }}>
                  <Button type="primary" htmlType="submit">
                    保存设置
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default Settings
