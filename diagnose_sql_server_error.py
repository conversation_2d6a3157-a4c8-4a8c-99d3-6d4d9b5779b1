#!/usr/bin/env python3
"""
SQL Server连接错误诊断工具

详细诊断SQL Server连接问题，包括ODBC驱动、连接字符串、网络等
"""

import sys
import socket
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 70)
    print("🔍 SQL Server连接错误诊断工具")
    print("=" * 70)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)

def test_pyodbc_import():
    """测试pyodbc导入"""
    print("\n📦 测试pyodbc模块...")
    try:
        import pyodbc
        print(f"✅ pyodbc导入成功，版本: {getattr(pyodbc, 'version', 'Unknown')}")
        return pyodbc
    except ImportError as e:
        print(f"❌ pyodbc导入失败: {e}")
        return None

def get_available_drivers(pyodbc):
    """获取可用驱动"""
    print("\n🗄️ 获取可用ODBC驱动...")
    try:
        drivers = pyodbc.drivers()
        print(f"📋 找到 {len(drivers)} 个ODBC驱动:")
        for i, driver in enumerate(drivers, 1):
            print(f"   {i}. {driver}")
        return drivers
    except Exception as e:
        print(f"❌ 获取驱动列表失败: {e}")
        return []

def find_sql_server_driver(drivers):
    """查找SQL Server驱动"""
    print("\n🔍 查找SQL Server驱动...")
    
    sql_server_drivers = [
        'ODBC Driver 18 for SQL Server',
        'ODBC Driver 17 for SQL Server', 
        'ODBC Driver 13 for SQL Server',
        'ODBC Driver 11 for SQL Server',
        'SQL Server Native Client 11.0',
        'SQL Server Native Client 10.0',
        'SQL Server'
    ]
    
    found_drivers = []
    for driver in sql_server_drivers:
        if driver in drivers:
            print(f"✅ {driver} - 可用")
            found_drivers.append(driver)
        else:
            print(f"❌ {driver} - 未安装")
    
    if found_drivers:
        recommended = found_drivers[0]
        print(f"\n🔧 推荐使用: {recommended}")
        return recommended
    else:
        print(f"\n❌ 未找到任何SQL Server驱动")
        return None

def test_connection_string_building(driver):
    """测试连接字符串构建"""
    print(f"\n🧪 测试连接字符串构建...")
    
    # 测试参数
    host = 'localhost'
    port = 1433
    database = 'master'
    username = 'sa'
    password = 'test123'
    
    try:
        conn_str = f'DRIVER={{{driver}}};SERVER={host},{port};DATABASE={database};UID={username};PWD={password};Timeout=10'
        print(f"✅ 连接字符串构建成功:")
        print(f"   {conn_str.replace(password, '***')}")
        return conn_str
    except Exception as e:
        print(f"❌ 连接字符串构建失败: {e}")
        return None

def test_network_connection():
    """测试网络连接"""
    print(f"\n🌐 测试网络连接...")
    
    host = 'localhost'
    port = 1433
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ 网络连接成功: {host}:{port}")
            return True
        else:
            print(f"❌ 网络连接失败: {host}:{port} (错误代码: {result})")
            print(f"💡 这是正常的，因为本地没有运行SQL Server服务器")
            return False
    except Exception as e:
        print(f"❌ 网络测试异常: {e}")
        return False

def test_actual_connection(pyodbc, conn_str):
    """测试实际数据库连接"""
    print(f"\n🔗 测试实际数据库连接...")
    
    try:
        print(f"🔄 尝试连接...")
        conn = pyodbc.connect(conn_str)
        print(f"✅ 连接成功!")
        
        cursor = conn.cursor()
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()[0]
        print(f"📊 SQL Server版本: {version[:100]}...")
        
        conn.close()
        return True, "连接成功"
        
    except pyodbc.Error as e:
        error_code = e.args[0] if e.args else ''
        error_msg = str(e)
        
        print(f"❌ 连接失败:")
        print(f"   错误代码: {error_code}")
        print(f"   错误信息: {error_msg}")
        
        # 分析错误类型
        if 'IM002' in error_code:
            analysis = "ODBC驱动问题: 未找到数据源名称或默认驱动程序"
            suggestion = "检查ODBC驱动是否正确安装和注册"
        elif 'login failed' in error_msg.lower():
            analysis = "认证失败: 用户名或密码错误"
            suggestion = "检查SQL Server认证设置"
        elif 'cannot open database' in error_msg.lower():
            analysis = "数据库访问失败: 数据库不存在或无权限"
            suggestion = "检查数据库名称和用户权限"
        elif '08001' in error_code or 'network' in error_msg.lower():
            analysis = "网络连接失败: 无法连接到SQL Server"
            suggestion = "检查SQL Server是否运行，网络连接是否正常"
        else:
            analysis = "未知错误"
            suggestion = "查看详细错误信息进行排查"
        
        print(f"\n🔍 错误分析: {analysis}")
        print(f"💡 建议: {suggestion}")
        
        return False, f"{error_code}: {error_msg}"
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ 连接异常: {error_msg}")
        return False, f"异常: {error_msg}"

def test_odbc_driver_18_specific():
    """测试ODBC Driver 18的特殊配置"""
    print(f"\n🔧 测试ODBC Driver 18特殊配置...")
    
    try:
        import pyodbc
        
        # ODBC Driver 18需要特殊的SSL配置
        host = 'localhost'
        port = 1433
        database = 'master'
        username = 'sa'
        password = 'test123'
        
        # 尝试不同的连接字符串配置
        configs = [
            {
                'name': '标准配置',
                'conn_str': f'DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={host},{port};DATABASE={database};UID={username};PWD={password};Timeout=10'
            },
            {
                'name': '禁用SSL加密',
                'conn_str': f'DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={host},{port};DATABASE={database};UID={username};PWD={password};Encrypt=no;Timeout=10'
            },
            {
                'name': '信任服务器证书',
                'conn_str': f'DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={host},{port};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Timeout=10'
            },
            {
                'name': '禁用SSL+信任证书',
                'conn_str': f'DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={host},{port};DATABASE={database};UID={username};PWD={password};Encrypt=no;TrustServerCertificate=yes;Timeout=10'
            }
        ]
        
        for config in configs:
            print(f"\n🧪 测试配置: {config['name']}")
            print(f"   连接字符串: {config['conn_str'].replace(password, '***')}")
            
            try:
                conn = pyodbc.connect(config['conn_str'])
                print(f"   ✅ 连接成功!")
                conn.close()
                return config['conn_str']
            except pyodbc.Error as e:
                error_code = e.args[0] if e.args else ''
                print(f"   ❌ 连接失败: {error_code}")
            except Exception as e:
                print(f"   ❌ 连接异常: {str(e)}")
        
        print(f"\n❌ 所有配置都失败了")
        return None
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 70)
    print("💡 解决方案建议")
    print("=" * 70)
    
    print("\n🔧 如果仍然出现ODBC驱动错误:")
    print("1. 重新安装ODBC Driver 18 for SQL Server")
    print("2. 以管理员身份运行安装程序")
    print("3. 重启计算机以确保驱动注册生效")
    print("4. 检查系统架构匹配 (32位 vs 64位)")
    
    print("\n🌐 如果出现网络连接错误:")
    print("1. 这是正常的，因为本地没有SQL Server服务器")
    print("2. 重要的是不再出现ODBC驱动错误")
    print("3. 可以安装SQL Server Express进行完整测试")
    
    print("\n🔒 如果出现SSL/加密错误:")
    print("1. 使用 Encrypt=no 禁用SSL加密")
    print("2. 使用 TrustServerCertificate=yes 信任服务器证书")
    print("3. 这些是ODBC Driver 18的新安全要求")

def main():
    """主函数"""
    print_header()
    
    # 测试pyodbc
    pyodbc = test_pyodbc_import()
    if not pyodbc:
        return
    
    # 获取驱动列表
    drivers = get_available_drivers(pyodbc)
    if not drivers:
        return
    
    # 查找SQL Server驱动
    recommended_driver = find_sql_server_driver(drivers)
    if not recommended_driver:
        return
    
    # 测试连接字符串构建
    conn_str = test_connection_string_building(recommended_driver)
    if not conn_str:
        return
    
    # 测试网络连接
    network_ok = test_network_connection()
    
    # 测试实际连接
    success, message = test_actual_connection(pyodbc, conn_str)
    
    # 如果使用ODBC Driver 18且失败，尝试特殊配置
    if not success and 'ODBC Driver 18' in recommended_driver:
        print(f"\n🔄 尝试ODBC Driver 18的特殊配置...")
        working_conn_str = test_odbc_driver_18_specific()
        if working_conn_str:
            print(f"\n✅ 找到可用的配置!")
    
    # 提供解决方案
    provide_solutions()
    
    print("\n" + "=" * 70)
    print("🎯 诊断完成")
    print("=" * 70)

if __name__ == "__main__":
    main()
