{"name": "bi-platform-frontend", "version": "1.0.0", "description": "自然语言BI数据分析平台前端", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "antd": "^5.11.5", "@ant-design/icons": "^5.2.6", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "three": "^0.158.0", "@types/three": "^0.158.3", "react-three-fiber": "^6.0.13", "d3": "^7.8.5", "@types/d3": "^7.4.3", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "classnames": "^2.3.2", "react-query": "^3.39.3", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "typescript": "^5.2.2", "vite": "^4.5.0", "sass": "^1.69.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}