<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试模态框问题</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .btn { background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; margin: 10px; }
        .btn:hover { background: #5a6fd8; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { background: white; border-radius: 12px; padding: 30px; max-width: 600px; width: 90%; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-close { background: none; border: none; font-size: 24px; cursor: pointer; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; margin-top: 20px; padding: 15px; background: #fee; border: 1px solid #fcc; border-radius: 5px; }
        .success { color: green; margin-top: 20px; padding: 15px; background: #efe; border: 1px solid #cfc; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 调试模态框问题</h1>
        <p>这个页面用于调试创建数据源模态框自动消失的问题。</p>
        
        <div id="errorLog" style="display: none;"></div>
        
        <button class="btn" onclick="openTestModal()">🧪 测试打开模态框</button>
        <button class="btn" onclick="checkJavaScriptErrors()">🔍 检查JavaScript错误</button>
        <button class="btn" onclick="testFormSubmission()">📝 测试表单提交</button>
        
        <div id="testResults" style="margin-top: 20px;"></div>
    </div>

    <!-- 测试模态框 -->
    <div id="testModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>测试模态框</h2>
                <button class="modal-close" onclick="closeTestModal()">&times;</button>
            </div>
            
            <form id="testForm" onsubmit="handleFormSubmit(event)">
                <div class="form-group">
                    <label>数据源名称:</label>
                    <input type="text" id="testName" required>
                </div>
                
                <div class="form-group">
                    <label>数据库类型:</label>
                    <select id="testType" onchange="handleTypeChange()">
                        <option value="">请选择</option>
                        <option value="sqlite">SQLite</option>
                        <option value="postgresql">PostgreSQL</option>
                    </select>
                </div>
                
                <div style="margin-top: 20px;">
                    <button type="button" class="btn" onclick="closeTestModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局错误捕获
        window.onerror = function(message, source, lineno, colno, error) {
            logError(`JavaScript错误: ${message} (行 ${lineno})`);
            return false;
        };

        window.addEventListener('unhandledrejection', function(event) {
            logError(`Promise错误: ${event.reason}`);
        });

        function logError(message) {
            console.error(message);
            const errorLog = document.getElementById('errorLog');
            errorLog.style.display = 'block';
            errorLog.className = 'error';
            errorLog.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function logSuccess(message) {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = 'success';
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
        }

        function openTestModal() {
            try {
                console.log('尝试打开模态框...');
                const modal = document.getElementById('testModal');
                if (!modal) {
                    logError('找不到模态框元素');
                    return;
                }
                
                modal.classList.add('show');
                logSuccess('模态框打开成功');
                
                // 检查模态框是否立即消失
                setTimeout(() => {
                    if (!modal.classList.contains('show')) {
                        logError('模态框在打开后立即消失了');
                    } else {
                        logSuccess('模态框保持打开状态');
                    }
                }, 100);
                
            } catch (error) {
                logError(`打开模态框时发生错误: ${error.message}`);
            }
        }

        function closeTestModal() {
            try {
                console.log('关闭模态框...');
                const modal = document.getElementById('testModal');
                modal.classList.remove('show');
                logSuccess('模态框关闭成功');
            } catch (error) {
                logError(`关闭模态框时发生错误: ${error.message}`);
            }
        }

        function handleTypeChange() {
            try {
                const type = document.getElementById('testType').value;
                console.log('数据库类型改变:', type);
                logSuccess(`数据库类型改变为: ${type}`);
            } catch (error) {
                logError(`处理类型改变时发生错误: ${error.message}`);
            }
        }

        function handleFormSubmit(event) {
            try {
                console.log('表单提交事件触发');
                event.preventDefault(); // 阻止默认提交行为
                
                const name = document.getElementById('testName').value;
                const type = document.getElementById('testType').value;
                
                if (!name || !type) {
                    logError('请填写完整信息');
                    return;
                }
                
                logSuccess(`表单提交成功: 名称=${name}, 类型=${type}`);
                
                // 模拟保存过程
                setTimeout(() => {
                    closeTestModal();
                    logSuccess('数据源创建完成');
                }, 1000);
                
            } catch (error) {
                logError(`处理表单提交时发生错误: ${error.message}`);
            }
        }

        function checkJavaScriptErrors() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="success">开始检查JavaScript错误...</div>';
            
            // 检查常见问题
            const checks = [
                {
                    name: '检查模态框元素',
                    test: () => document.getElementById('testModal') !== null
                },
                {
                    name: '检查CSS类',
                    test: () => {
                        const modal = document.getElementById('testModal');
                        return modal && getComputedStyle(modal).display !== undefined;
                    }
                },
                {
                    name: '检查事件监听器',
                    test: () => typeof openTestModal === 'function'
                },
                {
                    name: '检查表单元素',
                    test: () => document.getElementById('testForm') !== null
                }
            ];
            
            checks.forEach(check => {
                try {
                    if (check.test()) {
                        logSuccess(`✅ ${check.name}: 通过`);
                    } else {
                        logError(`❌ ${check.name}: 失败`);
                    }
                } catch (error) {
                    logError(`❌ ${check.name}: 异常 - ${error.message}`);
                }
            });
        }

        function testFormSubmission() {
            try {
                // 测试表单提交是否会导致页面刷新
                const form = document.getElementById('testForm');
                if (!form) {
                    logError('找不到测试表单');
                    return;
                }
                
                // 检查表单的action和method
                logSuccess(`表单action: ${form.action || '(空)'}`);
                logSuccess(`表单method: ${form.method || 'GET'}`);
                
                // 检查是否有其他事件监听器
                const events = ['submit', 'click', 'change'];
                events.forEach(eventType => {
                    const listeners = getEventListeners ? getEventListeners(form) : {};
                    if (listeners[eventType]) {
                        logSuccess(`发现${eventType}事件监听器: ${listeners[eventType].length}个`);
                    }
                });
                
            } catch (error) {
                logError(`测试表单提交时发生错误: ${error.message}`);
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('testModal');
            if (event.target === modal) {
                console.log('点击模态框外部，关闭模态框');
                closeTestModal();
            }
        });

        // 页面加载完成
        window.onload = function() {
            logSuccess('页面加载完成，开始调试');
            checkJavaScriptErrors();
        };
    </script>
</body>
</html>
