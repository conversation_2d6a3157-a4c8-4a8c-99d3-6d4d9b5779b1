# 无Docker本地开发指南

## 🎯 目标
完全不使用Docker，在本地搭建BI数据分析平台进行开发和测试。

## 📋 系统要求

### 必需软件
1. **Python 3.8+**
   - 下载：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **Node.js 16+**
   - 下载：https://nodejs.org/
   - 推荐LTS版本

### 验证安装
```cmd
python --version
node --version
npm --version
```

## 🚀 一键启动

### 最简单的方式
```cmd
# 双击运行或在命令行执行
start-local.bat
```

这个脚本会自动：
1. 检查Python和Node.js环境
2. 创建Python虚拟环境
3. 安装所有依赖
4. 配置SQLite数据库
5. 启动后端和前端服务

## 📝 手动启动步骤

如果自动脚本有问题，可以手动执行：

### 步骤1: 启动后端
```cmd
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 配置pip镜像源（可选，加速下载）
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 安装依赖
pip install -r requirements-sqlite.txt

# 启动后端服务
python app/main_sqlite.py
```

### 步骤2: 启动前端
```cmd
# 新开一个命令行窗口
cd frontend

# 配置npm镜像源（可选，加速下载）
npm config set registry https://registry.npmmirror.com

# 安装依赖
npm install

# 启动前端服务
npm run dev
```

## 🌐 访问地址

启动成功后：
- **前端**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/v1/docs
- **数据库**: SQLite文件 `backend/bi_platform.db`

## 🔑 默认登录信息

- **邮箱**: <EMAIL>
- **密码**: admin123

## 📊 功能特性

### 已实现的功能
1. **用户认证**: 模拟登录系统
2. **数据展示**: SQLite示例数据
3. **API文档**: 自动生成的接口文档
4. **前端界面**: 完整的React应用

### 示例数据
- **销售数据**: 包含产品、类别、金额、地区等信息
- **用户数据**: 包含用户基本信息和活动数据
- **自动初始化**: 首次启动自动创建示例数据

## 🔧 开发说明

### 数据库
- 使用SQLite，无需安装额外数据库
- 数据文件：`backend/bi_platform.db`
- 自动创建表和示例数据

### 后端特性
- FastAPI框架，自动生成API文档
- SQLAlchemy ORM，支持多种数据库
- 模拟认证系统，便于前端开发
- 热重载，修改代码自动重启

### 前端特性
- React 18 + TypeScript
- Ant Design UI组件库
- Vite构建工具，快速热重载
- Redux状态管理

## 🛠️ 开发工作流

### 修改后端代码
1. 编辑 `backend/app/` 目录下的文件
2. 保存后服务器自动重载
3. 访问 http://localhost:8000/api/v1/docs 查看API变化

### 修改前端代码
1. 编辑 `frontend/src/` 目录下的文件
2. 保存后页面自动刷新
3. 在浏览器开发者工具中查看变化

### 查看数据库
```cmd
# 安装SQLite命令行工具（可选）
# 或使用在线SQLite查看器
# 数据库文件：backend/bi_platform.db
```

## 🔍 故障排除

### 问题1: Python虚拟环境创建失败
```cmd
# 确保Python正确安装
python --version

# 手动创建虚拟环境
cd backend
python -m venv venv
```

### 问题2: pip安装依赖失败
```cmd
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements-sqlite.txt
```

### 问题3: npm安装依赖失败
```cmd
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rmdir /s node_modules
del package-lock.json
npm install
```

### 问题4: 端口被占用
- 后端端口8000被占用：修改 `main_sqlite.py` 中的端口
- 前端端口3000被占用：Vite会自动选择其他端口

### 问题5: 数据库文件权限问题
```cmd
# 删除数据库文件重新创建
del backend\bi_platform.db
# 重新启动后端服务
```

## 🎉 验证安装

成功启动的标志：

1. **后端启动成功**:
   ```
   INFO:     Uvicorn running on http://0.0.0.0:8000
   ✅ 数据库初始化完成
   ```

2. **前端启动成功**:
   ```
   Local:   http://localhost:3000/
   Network: http://192.168.x.x:3000/
   ```

3. **功能测试**:
   - 访问前端页面能看到登录界面
   - 使用默认账号能成功登录
   - API文档页面正常显示

## 📚 下一步

1. **探索功能**: 登录后查看各个模块
2. **查看数据**: 在数据源管理中查看示例数据
3. **测试API**: 在API文档页面测试接口
4. **修改代码**: 尝试修改前端或后端代码
5. **添加功能**: 根据需要扩展新功能

## 🆘 获取帮助

如果遇到问题：
1. 查看命令行输出的错误信息
2. 检查Python和Node.js版本
3. 确保网络连接正常（下载依赖时）
4. 查看浏览器开发者工具的错误信息

祝您开发愉快！🎉
