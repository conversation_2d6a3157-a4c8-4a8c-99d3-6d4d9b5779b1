#!/usr/bin/env python3
"""
核心数据库驱动安装脚本

快速安装最重要的数据库驱动
"""

import subprocess
import sys
import os

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🗄️ 核心数据库驱动安装器")
    print("=" * 60)
    print("快速安装最重要的数据库驱动")
    print()

def install_package(package, description):
    """安装单个包"""
    print(f"📦 安装 {description}...")
    print(f"   命令: pip install {package}")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print(f"✅ {description} 安装成功")
            return True
        else:
            print(f"❌ {description} 安装失败")
            print(f"   错误: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {description} 安装异常: {str(e)}")
        return False

def test_import(module_name, display_name):
    """测试导入"""
    try:
        __import__(module_name)
        print(f"✅ {display_name} 可用")
        return True
    except ImportError:
        print(f"❌ {display_name} 不可用")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 核心数据库驱动
    core_drivers = [
        ("psycopg2-binary", "PostgreSQL驱动", "psycopg2"),
        ("pymysql", "MySQL驱动", "pymysql"),
        ("pymongo", "MongoDB驱动", "pymongo"),
        ("redis", "Redis驱动", "redis"),
    ]
    
    # 可选驱动
    optional_drivers = [
        ("pyodbc", "SQL Server驱动", "pyodbc"),
        ("elasticsearch", "Elasticsearch驱动", "elasticsearch"),
        ("duckdb", "DuckDB驱动", "duckdb"),
    ]
    
    print("🔧 安装核心数据库驱动:")
    print("-" * 40)
    
    core_success = 0
    for package, description, module in core_drivers:
        if install_package(package, description):
            core_success += 1
        print()
    
    print("🔧 安装可选数据库驱动:")
    print("-" * 40)
    
    optional_success = 0
    for package, description, module in optional_drivers:
        if install_package(package, description):
            optional_success += 1
        print()
    
    print("🧪 测试驱动可用性:")
    print("-" * 40)
    
    # 测试核心驱动
    print("核心驱动:")
    for package, description, module in core_drivers:
        test_import(module, description)
    
    print("\n可选驱动:")
    for package, description, module in optional_drivers:
        test_import(module, description)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 安装总结")
    print("=" * 60)
    print(f"核心驱动: {core_success}/{len(core_drivers)} 成功")
    print(f"可选驱动: {optional_success}/{len(optional_drivers)} 成功")
    
    if core_success >= 3:  # 至少3个核心驱动成功
        print("\n🎉 核心驱动安装完成！")
        print("✅ 可以启动真实连接测试服务器")
        print("\n🚀 下一步:")
        print("   python real_connection_server.py")
    else:
        print("\n⚠️ 部分核心驱动安装失败")
        print("💡 可以先使用SQLite进行测试")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
