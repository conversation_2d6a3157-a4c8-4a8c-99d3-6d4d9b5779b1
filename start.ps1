# BI数据分析平台启动脚本 (PowerShell版本)

Write-Host ""
Write-Host "BI数据分析平台启动脚本" -ForegroundColor Blue
Write-Host "=================================" -ForegroundColor Blue

# 检查Docker是否安装
Write-Host "检查Docker是否安装..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "Docker已安装: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: Docker未安装，请先安装Docker Desktop" -ForegroundColor Red
    Write-Host "下载地址: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查Docker Compose是否安装
Write-Host "检查Docker Compose是否安装..." -ForegroundColor Yellow
try {
    $composeVersion = docker-compose --version
    Write-Host "Docker Compose已安装: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: Docker Compose未安装" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查环境配置文件
Write-Host "检查环境配置文件..." -ForegroundColor Yellow
if (-not (Test-Path "backend\.env")) {
    if (Test-Path "backend\.env.example") {
        Copy-Item "backend\.env.example" "backend\.env"
        Write-Host "已创建 backend\.env 文件" -ForegroundColor Green
    } else {
        Write-Host "警告: backend\.env.example 文件不存在" -ForegroundColor Yellow
    }
}

# 停止现有容器
Write-Host "停止现有容器..." -ForegroundColor Yellow
docker-compose down

# 构建并启动服务
Write-Host "构建并启动服务..." -ForegroundColor Yellow
docker-compose up -d --build

# 等待服务启动
Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# 检查服务状态
Write-Host "检查服务状态..." -ForegroundColor Yellow
docker-compose ps

Write-Host ""
Write-Host "服务启动完成！" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Blue
Write-Host "前端地址: http://localhost" -ForegroundColor Cyan
Write-Host "后端API: http://localhost:8000" -ForegroundColor Cyan
Write-Host "API文档: http://localhost:8000/api/v1/docs" -ForegroundColor Cyan
Write-Host "数据库: localhost:5432" -ForegroundColor Cyan
Write-Host "Redis: localhost:6379" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Blue

Write-Host ""
Write-Host "默认登录信息:" -ForegroundColor Yellow
Write-Host "   邮箱: <EMAIL>" -ForegroundColor White
Write-Host "   密码: admin123" -ForegroundColor White
Write-Host "=================================" -ForegroundColor Blue

Write-Host ""
Write-Host "有用的命令:" -ForegroundColor Yellow
Write-Host "   查看日志: docker-compose logs -f" -ForegroundColor White
Write-Host "   停止服务: docker-compose down" -ForegroundColor White
Write-Host "   重启服务: docker-compose restart" -ForegroundColor White

Write-Host ""
Write-Host "启动完成，祝您使用愉快！" -ForegroundColor Green
Read-Host "按任意键退出"
