"""
认证相关的Pydantic模式

定义认证数据的输入输出模式。
"""

from typing import Optional

from pydantic import BaseModel, EmailStr

from app.db.models.user import UserRole


class Token(BaseModel):
    """令牌模式"""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int


class TokenPayload(BaseModel):
    """令牌载荷模式"""
    sub: Optional[int] = None


class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str


class UserRegister(BaseModel):
    """用户注册模式"""
    email: EmailStr
    username: str
    password: str
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER


class PasswordReset(BaseModel):
    """密码重置模式"""
    token: str
    new_password: str


class PasswordChange(BaseModel):
    """密码修改模式"""
    current_password: str
    new_password: str
