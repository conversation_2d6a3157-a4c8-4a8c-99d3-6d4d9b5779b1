"""
数据源模型

定义数据源的数据库模型。
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, Enum
from sqlalchemy.sql import func
import enum

from app.db.base import Base


class DataSourceType(str, enum.Enum):
    """数据源类型枚举"""
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    MONGODB = "mongodb"
    REDIS = "redis"
    SQLITE = "sqlite"
    ORACLE = "oracle"
    SQLSERVER = "sqlserver"
    CSV = "csv"
    EXCEL = "excel"
    API = "api"


class DataSourceStatus(str, enum.Enum):
    """数据源状态枚举"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"
    TESTING = "testing"


class DataSource(Base):
    """数据源模型"""
    
    __tablename__ = "data_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True, comment="数据源名称")
    description = Column(Text, comment="数据源描述")
    
    # 数据源类型和状态
    type = Column(Enum(DataSourceType), nullable=False, comment="数据源类型")
    status = Column(
        Enum(DataSourceStatus), 
        default=DataSourceStatus.DISCONNECTED, 
        comment="连接状态"
    )
    
    # 连接配置
    host = Column(String(255), comment="主机地址")
    port = Column(Integer, comment="端口号")
    database = Column(String(100), comment="数据库名")
    username = Column(String(100), comment="用户名")
    password = Column(String(255), comment="密码（加密存储）")
    
    # 扩展配置（JSON格式存储其他配置项）
    config = Column(JSON, comment="扩展配置")
    
    # 连接信息
    connection_string = Column(Text, comment="连接字符串")
    last_test_time = Column(DateTime, comment="最后测试时间")
    last_test_result = Column(Text, comment="最后测试结果")
    
    # 元数据
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_by = Column(Integer, comment="创建者ID")
    
    # 时间戳
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        DateTime, 
        server_default=func.now(), 
        onupdate=func.now(), 
        comment="更新时间"
    )
    
    def __repr__(self):
        return f"<DataSource(id={self.id}, name='{self.name}', type='{self.type}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "type": self.type.value if self.type else None,
            "status": self.status.value if self.status else None,
            "host": self.host,
            "port": self.port,
            "database": self.database,
            "username": self.username,
            "config": self.config,
            "is_active": self.is_active,
            "last_test_time": self.last_test_time.isoformat() if self.last_test_time else None,
            "last_test_result": self.last_test_result,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def get_safe_dict(self):
        """获取安全的字典格式（不包含敏感信息）"""
        data = self.to_dict()
        # 移除敏感信息
        data.pop("password", None)
        data.pop("connection_string", None)
        return data
