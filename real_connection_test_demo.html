<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实连接测试演示</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px; color: #333;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { 
            background: white; border-radius: 16px; padding: 30px; margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center;
        }
        .card { 
            background: white; border-radius: 16px; padding: 30px; margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .btn { 
            background: #667eea; color: white; border: none; padding: 12px 24px;
            border-radius: 10px; cursor: pointer; margin: 8px; font-size: 14px;
            font-weight: 600; transition: all 0.3s ease;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
        .btn:disabled { background: #cbd5e1; cursor: not-allowed; transform: none; }
        .btn-success { background: #10b981; }
        .btn-danger { background: #ef4444; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-group input, .form-group select { 
            width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px;
            font-size: 14px; transition: border-color 0.3s ease;
        }
        .form-group input:focus, .form-group select:focus { 
            border-color: #667eea; outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .result { 
            background: #f8fafc; border: 2px solid #e5e7eb; border-radius: 12px; 
            padding: 20px; margin: 20px 0; font-family: monospace; font-size: 13px;
            max-height: 400px; overflow-y: auto;
        }
        .result.success { border-color: #10b981; background: #f0fdf4; }
        .result.error { border-color: #ef4444; background: #fef2f2; }
        .status { padding: 15px; border-radius: 8px; margin: 15px 0; }
        .status.info { background: #dbeafe; border: 1px solid #93c5fd; color: #1e40af; }
        .status.warning { background: #fef3c7; border: 1px solid #fcd34d; color: #92400e; }
        .status.success { background: #dcfce7; border: 1px solid #86efac; color: #166534; }
        .status.error { background: #fee2e2; border: 1px solid #fca5a5; color: #991b1b; }
        .demo-section { margin: 30px 0; }
        .demo-section h3 { margin-bottom: 15px; color: #374151; }
        code { background: #f1f5f9; padding: 2px 6px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 真实连接测试演示</h1>
            <p>对比模拟测试与真实测试的区别</p>
            <div style="margin-top: 15px;">
                <span style="background: #10b981; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; margin: 4px;">✅ 真实验证</span>
                <span style="background: #ef4444; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; margin: 4px;">❌ 错误检测</span>
                <span style="background: #3b82f6; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; margin: 4px;">🔧 详细诊断</span>
            </div>
        </div>

        <div class="grid">
            <!-- SQLite测试 -->
            <div class="card">
                <h3>📁 SQLite 连接测试</h3>
                <div class="form-group">
                    <label>数据库路径:</label>
                    <input type="text" id="sqliteDb" value="C:\Users\<USER>\test\test\test_database.db">
                </div>
                <button class="btn" onclick="testSQLite()">🔍 测试SQLite连接</button>
                <div id="sqliteResult" class="result" style="display: none;"></div>
            </div>

            <!-- 错误演示 -->
            <div class="card">
                <h3>❌ 错误连接演示</h3>
                <div class="form-group">
                    <label>PostgreSQL (错误配置):</label>
                    <input type="text" id="errorHost" value="nonexistent.server.com" placeholder="不存在的主机">
                </div>
                <div class="form-group">
                    <input type="number" id="errorPort" value="9999" placeholder="错误端口">
                </div>
                <button class="btn btn-danger" onclick="testError()">🔍 测试错误连接</button>
                <div id="errorResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <!-- 服务器状态检查 -->
        <div class="card">
            <h3>🔧 服务器状态检查</h3>
            <div id="serverStatus" class="status info">
                <strong>检查中...</strong> 正在检测真实连接测试服务器状态
            </div>
            <button class="btn" onclick="checkServerStatus()">🔄 重新检查服务器</button>
        </div>

        <!-- 对比说明 -->
        <div class="card">
            <h3>📊 模拟测试 vs 真实测试对比</h3>
            <div class="grid">
                <div>
                    <h4 style="color: #ef4444;">❌ 模拟测试问题</h4>
                    <ul style="margin: 10px 0 0 20px; line-height: 1.8;">
                        <li>随机返回成功/失败</li>
                        <li>无法验证真实连接</li>
                        <li>错误信息不准确</li>
                        <li>无法检测网络问题</li>
                        <li>不能验证认证信息</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #10b981;">✅ 真实测试优势</h4>
                    <ul style="margin: 10px 0 0 20px; line-height: 1.8;">
                        <li>真实数据库连接验证</li>
                        <li>准确的错误诊断</li>
                        <li>网络连接检测</li>
                        <li>认证信息验证</li>
                        <li>详细的连接信息</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card">
            <h3>🚀 如何启用真实连接测试</h3>
            <div class="demo-section">
                <h4>第一步：启动真实连接测试服务器</h4>
                <code>python real_connection_server.py</code>
                <p style="margin: 10px 0; color: #6b7280;">服务器将在 http://localhost:8080 启动</p>
            </div>
            
            <div class="demo-section">
                <h4>第二步：修改前端API地址</h4>
                <p>将连接测试API地址修改为：</p>
                <code>http://localhost:8080/api/test-connection</code>
            </div>
            
            <div class="demo-section">
                <h4>第三步：安装数据库驱动 (可选)</h4>
                <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <code>pip install psycopg2-binary</code> # PostgreSQL<br>
                    <code>pip install pymysql</code> # MySQL<br>
                    <code>pip install pyodbc</code> # SQL Server<br>
                    <code>pip install pymongo</code> # MongoDB<br>
                    <code>pip install redis</code> # Redis
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检查服务器状态
        async function checkServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '<strong>检查中...</strong> 正在检测真实连接测试服务器状态';
            
            try {
                const response = await fetch('http://localhost:8080/', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '<strong>✅ 服务器在线</strong> 真实连接测试服务器运行正常，可以进行真实连接验证';
                } else {
                    throw new Error('服务器响应异常');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <strong>❌ 服务器离线</strong><br>
                    无法连接到真实连接测试服务器。请启动服务器：<br>
                    <code>python real_connection_server.py</code>
                `;
            }
        }

        // 测试SQLite连接
        async function testSQLite() {
            const database = document.getElementById('sqliteDb').value;
            const resultDiv = document.getElementById('sqliteResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '🔄 正在测试SQLite连接...';
            
            try {
                const response = await fetch('http://localhost:8080/api/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'sqlite',
                        database: database
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ ${result.message}</strong><br>
                        测试时间: ${new Date(result.test_time).toLocaleString()}<br>
                        模式: ${result.mode}<br><br>
                        <strong>连接详情:</strong><br>
                        ${JSON.stringify(result.details, null, 2)}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ ${result.message}</strong><br>
                        错误类型: ${result.error_type}<br>
                        测试时间: ${new Date(result.test_time).toLocaleString()}<br>
                        模式: ${result.mode}<br><br>
                        ${result.suggestion ? `<strong>建议:</strong> ${result.suggestion}` : ''}
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ 连接测试异常</strong><br>
                    错误: ${error.message}<br><br>
                    <strong>解决方案:</strong><br>
                    1. 启动真实连接测试服务器<br>
                    2. 检查服务器地址是否正确
                `;
            }
        }

        // 测试错误连接
        async function testError() {
            const host = document.getElementById('errorHost').value;
            const port = document.getElementById('errorPort').value;
            const resultDiv = document.getElementById('errorResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '🔄 正在测试错误连接（演示错误检测）...';
            
            try {
                const response = await fetch('http://localhost:8080/api/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'postgresql',
                        host: host,
                        port: parseInt(port),
                        database: 'test',
                        username: 'test',
                        password: 'test'
                    })
                });
                
                const result = await response.json();
                
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ ${result.message}</strong><br>
                    错误类型: ${result.error_type}<br>
                    测试时间: ${new Date(result.test_time).toLocaleString()}<br>
                    模式: ${result.mode}<br><br>
                    ${result.suggestion ? `<strong>建议:</strong> ${result.suggestion}` : ''}
                    <br><br>
                    <strong>💡 这演示了真实测试如何准确检测连接错误！</strong>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ 连接测试异常</strong><br>
                    错误: ${error.message}<br><br>
                    请确保真实连接测试服务器正在运行
                `;
            }
        }

        // 页面加载时检查服务器状态
        window.onload = function() {
            checkServerStatus();
        };
    </script>
</body>
</html>
