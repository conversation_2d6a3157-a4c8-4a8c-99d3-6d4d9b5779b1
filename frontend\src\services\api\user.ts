/**
 * 用户API服务
 */

import client from './client'
import { User, UserUpdateRequest } from '@/types/user'

export const userAPI = {
  // 获取用户列表
  getUsers: async (params: { skip?: number; limit?: number } = {}): Promise<User[]> => {
    return client.get('/users', { params })
  },

  // 获取单个用户
  getUser: async (userId: number): Promise<User> => {
    return client.get(`/users/${userId}`)
  },

  // 更新用户信息
  updateUser: async (userId: number, userData: UserUpdateRequest): Promise<User> => {
    return client.put(`/users/${userId}`, userData)
  },

  // 删除用户
  deleteUser: async (userId: number): Promise<void> => {
    return client.delete(`/users/${userId}`)
  },

  // 上传头像
  uploadAvatar: async (userId: number, file: File): Promise<{ avatar_url: string }> => {
    const formData = new FormData()
    formData.append('file', file)
    
    return client.post(`/users/${userId}/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}
