@echo off
echo Starting Backend Development Server
echo ====================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.10+
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM 激活虚拟环境
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM 升级pip并配置国内镜像源
echo Configuring pip...
python -m pip install --upgrade pip
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

REM 安装核心依赖
echo Installing core dependencies...
pip install fastapi==0.104.1 uvicorn[standard]==0.24.0

REM 安装数据库依赖
echo Installing database dependencies...
pip install sqlalchemy==2.0.23 psycopg2-binary==2.9.9 redis==5.0.1

REM 安装认证依赖
echo Installing auth dependencies...
pip install python-jose[cryptography]==3.3.0 passlib[bcrypt]==1.7.4 python-multipart==0.0.6

REM 安装配置依赖
echo Installing config dependencies...
pip install pydantic[email]==2.5.0 python-dotenv==1.0.0

REM 创建环境配置文件
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo Created .env file
    )
)

echo.
echo Starting FastAPI development server...
echo Backend will be available at: http://localhost:8000
echo API docs will be available at: http://localhost:8000/api/v1/docs
echo.

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
