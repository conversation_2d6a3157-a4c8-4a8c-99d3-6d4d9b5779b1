"""
数据库连接器

处理各种数据库的连接和查询。
"""

import logging
from typing import Dict, Any, List
import time
import json

from app.db.models.data_source import DataSourceType

logger = logging.getLogger(__name__)


class DatabaseConnector:
    """数据库连接器类"""
    
    def __init__(self):
        self.connection_cache = {}
    
    def test_connection(
        self,
        db_type: DataSourceType,
        host: str,
        port: int,
        database: str,
        username: str,
        password: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        测试数据库连接。
        
        Args:
            db_type: 数据库类型
            host: 主机地址
            port: 端口
            database: 数据库名
            username: 用户名
            password: 密码
            config: 扩展配置
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            if db_type == DataSourceType.POSTGRESQL:
                return self._test_postgresql(host, port, database, username, password, config)
            elif db_type == DataSourceType.MYSQL:
                return self._test_mysql(host, port, database, username, password, config)
            elif db_type == DataSourceType.MONGODB:
                return self._test_mongodb(host, port, database, username, password, config)
            elif db_type == DataSourceType.REDIS:
                return self._test_redis(host, port, password, config)
            elif db_type == DataSourceType.SQLITE:
                return self._test_sqlite(database, config)
            elif db_type == DataSourceType.MSSQL or db_type == DataSourceType.SQLSERVER:
                return self._test_mssql(host, port, database, username, password, config)
            else:
                return {
                    "success": False,
                    "message": f"暂不支持的数据库类型: {db_type.value}",
                    "details": None
                }
                
        except Exception as e:
            logger.error(f"数据库连接测试失败: {str(e)}")
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def _test_postgresql(
        self, 
        host: str, 
        port: int, 
        database: str, 
        username: str, 
        password: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """测试PostgreSQL连接"""
        try:
            import psycopg2
            
            conn = psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password,
                connect_timeout=10
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            return {
                "success": True,
                "message": "PostgreSQL连接成功",
                "details": {
                    "version": version,
                    "host": host,
                    "port": port,
                    "database": database
                }
            }
            
        except ImportError:
            return {
                "success": False,
                "message": "缺少psycopg2依赖包",
                "details": {"error": "请安装psycopg2-binary"}
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"PostgreSQL连接失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def _test_mysql(
        self, 
        host: str, 
        port: int, 
        database: str, 
        username: str, 
        password: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """测试MySQL连接"""
        try:
            import pymysql
            
            conn = pymysql.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password,
                connect_timeout=10,
                charset='utf8mb4'
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION();")
            version = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            return {
                "success": True,
                "message": "MySQL连接成功",
                "details": {
                    "version": version,
                    "host": host,
                    "port": port,
                    "database": database
                }
            }
            
        except ImportError:
            return {
                "success": False,
                "message": "缺少pymysql依赖包",
                "details": {"error": "请安装pymysql"}
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"MySQL连接失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def _test_mongodb(
        self, 
        host: str, 
        port: int, 
        database: str, 
        username: str, 
        password: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """测试MongoDB连接"""
        try:
            from pymongo import MongoClient
            
            # 构建连接URI
            if username and password:
                uri = f"mongodb://{username}:{password}@{host}:{port}/{database}"
            else:
                uri = f"mongodb://{host}:{port}/{database}"
            
            client = MongoClient(uri, serverSelectionTimeoutMS=10000)
            
            # 测试连接
            client.admin.command('ping')
            
            # 获取服务器信息
            server_info = client.server_info()
            
            client.close()
            
            return {
                "success": True,
                "message": "MongoDB连接成功",
                "details": {
                    "version": server_info.get("version"),
                    "host": host,
                    "port": port,
                    "database": database
                }
            }
            
        except ImportError:
            return {
                "success": False,
                "message": "缺少pymongo依赖包",
                "details": {"error": "请安装pymongo"}
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"MongoDB连接失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def _test_redis(
        self, 
        host: str, 
        port: int, 
        password: str = None,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """测试Redis连接"""
        try:
            import redis
            
            r = redis.Redis(
                host=host,
                port=port,
                password=password,
                socket_connect_timeout=10,
                decode_responses=True
            )
            
            # 测试连接
            r.ping()
            
            # 获取服务器信息
            info = r.info()
            
            return {
                "success": True,
                "message": "Redis连接成功",
                "details": {
                    "version": info.get("redis_version"),
                    "host": host,
                    "port": port,
                    "memory": info.get("used_memory_human")
                }
            }
            
        except ImportError:
            return {
                "success": False,
                "message": "缺少redis依赖包",
                "details": {"error": "请安装redis"}
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Redis连接失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def _test_sqlite(
        self,
        database: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """测试SQLite连接"""
        try:
            import sqlite3

            conn = sqlite3.connect(database, timeout=10)
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version();")
            version = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            return {
                "success": True,
                "message": "SQLite连接成功",
                "details": {
                    "version": version,
                    "database": database
                }
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"SQLite连接失败: {str(e)}",
                "details": {"error": str(e)}
            }

    def _test_mssql(
        self,
        host: str,
        port: int,
        database: str,
        username: str,
        password: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """测试SQL Server连接"""
        try:
            import pyodbc

            # 构建连接字符串
            conn_str = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={host},{port};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                f"TrustServerCertificate=yes;"
            )

            conn = pyodbc.connect(conn_str, timeout=10)
            cursor = conn.cursor()
            cursor.execute("SELECT @@VERSION;")
            version = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            return {
                "success": True,
                "message": "SQL Server连接成功",
                "details": {
                    "version": version[:100] + "..." if len(version) > 100 else version,
                    "host": host,
                    "port": port,
                    "database": database
                }
            }

        except ImportError:
            return {
                "success": False,
                "message": "缺少pyodbc依赖包",
                "details": {"error": "请安装pyodbc"}
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"SQL Server连接失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def execute_query(
        self,
        db_type: DataSourceType,
        host: str,
        port: int,
        database: str,
        username: str,
        password: str,
        query: str,
        limit: int = 100,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        执行数据库查询。
        
        Args:
            db_type: 数据库类型
            host: 主机地址
            port: 端口
            database: 数据库名
            username: 用户名
            password: 密码
            query: 查询语句
            limit: 结果限制
            config: 扩展配置
            
        Returns:
            Dict[str, Any]: 查询结果
        """
        try:
            if db_type == DataSourceType.POSTGRESQL:
                return self._execute_postgresql_query(
                    host, port, database, username, password, query, limit, config
                )
            elif db_type == DataSourceType.MYSQL:
                return self._execute_mysql_query(
                    host, port, database, username, password, query, limit, config
                )
            elif db_type == DataSourceType.SQLITE:
                return self._execute_sqlite_query(database, query, limit, config)
            else:
                # 对于其他类型，返回模拟数据
                return self._execute_mock_query(query, limit)
                
        except Exception as e:
            logger.error(f"查询执行失败: {str(e)}")
            raise e
    
    def _execute_postgresql_query(
        self, 
        host: str, 
        port: int, 
        database: str, 
        username: str, 
        password: str,
        query: str,
        limit: int,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """执行PostgreSQL查询"""
        import psycopg2
        import psycopg2.extras
        
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # 添加LIMIT子句
        if "LIMIT" not in query.upper():
            query = f"{query} LIMIT {limit}"
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description] if cursor.description else []
        
        # 转换为字典列表
        data = [dict(row) for row in rows]
        
        cursor.close()
        conn.close()
        
        return {
            "data": data,
            "columns": columns
        }
    
    def _execute_mysql_query(
        self, 
        host: str, 
        port: int, 
        database: str, 
        username: str, 
        password: str,
        query: str,
        limit: int,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """执行MySQL查询"""
        import pymysql
        
        conn = pymysql.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        
        # 添加LIMIT子句
        if "LIMIT" not in query.upper():
            query = f"{query} LIMIT {limit}"
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description] if cursor.description else []
        
        cursor.close()
        conn.close()
        
        return {
            "data": rows,
            "columns": columns
        }
    
    def _execute_sqlite_query(
        self, 
        database: str,
        query: str,
        limit: int,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """执行SQLite查询"""
        import sqlite3
        
        conn = sqlite3.connect(database)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 添加LIMIT子句
        if "LIMIT" not in query.upper():
            query = f"{query} LIMIT {limit}"
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # 获取列名
        columns = [description[0] for description in cursor.description] if cursor.description else []
        
        # 转换为字典列表
        data = [dict(row) for row in rows]
        
        cursor.close()
        conn.close()
        
        return {
            "data": data,
            "columns": columns
        }
    
    def _execute_mock_query(self, query: str, limit: int) -> Dict[str, Any]:
        """执行模拟查询（用于暂不支持的数据库类型）"""
        # 返回模拟数据
        mock_data = [
            {"id": 1, "name": "示例数据1", "value": 100, "date": "2024-01-01"},
            {"id": 2, "name": "示例数据2", "value": 200, "date": "2024-01-02"},
            {"id": 3, "name": "示例数据3", "value": 300, "date": "2024-01-03"},
        ]
        
        columns = ["id", "name", "value", "date"]
        
        return {
            "data": mock_data[:limit],
            "columns": columns
        }
