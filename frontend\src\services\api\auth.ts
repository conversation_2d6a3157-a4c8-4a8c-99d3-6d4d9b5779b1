/**
 * 认证API服务
 */

import client from './client'
import { LoginRequest, RegisterRequest, AuthResponse, User } from '@/types/auth'

export const authAPI = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)
    
    return client.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 用户注册
  register: async (userData: RegisterRequest): Promise<User> => {
    return client.post('/auth/register', userData)
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    return client.get('/auth/me')
  },

  // 刷新令牌
  refreshToken: async (): Promise<AuthResponse> => {
    return client.post('/auth/refresh')
  },

  // 修改密码
  changePassword: async (data: { current_password: string; new_password: string }): Promise<void> => {
    return client.post('/auth/change-password', data)
  },

  // 重置密码
  resetPassword: async (email: string): Promise<void> => {
    return client.post('/auth/reset-password', { email })
  },

  // 确认重置密码
  confirmResetPassword: async (data: { token: string; new_password: string }): Promise<void> => {
    return client.post('/auth/confirm-reset-password', data)
  },
}
