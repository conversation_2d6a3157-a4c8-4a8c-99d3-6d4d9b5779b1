@echo off
setlocal enabledelayedexpansion

echo.
echo BI数据分析平台启动脚本
echo =================================

echo 检查Docker是否安装...
docker --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker未安装，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo 检查Docker Compose是否安装...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

echo 检查环境配置文件...
if not exist "backend\.env" (
    echo 创建后端环境配置文件...
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env" >nul
        echo 已创建 backend\.env 文件
    ) else (
        echo 警告: backend\.env.example 文件不存在
    )
)

echo 停止现有容器...
docker-compose down

echo 构建并启动服务...
docker-compose up -d --build

echo 等待服务启动...
timeout /t 15 /nobreak >nul

echo 检查服务状态...
docker-compose ps

echo.
echo 服务启动完成！
echo =================================
echo 前端地址: http://localhost
echo 后端API: http://localhost:8000
echo API文档: http://localhost:8000/api/v1/docs
echo 数据库: localhost:5432
echo Redis: localhost:6379
echo =================================

echo.
echo 默认登录信息:
echo    邮箱: <EMAIL>
echo    密码: admin123
echo =================================

echo.
echo 有用的命令:
echo    查看日志: docker-compose logs -f
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart

echo.
echo 启动完成，祝您使用愉快！
pause
