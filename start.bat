@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo 🚀 自然语言BI数据分析平台启动脚本
echo =================================

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装，请先安装Docker
    pause
    exit /b 1
)

REM 检查Docker Compose是否安装
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

REM 检查.env文件是否存在
if not exist "backend\.env" (
    echo 📝 创建后端环境配置文件...
    copy "backend\.env.example" "backend\.env" >nul
    echo ✅ 已创建 backend\.env 文件，请根据需要修改配置
)

REM 停止现有容器
echo 🛑 停止现有容器...
docker-compose down

REM 构建并启动服务
echo 🔨 构建并启动服务...
docker-compose up -d --build

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose ps

echo.
echo ✅ 服务启动完成！
echo =================================
echo 📱 前端地址: http://localhost
echo 🔧 后端API: http://localhost:8000
echo 📚 API文档: http://localhost:8000/api/v1/docs
echo 🗄️ 数据库: localhost:5432
echo 🔴 Redis: localhost:6379
echo =================================

echo.
echo 🔑 默认登录信息:
echo    邮箱: <EMAIL>
echo    密码: admin123
echo =================================

echo.
echo 💡 有用的命令:
echo    查看日志: docker-compose logs -f
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart
echo    进入后端: docker-compose exec backend bash
echo    进入数据库: docker-compose exec postgres psql -U postgres -d bi_platform

echo.
echo 🎉 启动完成，祝您使用愉快！
pause
