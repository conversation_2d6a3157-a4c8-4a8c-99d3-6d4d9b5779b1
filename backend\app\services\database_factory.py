"""
数据库连接工厂

为不同类型的数据库提供统一的连接接口。
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from abc import ABC, abstractmethod
import json

from app.db.models.data_source import DataSourceType

logger = logging.getLogger(__name__)


class DatabaseAdapter(ABC):
    """数据库适配器抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connection = None
    
    @abstractmethod
    def connect(self) -> bool:
        """建立连接"""
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass
    
    @abstractmethod
    def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        pass
    
    @abstractmethod
    def execute_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        """执行查询"""
        pass
    
    @abstractmethod
    def get_tables(self) -> List[str]:
        """获取表列表"""
        pass
    
    @abstractmethod
    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构"""
        pass
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "type": self.__class__.__name__.replace("Adapter", "").lower(),
            "host": self.config.get("host"),
            "port": self.config.get("port"),
            "database": self.config.get("database")
        }


class PostgreSQLAdapter(DatabaseAdapter):
    """PostgreSQL适配器"""
    
    def connect(self) -> bool:
        try:
            import psycopg2
            
            self.connection = psycopg2.connect(
                host=self.config["host"],
                port=self.config["port"],
                database=self.config["database"],
                user=self.config["username"],
                password=self.config["password"],
                connect_timeout=10
            )
            return True
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connect():
                return {"success": False, "message": "连接失败"}
            
            cursor = self.connection.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            cursor.close()
            
            return {
                "success": True,
                "message": "PostgreSQL连接成功",
                "details": {
                    "version": version,
                    "host": self.config["host"],
                    "port": self.config["port"],
                    "database": self.config["database"]
                }
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"PostgreSQL连接测试失败: {str(e)}"
            }
        finally:
            self.disconnect()
    
    def execute_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        try:
            if not self.connect():
                raise Exception("无法建立连接")
            
            cursor = self.connection.cursor()
            
            # 添加LIMIT子句
            if "LIMIT" not in query.upper():
                query = f"{query} LIMIT {limit}"
            
            cursor.execute(query)
            
            # 获取列名
            columns = [desc[0] for desc in cursor.description] if cursor.description else []
            
            # 获取数据
            rows = cursor.fetchall()
            data = [dict(zip(columns, row)) for row in rows]
            
            cursor.close()
            
            return {
                "success": True,
                "data": data,
                "columns": columns,
                "total": len(data)
            }
        except Exception as e:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "error": str(e)
            }
        finally:
            self.disconnect()
    
    def get_tables(self) -> List[str]:
        try:
            if not self.connect():
                return []
            
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            return tables
        except Exception as e:
            logger.error(f"获取PostgreSQL表列表失败: {str(e)}")
            return []
        finally:
            self.disconnect()
    
    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        try:
            if not self.connect():
                return []
            
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT 
                    column_name,
                    data_type,
                    is_nullable,
                    column_default
                FROM information_schema.columns 
                WHERE table_name = %s 
                ORDER BY ordinal_position
            """, (table_name,))
            
            schema = []
            for row in cursor.fetchall():
                schema.append({
                    "column_name": row[0],
                    "data_type": row[1],
                    "is_nullable": row[2] == "YES",
                    "default_value": row[3]
                })
            
            cursor.close()
            return schema
        except Exception as e:
            logger.error(f"获取PostgreSQL表结构失败: {str(e)}")
            return []
        finally:
            self.disconnect()


class MySQLAdapter(DatabaseAdapter):
    """MySQL适配器"""
    
    def connect(self) -> bool:
        try:
            import pymysql
            
            self.connection = pymysql.connect(
                host=self.config["host"],
                port=self.config["port"],
                database=self.config["database"],
                user=self.config["username"],
                password=self.config["password"],
                connect_timeout=10,
                charset='utf8mb4'
            )
            return True
        except Exception as e:
            logger.error(f"MySQL连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connect():
                return {"success": False, "message": "连接失败"}
            
            cursor = self.connection.cursor()
            cursor.execute("SELECT VERSION();")
            version = cursor.fetchone()[0]
            cursor.close()
            
            return {
                "success": True,
                "message": "MySQL连接成功",
                "details": {
                    "version": version,
                    "host": self.config["host"],
                    "port": self.config["port"],
                    "database": self.config["database"]
                }
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"MySQL连接测试失败: {str(e)}"
            }
        finally:
            self.disconnect()
    
    def execute_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        try:
            if not self.connect():
                raise Exception("无法建立连接")
            
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 添加LIMIT子句
            if "LIMIT" not in query.upper():
                query = f"{query} LIMIT {limit}"
            
            cursor.execute(query)
            
            # 获取数据
            data = cursor.fetchall()
            columns = list(data[0].keys()) if data else []
            
            cursor.close()
            
            return {
                "success": True,
                "data": data,
                "columns": columns,
                "total": len(data)
            }
        except Exception as e:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "error": str(e)
            }
        finally:
            self.disconnect()
    
    def get_tables(self) -> List[str]:
        try:
            if not self.connect():
                return []
            
            cursor = self.connection.cursor()
            cursor.execute("SHOW TABLES")
            
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            return tables
        except Exception as e:
            logger.error(f"获取MySQL表列表失败: {str(e)}")
            return []
        finally:
            self.disconnect()
    
    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        try:
            if not self.connect():
                return []
            
            cursor = self.connection.cursor()
            cursor.execute(f"DESCRIBE {table_name}")
            
            schema = []
            for row in cursor.fetchall():
                schema.append({
                    "column_name": row[0],
                    "data_type": row[1],
                    "is_nullable": row[2] == "YES",
                    "default_value": row[4]
                })
            
            cursor.close()
            return schema
        except Exception as e:
            logger.error(f"获取MySQL表结构失败: {str(e)}")
            return []
        finally:
            self.disconnect()


class SQLServerAdapter(DatabaseAdapter):
    """SQL Server适配器"""

    def connect(self) -> bool:
        try:
            import pyodbc

            # 构建连接字符串
            conn_str = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={self.config['host']},{self.config['port']};"
                f"DATABASE={self.config['database']};"
                f"UID={self.config['username']};"
                f"PWD={self.config['password']};"
                f"TrustServerCertificate=yes;"
            )

            self.connection = pyodbc.connect(conn_str, timeout=10)
            return True
        except Exception as e:
            logger.error(f"SQL Server连接失败: {str(e)}")
            return False

    def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None

    def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connect():
                return {"success": False, "message": "连接失败"}

            cursor = self.connection.cursor()
            cursor.execute("SELECT @@VERSION;")
            version = cursor.fetchone()[0]
            cursor.close()

            return {
                "success": True,
                "message": "SQL Server连接成功",
                "details": {
                    "version": version[:100] + "..." if len(version) > 100 else version,
                    "host": self.config["host"],
                    "port": self.config["port"],
                    "database": self.config["database"]
                }
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"SQL Server连接测试失败: {str(e)}"
            }
        finally:
            self.disconnect()

    def execute_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        try:
            if not self.connect():
                raise Exception("无法建立连接")

            cursor = self.connection.cursor()

            # SQL Server使用TOP而不是LIMIT
            if "TOP" not in query.upper() and "LIMIT" not in query.upper():
                if query.upper().startswith("SELECT"):
                    query = query.replace("SELECT", f"SELECT TOP {limit}", 1)

            cursor.execute(query)

            # 获取列名
            columns = [desc[0] for desc in cursor.description] if cursor.description else []

            # 获取数据
            rows = cursor.fetchall()
            data = [dict(zip(columns, row)) for row in rows]

            cursor.close()

            return {
                "success": True,
                "data": data,
                "columns": columns,
                "total": len(data)
            }
        except Exception as e:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "error": str(e)
            }
        finally:
            self.disconnect()

    def get_tables(self) -> List[str]:
        try:
            if not self.connect():
                return []

            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_type = 'BASE TABLE'
                ORDER BY table_name
            """)

            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()

            return tables
        except Exception as e:
            logger.error(f"获取SQL Server表列表失败: {str(e)}")
            return []
        finally:
            self.disconnect()

    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        try:
            if not self.connect():
                return []

            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT
                    column_name,
                    data_type,
                    is_nullable,
                    column_default
                FROM information_schema.columns
                WHERE table_name = ?
                ORDER BY ordinal_position
            """, (table_name,))

            schema = []
            for row in cursor.fetchall():
                schema.append({
                    "column_name": row[0],
                    "data_type": row[1],
                    "is_nullable": row[2] == "YES",
                    "default_value": row[3]
                })

            cursor.close()
            return schema
        except Exception as e:
            logger.error(f"获取SQL Server表结构失败: {str(e)}")
            return []
        finally:
            self.disconnect()


class MongoDBAdapter(DatabaseAdapter):
    """MongoDB适配器"""

    def connect(self) -> bool:
        try:
            from pymongo import MongoClient

            # 构建连接URI
            if self.config.get("username") and self.config.get("password"):
                uri = f"mongodb://{self.config['username']}:{self.config['password']}@{self.config['host']}:{self.config['port']}/{self.config['database']}"
            else:
                uri = f"mongodb://{self.config['host']}:{self.config['port']}/{self.config['database']}"

            self.connection = MongoClient(uri, serverSelectionTimeoutMS=10000)
            # 测试连接
            self.connection.admin.command('ping')

            return True
        except Exception as e:
            logger.error(f"MongoDB连接失败: {str(e)}")
            return False

    def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None

    def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connect():
                return {"success": False, "message": "连接失败"}

            # 获取服务器信息
            server_info = self.connection.server_info()

            return {
                "success": True,
                "message": "MongoDB连接成功",
                "details": {
                    "version": server_info.get("version"),
                    "host": self.config["host"],
                    "port": self.config["port"],
                    "database": self.config["database"]
                }
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"MongoDB连接测试失败: {str(e)}"
            }
        finally:
            self.disconnect()

    def execute_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        try:
            if not self.connect():
                raise Exception("无法建立连接")

            db = self.connection[self.config["database"]]

            # MongoDB查询需要特殊处理
            try:
                # 尝试解析为JSON查询
                query_dict = json.loads(query)
                collection_name = query_dict.get("collection", "")
                find_query = query_dict.get("query", {})

                if not collection_name:
                    raise Exception("MongoDB查询必须指定collection")

                collection = db[collection_name]
                cursor = collection.find(find_query).limit(limit)

                data = list(cursor)

                # 处理ObjectId等特殊类型
                for item in data:
                    for key, value in item.items():
                        if hasattr(value, '__str__'):
                            item[key] = str(value)

                columns = list(data[0].keys()) if data else []

                return {
                    "success": True,
                    "data": data,
                    "columns": columns,
                    "total": len(data)
                }
            except json.JSONDecodeError:
                # 如果不是JSON格式，返回错误
                raise Exception("MongoDB查询必须是JSON格式，例如: {\"collection\": \"users\", \"query\": {}}")

        except Exception as e:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "error": str(e)
            }
        finally:
            self.disconnect()

    def get_tables(self) -> List[str]:
        """MongoDB中获取集合列表"""
        try:
            if not self.connect():
                return []

            db = self.connection[self.config["database"]]
            collections = db.list_collection_names()

            return collections
        except Exception as e:
            logger.error(f"获取MongoDB集合列表失败: {str(e)}")
            return []
        finally:
            self.disconnect()

    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """MongoDB中获取集合结构（采样分析）"""
        try:
            if not self.connect():
                return []

            db = self.connection[self.config["database"]]
            collection = db[table_name]

            # 采样分析文档结构
            sample_docs = list(collection.find().limit(10))

            if not sample_docs:
                return []

            # 分析字段类型
            fields = {}
            for doc in sample_docs:
                for key, value in doc.items():
                    if key not in fields:
                        fields[key] = type(value).__name__

            schema = []
            for field_name, field_type in fields.items():
                schema.append({
                    "column_name": field_name,
                    "data_type": field_type,
                    "is_nullable": True,  # MongoDB字段都是可选的
                    "default_value": None
                })

            return schema
        except Exception as e:
            logger.error(f"获取MongoDB集合结构失败: {str(e)}")
            return []
        finally:
            self.disconnect()


class RedisAdapter(DatabaseAdapter):
    """Redis适配器"""

    def connect(self) -> bool:
        try:
            import redis

            self.connection = redis.Redis(
                host=self.config["host"],
                port=self.config["port"],
                password=self.config.get("password"),
                socket_connect_timeout=10,
                decode_responses=True
            )

            # 测试连接
            self.connection.ping()
            return True
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            return False

    def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None

    def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connect():
                return {"success": False, "message": "连接失败"}

            # 获取服务器信息
            info = self.connection.info()

            return {
                "success": True,
                "message": "Redis连接成功",
                "details": {
                    "version": info.get("redis_version"),
                    "host": self.config["host"],
                    "port": self.config["port"],
                    "memory": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients")
                }
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Redis连接测试失败: {str(e)}"
            }
        finally:
            self.disconnect()

    def execute_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        try:
            if not self.connect():
                raise Exception("无法建立连接")

            # Redis查询需要特殊处理
            query = query.strip()

            if query.upper().startswith("KEYS"):
                # 处理KEYS命令
                keys = self.connection.execute_command(query)
                if isinstance(keys, list):
                    keys = keys[:limit]  # 限制结果数量
                    data = [{"key": key, "type": self.connection.type(key)} for key in keys]
                    columns = ["key", "type"]
                else:
                    data = [{"result": str(keys)}]
                    columns = ["result"]
            elif query.upper().startswith("GET"):
                # 处理GET命令
                result = self.connection.execute_command(query)
                data = [{"value": str(result)}]
                columns = ["value"]
            else:
                # 其他Redis命令
                result = self.connection.execute_command(query)
                if isinstance(result, list):
                    data = [{"item": str(item)} for item in result[:limit]]
                    columns = ["item"]
                else:
                    data = [{"result": str(result)}]
                    columns = ["result"]

            return {
                "success": True,
                "data": data,
                "columns": columns,
                "total": len(data)
            }
        except Exception as e:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "error": str(e)
            }
        finally:
            self.disconnect()

    def get_tables(self) -> List[str]:
        """Redis中获取键模式"""
        try:
            if not self.connect():
                return []

            # 获取所有键的模式（限制数量）
            keys = self.connection.keys("*")

            # 分析键的模式
            patterns = set()
            for key in keys[:100]:  # 限制分析数量
                if ":" in key:
                    pattern = key.split(":")[0] + ":*"
                    patterns.add(pattern)
                else:
                    patterns.add(key)

            return list(patterns)
        except Exception as e:
            logger.error(f"获取Redis键模式失败: {str(e)}")
            return []
        finally:
            self.disconnect()

    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """Redis中获取键结构"""
        try:
            if not self.connect():
                return []

            # 获取匹配模式的键
            keys = self.connection.keys(table_name)[:10]  # 限制分析数量

            schema = [
                {"column_name": "key", "data_type": "string", "is_nullable": False, "default_value": None},
                {"column_name": "type", "data_type": "string", "is_nullable": False, "default_value": None},
                {"column_name": "value", "data_type": "string", "is_nullable": True, "default_value": None}
            ]

            return schema
        except Exception as e:
            logger.error(f"获取Redis键结构失败: {str(e)}")
            return []
        finally:
            self.disconnect()


class SQLiteAdapter(DatabaseAdapter):
    """SQLite适配器"""

    def connect(self) -> bool:
        try:
            import sqlite3

            self.connection = sqlite3.connect(
                self.config["database"],
                timeout=10
            )
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            logger.error(f"SQLite连接失败: {str(e)}")
            return False

    def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None

    def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connect():
                return {"success": False, "message": "连接失败"}

            cursor = self.connection.cursor()
            cursor.execute("SELECT sqlite_version();")
            version = cursor.fetchone()[0]
            cursor.close()

            return {
                "success": True,
                "message": "SQLite连接成功",
                "details": {
                    "version": version,
                    "database": self.config["database"]
                }
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"SQLite连接测试失败: {str(e)}"
            }
        finally:
            self.disconnect()

    def execute_query(self, query: str, limit: int = 100) -> Dict[str, Any]:
        try:
            if not self.connect():
                raise Exception("无法建立连接")

            cursor = self.connection.cursor()

            # 添加LIMIT子句
            if "LIMIT" not in query.upper():
                query = f"{query} LIMIT {limit}"

            cursor.execute(query)

            # 获取数据
            rows = cursor.fetchall()
            columns = [description[0] for description in cursor.description] if cursor.description else []

            # 转换为字典列表
            data = [dict(row) for row in rows]

            cursor.close()

            return {
                "success": True,
                "data": data,
                "columns": columns,
                "total": len(data)
            }
        except Exception as e:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "error": str(e)
            }
        finally:
            self.disconnect()

    def get_tables(self) -> List[str]:
        try:
            if not self.connect():
                return []

            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)

            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()

            return tables
        except Exception as e:
            logger.error(f"获取SQLite表列表失败: {str(e)}")
            return []
        finally:
            self.disconnect()

    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        try:
            if not self.connect():
                return []

            cursor = self.connection.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")

            schema = []
            for row in cursor.fetchall():
                schema.append({
                    "column_name": row[1],
                    "data_type": row[2],
                    "is_nullable": row[3] == 0,  # SQLite中0表示NOT NULL
                    "default_value": row[4]
                })

            cursor.close()
            return schema
        except Exception as e:
            logger.error(f"获取SQLite表结构失败: {str(e)}")
            return []
        finally:
            self.disconnect()


class DatabaseFactory:
    """数据库工厂类"""

    _adapters = {
        DataSourceType.POSTGRESQL: PostgreSQLAdapter,
        DataSourceType.MYSQL: MySQLAdapter,
        DataSourceType.SQLSERVER: SQLServerAdapter,
        DataSourceType.MSSQL: SQLServerAdapter,  # MSSQL和SQLSERVER使用同一个适配器
        DataSourceType.MONGODB: MongoDBAdapter,
        DataSourceType.REDIS: RedisAdapter,
        DataSourceType.SQLITE: SQLiteAdapter,
    }

    @classmethod
    def create_adapter(cls, db_type: DataSourceType, config: Dict[str, Any]) -> Optional[DatabaseAdapter]:
        """
        创建数据库适配器。

        Args:
            db_type: 数据库类型
            config: 连接配置

        Returns:
            Optional[DatabaseAdapter]: 数据库适配器实例
        """
        adapter_class = cls._adapters.get(db_type)
        if not adapter_class:
            logger.error(f"不支持的数据库类型: {db_type}")
            return None

        try:
            return adapter_class(config)
        except Exception as e:
            logger.error(f"创建数据库适配器失败: {str(e)}")
            return None

    @classmethod
    def get_supported_types(cls) -> List[DataSourceType]:
        """获取支持的数据库类型列表"""
        return list(cls._adapters.keys())

    @classmethod
    def is_supported(cls, db_type: DataSourceType) -> bool:
        """检查是否支持指定的数据库类型"""
        return db_type in cls._adapters

    @classmethod
    def get_default_ports(cls) -> Dict[DataSourceType, int]:
        """获取各数据库类型的默认端口"""
        return {
            DataSourceType.POSTGRESQL: 5432,
            DataSourceType.MYSQL: 3306,
            DataSourceType.SQLSERVER: 1433,
            DataSourceType.MSSQL: 1433,
            DataSourceType.MONGODB: 27017,
            DataSourceType.REDIS: 6379,
            DataSourceType.SQLITE: 0,  # SQLite不需要端口
        }

    @classmethod
    def get_connection_requirements(cls, db_type: DataSourceType) -> Dict[str, Any]:
        """获取连接要求"""
        requirements = {
            DataSourceType.POSTGRESQL: {
                "required_fields": ["host", "port", "database", "username", "password"],
                "optional_fields": ["ssl_mode", "connect_timeout"],
                "default_port": 5432,
                "supports_ssl": True
            },
            DataSourceType.MYSQL: {
                "required_fields": ["host", "port", "database", "username", "password"],
                "optional_fields": ["charset", "connect_timeout"],
                "default_port": 3306,
                "supports_ssl": True
            },
            DataSourceType.SQLSERVER: {
                "required_fields": ["host", "port", "database", "username", "password"],
                "optional_fields": ["driver", "trust_certificate"],
                "default_port": 1433,
                "supports_ssl": True
            },
            DataSourceType.MSSQL: {
                "required_fields": ["host", "port", "database", "username", "password"],
                "optional_fields": ["driver", "trust_certificate"],
                "default_port": 1433,
                "supports_ssl": True
            },
            DataSourceType.MONGODB: {
                "required_fields": ["host", "port", "database"],
                "optional_fields": ["username", "password", "auth_source"],
                "default_port": 27017,
                "supports_ssl": True
            },
            DataSourceType.REDIS: {
                "required_fields": ["host", "port"],
                "optional_fields": ["password", "database"],
                "default_port": 6379,
                "supports_ssl": False
            },
            DataSourceType.SQLITE: {
                "required_fields": ["database"],
                "optional_fields": ["timeout"],
                "default_port": 0,
                "supports_ssl": False
            }
        }

        return requirements.get(db_type, {})


# 便捷函数
def create_database_adapter(db_type: DataSourceType, config: Dict[str, Any]) -> Optional[DatabaseAdapter]:
    """创建数据库适配器的便捷函数"""
    return DatabaseFactory.create_adapter(db_type, config)


def test_database_connection(db_type: DataSourceType, config: Dict[str, Any]) -> Dict[str, Any]:
    """测试数据库连接的便捷函数"""
    adapter = create_database_adapter(db_type, config)
    if not adapter:
        return {
            "success": False,
            "message": f"不支持的数据库类型: {db_type}"
        }

    return adapter.test_connection()


def execute_database_query(db_type: DataSourceType, config: Dict[str, Any], query: str, limit: int = 100) -> Dict[str, Any]:
    """执行数据库查询的便捷函数"""
    adapter = create_database_adapter(db_type, config)
    if not adapter:
        return {
            "success": False,
            "data": [],
            "columns": [],
            "total": 0,
            "error": f"不支持的数据库类型: {db_type}"
        }

    return adapter.execute_query(query, limit)
