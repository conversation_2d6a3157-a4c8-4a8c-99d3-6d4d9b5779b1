"""
数据源服务

处理数据源相关的业务逻辑。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import json

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.db.models.data_source import DataSource, DataSourceType, DataSourceStatus
from app.schemas.data_source import (
    DataSourceCreate, 
    DataSourceUpdate, 
    DataSourceTestRequest,
    DataSourceQuery
)
from app.core.security import encrypt_password, decrypt_password
from app.services.database_connector import DatabaseConnector

logger = logging.getLogger(__name__)


class DataSourceService:
    """数据源服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.connector = DatabaseConnector()
    
    def create_data_source(
        self, 
        data_source_data: DataSourceCreate, 
        created_by: int
    ) -> DataSource:
        """
        创建数据源。
        
        Args:
            data_source_data: 数据源创建数据
            created_by: 创建者ID
            
        Returns:
            DataSource: 创建的数据源对象
        """
        # 检查名称是否已存在
        existing = self.db.query(DataSource).filter(
            DataSource.name == data_source_data.name
        ).first()
        if existing:
            raise ValueError(f"数据源名称 '{data_source_data.name}' 已存在")
        
        # 加密密码
        encrypted_password = None
        if data_source_data.password:
            encrypted_password = encrypt_password(data_source_data.password)
        
        # 创建数据源对象
        db_data_source = DataSource(
            name=data_source_data.name,
            description=data_source_data.description,
            type=data_source_data.type,
            host=data_source_data.host,
            port=data_source_data.port,
            database=data_source_data.database,
            username=data_source_data.username,
            password=encrypted_password,
            config=data_source_data.config,
            is_active=data_source_data.is_active,
            created_by=created_by,
            status=DataSourceStatus.DISCONNECTED
        )
        
        self.db.add(db_data_source)
        self.db.commit()
        self.db.refresh(db_data_source)
        
        logger.info(f"创建数据源: {db_data_source.name} (ID: {db_data_source.id})")
        return db_data_source
    
    def get_data_source(self, data_source_id: int) -> Optional[DataSource]:
        """
        获取数据源。
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            Optional[DataSource]: 数据源对象
        """
        return self.db.query(DataSource).filter(
            DataSource.id == data_source_id
        ).first()
    
    def get_data_sources(
        self, 
        skip: int = 0, 
        limit: int = 100,
        search: Optional[str] = None,
        type_filter: Optional[DataSourceType] = None,
        status_filter: Optional[DataSourceStatus] = None,
        active_only: bool = True
    ) -> Tuple[List[DataSource], int]:
        """
        获取数据源列表。
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            search: 搜索关键词
            type_filter: 类型过滤
            status_filter: 状态过滤
            active_only: 仅活跃的
            
        Returns:
            Tuple[List[DataSource], int]: 数据源列表和总数
        """
        query = self.db.query(DataSource)
        
        # 构建过滤条件
        filters = []
        
        if active_only:
            filters.append(DataSource.is_active == True)
        
        if search:
            filters.append(
                or_(
                    DataSource.name.ilike(f"%{search}%"),
                    DataSource.description.ilike(f"%{search}%")
                )
            )
        
        if type_filter:
            filters.append(DataSource.type == type_filter)
        
        if status_filter:
            filters.append(DataSource.status == status_filter)
        
        if filters:
            query = query.filter(and_(*filters))
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        data_sources = query.order_by(DataSource.created_at.desc()).offset(skip).limit(limit).all()
        
        return data_sources, total
    
    def update_data_source(
        self, 
        data_source_id: int, 
        data_source_data: DataSourceUpdate
    ) -> Optional[DataSource]:
        """
        更新数据源。
        
        Args:
            data_source_id: 数据源ID
            data_source_data: 更新数据
            
        Returns:
            Optional[DataSource]: 更新后的数据源对象
        """
        db_data_source = self.get_data_source(data_source_id)
        if not db_data_source:
            return None
        
        # 更新字段
        update_data = data_source_data.dict(exclude_unset=True)
        
        # 处理密码加密
        if "password" in update_data and update_data["password"]:
            update_data["password"] = encrypt_password(update_data["password"])
        
        # 检查名称唯一性
        if "name" in update_data:
            existing = self.db.query(DataSource).filter(
                and_(
                    DataSource.name == update_data["name"],
                    DataSource.id != data_source_id
                )
            ).first()
            if existing:
                raise ValueError(f"数据源名称 '{update_data['name']}' 已存在")
        
        for field, value in update_data.items():
            setattr(db_data_source, field, value)
        
        self.db.commit()
        self.db.refresh(db_data_source)
        
        logger.info(f"更新数据源: {db_data_source.name} (ID: {db_data_source.id})")
        return db_data_source
    
    def delete_data_source(self, data_source_id: int) -> bool:
        """
        删除数据源。
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            bool: 是否删除成功
        """
        db_data_source = self.get_data_source(data_source_id)
        if not db_data_source:
            return False
        
        self.db.delete(db_data_source)
        self.db.commit()
        
        logger.info(f"删除数据源: {db_data_source.name} (ID: {db_data_source.id})")
        return True
    
    def test_connection(
        self, 
        test_data: DataSourceTestRequest
    ) -> Dict[str, Any]:
        """
        测试数据源连接。
        
        Args:
            test_data: 测试数据
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            result = self.connector.test_connection(
                db_type=test_data.type,
                host=test_data.host,
                port=test_data.port,
                database=test_data.database,
                username=test_data.username,
                password=test_data.password,
                config=test_data.config or {}
            )
            
            return {
                "success": result["success"],
                "message": result["message"],
                "details": result.get("details"),
                "test_time": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"测试连接失败: {str(e)}")
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}",
                "details": None,
                "test_time": datetime.now()
            }
    
    def test_data_source_connection(self, data_source_id: int) -> Dict[str, Any]:
        """
        测试已存在数据源的连接。
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        db_data_source = self.get_data_source(data_source_id)
        if not db_data_source:
            return {
                "success": False,
                "message": "数据源不存在",
                "details": None,
                "test_time": datetime.now()
            }
        
        # 解密密码
        password = None
        if db_data_source.password:
            try:
                password = decrypt_password(db_data_source.password)
            except Exception as e:
                logger.error(f"密码解密失败: {str(e)}")
                return {
                    "success": False,
                    "message": "密码解密失败",
                    "details": None,
                    "test_time": datetime.now()
                }
        
        # 执行测试
        test_data = DataSourceTestRequest(
            type=db_data_source.type,
            host=db_data_source.host,
            port=db_data_source.port,
            database=db_data_source.database,
            username=db_data_source.username,
            password=password,
            config=db_data_source.config
        )
        
        result = self.test_connection(test_data)
        
        # 更新数据源状态
        if result["success"]:
            db_data_source.status = DataSourceStatus.CONNECTED
        else:
            db_data_source.status = DataSourceStatus.ERROR
        
        db_data_source.last_test_time = result["test_time"]
        db_data_source.last_test_result = result["message"]
        
        self.db.commit()
        
        return result
    
    def execute_query(
        self, 
        query_data: DataSourceQuery
    ) -> Dict[str, Any]:
        """
        执行数据源查询。
        
        Args:
            query_data: 查询数据
            
        Returns:
            Dict[str, Any]: 查询结果
        """
        db_data_source = self.get_data_source(query_data.data_source_id)
        if not db_data_source:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "execution_time": 0,
                "message": "数据源不存在"
            }
        
        if db_data_source.status != DataSourceStatus.CONNECTED:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "execution_time": 0,
                "message": "数据源未连接"
            }
        
        try:
            # 解密密码
            password = None
            if db_data_source.password:
                password = decrypt_password(db_data_source.password)
            
            # 执行查询
            start_time = datetime.now()
            result = self.connector.execute_query(
                db_type=db_data_source.type,
                host=db_data_source.host,
                port=db_data_source.port,
                database=db_data_source.database,
                username=db_data_source.username,
                password=password,
                query=query_data.query,
                limit=query_data.limit,
                config=db_data_source.config or {}
            )
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            
            return {
                "success": True,
                "data": result["data"],
                "columns": result["columns"],
                "total": len(result["data"]),
                "execution_time": execution_time,
                "message": "查询执行成功"
            }
            
        except Exception as e:
            logger.error(f"查询执行失败: {str(e)}")
            return {
                "success": False,
                "data": [],
                "columns": [],
                "total": 0,
                "execution_time": 0,
                "message": f"查询执行失败: {str(e)}"
            }
