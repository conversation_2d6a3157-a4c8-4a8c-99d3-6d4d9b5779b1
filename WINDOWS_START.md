# Windows启动指南

## 🚀 推荐启动方式

### 方法1: 快速启动（最简单）
1. 双击运行 `quick-start.bat`
2. 等待服务启动完成
3. 访问 http://localhost

### 方法2: 环境检查 + 手动启动
1. 双击运行 `test-env.bat` 检查环境
2. 如果检查通过，在命令行运行：
   ```cmd
   docker-compose up -d --build
   ```

### 方法3: PowerShell启动
1. 右键点击项目文件夹，选择"在此处打开PowerShell窗口"
2. 运行：
   ```powershell
   .\start.ps1
   ```

### 方法4: 完全手动启动
按照 [手动启动指南](MANUAL_START.md) 逐步执行

## 🔧 前提条件

1. **安装Docker Desktop**
   - 下载：https://www.docker.com/products/docker-desktop
   - 安装后启动Docker Desktop应用

2. **确认Docker运行**
   - 系统托盘应该有Docker图标
   - 图标不应该显示错误状态

## 📝 启动步骤详解

### 步骤1: 检查环境
```cmd
# 运行环境检查
test-env.bat
```

### 步骤2: 启动服务
```cmd
# 快速启动
quick-start.bat

# 或者手动启动
docker-compose up -d --build
```

### 步骤3: 验证启动
```cmd
# 检查服务状态
docker-compose ps
```

### 步骤4: 访问应用
- 前端：http://localhost
- 后端：http://localhost:8000
- API文档：http://localhost:8000/api/v1/docs

## 🔍 故障排除

### 问题1: 脚本无法运行
**症状**: 双击脚本没有反应或闪退
**解决方案**:
1. 右键脚本 → "以管理员身份运行"
2. 或者打开命令提示符，手动运行脚本
3. 使用PowerShell版本：`.\start.ps1`

### 问题2: Docker命令不识别
**症状**: 提示"docker不是内部或外部命令"
**解决方案**:
1. 确保Docker Desktop已安装并启动
2. 重启命令提示符
3. 重启电脑

### 问题3: 端口被占用
**症状**: 启动时提示端口80或8000被占用
**解决方案**:
1. 关闭占用端口的程序（如IIS、Apache等）
2. 或者修改docker-compose.yml中的端口映射

### 问题4: 权限问题
**症状**: 提示权限不足
**解决方案**:
1. 以管理员身份运行命令提示符
2. 确保Docker Desktop有足够权限

### 问题5: 网络问题
**症状**: 下载镜像失败或很慢
**解决方案**:
1. 检查网络连接
2. 配置Docker镜像加速器
3. 使用VPN或更换网络

## 🎯 验证启动成功

启动成功的标志：

1. **命令行输出**：看到所有服务状态为"Up"
   ```
   NAME          IMAGE           STATUS
   bi_backend    test-backend    Up
   bi_frontend   test-frontend   Up
   bi_postgres   postgres:14     Up
   bi_redis      redis:7-alpine  Up
   ```

2. **网页访问**：
   - http://localhost 显示登录页面
   - http://localhost:8000/api/v1/docs 显示API文档

3. **登录测试**：
   - 邮箱：<EMAIL>
   - 密码：admin123
   - 能够成功登录并看到仪表板

## 🛠️ 常用命令

```cmd
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose down
docker-compose up -d --build
```

## 📞 获取帮助

如果仍然遇到问题：

1. **查看详细日志**：
   ```cmd
   docker-compose logs -f backend
   docker-compose logs -f frontend
   ```

2. **检查Docker状态**：
   - 确保Docker Desktop正在运行
   - 检查Docker设置中的资源分配

3. **重置环境**：
   ```cmd
   docker-compose down -v
   docker system prune -f
   docker-compose up -d --build
   ```

4. **查看文档**：
   - [手动启动指南](MANUAL_START.md)
   - [快速开始指南](GETTING_STARTED.md)

## 🎉 成功启动后

1. 访问 http://localhost
2. 使用默认账号登录
3. 探索各个功能模块
4. 查看示例数据和图表
5. 尝试自然语言查询功能

祝您使用愉快！
