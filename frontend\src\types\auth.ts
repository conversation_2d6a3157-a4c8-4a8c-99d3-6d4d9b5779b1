/**
 * 认证相关类型定义
 */

export interface User {
  id: number
  email: string
  username: string
  full_name?: string
  role: User<PERSON><PERSON>
  is_active: boolean
  is_verified: boolean
  avatar_url?: string
  phone?: string
  department?: string
  position?: string
  language: string
  theme: string
  timezone: string
  email_notifications: boolean
  push_notifications: boolean
  bio?: string
  created_at: string
  updated_at: string
  last_login_at?: string
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  ANALYST = 'analyst',
  USER = 'user',
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
  full_name?: string
  role?: UserRole
}

export interface AuthResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

export interface TokenPayload {
  sub: number
  exp: number
  type?: string
}
