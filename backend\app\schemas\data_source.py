"""
数据源Pydantic模式

定义数据源的请求和响应模式。
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.db.models.data_source import DataSourceType, DataSourceStatus


class DataSourceBase(BaseModel):
    """数据源基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="数据源名称")
    description: Optional[str] = Field(None, max_length=500, description="数据源描述")
    type: DataSourceType = Field(..., description="数据源类型")
    host: Optional[str] = Field(None, max_length=255, description="主机地址")
    port: Optional[int] = Field(None, ge=1, le=65535, description="端口号")
    database: Optional[str] = Field(None, max_length=100, description="数据库名")
    username: Optional[str] = Field(None, max_length=100, description="用户名")
    config: Optional[Dict[str, Any]] = Field(None, description="扩展配置")
    is_active: bool = Field(True, description="是否启用")


class DataSourceCreate(DataSourceBase):
    """创建数据源模式"""
    password: Optional[str] = Field(None, min_length=1, max_length=255, description="密码")
    
    @validator('port')
    def validate_port_for_type(cls, v, values):
        """根据数据源类型验证端口"""
        if v is None:
            return v
            
        data_type = values.get('type')
        if data_type == DataSourceType.POSTGRESQL and v != 5432:
            # 允许自定义端口，但给出提示
            pass
        elif data_type == DataSourceType.MYSQL and v != 3306:
            pass
        elif data_type == DataSourceType.MONGODB and v != 27017:
            pass
        elif data_type == DataSourceType.REDIS and v != 6379:
            pass
        
        return v
    
    @validator('host')
    def validate_host_required(cls, v, values):
        """验证主机地址是否必需"""
        data_type = values.get('type')
        if data_type in [
            DataSourceType.POSTGRESQL, 
            DataSourceType.MYSQL, 
            DataSourceType.MONGODB,
            DataSourceType.REDIS,
            DataSourceType.ORACLE,
            DataSourceType.SQLSERVER
        ] and not v:
            raise ValueError(f"主机地址对于 {data_type.value} 类型是必需的")
        return v


class DataSourceUpdate(BaseModel):
    """更新数据源模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="数据源名称")
    description: Optional[str] = Field(None, max_length=500, description="数据源描述")
    host: Optional[str] = Field(None, max_length=255, description="主机地址")
    port: Optional[int] = Field(None, ge=1, le=65535, description="端口号")
    database: Optional[str] = Field(None, max_length=100, description="数据库名")
    username: Optional[str] = Field(None, max_length=100, description="用户名")
    password: Optional[str] = Field(None, min_length=1, max_length=255, description="密码")
    config: Optional[Dict[str, Any]] = Field(None, description="扩展配置")
    is_active: Optional[bool] = Field(None, description="是否启用")


class DataSourceResponse(DataSourceBase):
    """数据源响应模式"""
    id: int = Field(..., description="数据源ID")
    status: DataSourceStatus = Field(..., description="连接状态")
    last_test_time: Optional[datetime] = Field(None, description="最后测试时间")
    last_test_result: Optional[str] = Field(None, description="最后测试结果")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class DataSourceTestRequest(BaseModel):
    """数据源测试请求模式"""
    type: DataSourceType = Field(..., description="数据源类型")
    host: str = Field(..., description="主机地址")
    port: int = Field(..., description="端口号")
    database: Optional[str] = Field(None, description="数据库名")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    config: Optional[Dict[str, Any]] = Field(None, description="扩展配置")


class DataSourceTestResponse(BaseModel):
    """数据源测试响应模式"""
    success: bool = Field(..., description="测试是否成功")
    message: str = Field(..., description="测试结果消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    test_time: datetime = Field(..., description="测试时间")


class DataSourceConnectionInfo(BaseModel):
    """数据源连接信息模式"""
    id: int = Field(..., description="数据源ID")
    name: str = Field(..., description="数据源名称")
    type: DataSourceType = Field(..., description="数据源类型")
    status: DataSourceStatus = Field(..., description="连接状态")
    host: Optional[str] = Field(None, description="主机地址")
    port: Optional[int] = Field(None, description="端口号")
    database: Optional[str] = Field(None, description="数据库名")


class DataSourceList(BaseModel):
    """数据源列表响应模式"""
    items: list[DataSourceResponse] = Field(..., description="数据源列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class DataSourceQuery(BaseModel):
    """数据源查询模式"""
    data_source_id: int = Field(..., description="数据源ID")
    query: str = Field(..., min_length=1, description="查询语句")
    limit: Optional[int] = Field(100, ge=1, le=10000, description="结果限制")


class DataSourceQueryResponse(BaseModel):
    """数据源查询响应模式"""
    success: bool = Field(..., description="查询是否成功")
    data: list[Dict[str, Any]] = Field(..., description="查询结果数据")
    columns: list[str] = Field(..., description="列名列表")
    total: int = Field(..., description="结果总数")
    execution_time: float = Field(..., description="执行时间（秒）")
    message: Optional[str] = Field(None, description="消息")
