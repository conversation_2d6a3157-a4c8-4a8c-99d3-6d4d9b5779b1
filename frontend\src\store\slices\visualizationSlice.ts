/**
 * 可视化状态管理
 */

import { createSlice } from '@reduxjs/toolkit'

interface VisualizationState {
  charts: any[]
  dashboards: any[]
  currentChart: any | null
  currentDashboard: any | null
  loading: boolean
  error: string | null
}

const initialState: VisualizationState = {
  charts: [],
  dashboards: [],
  currentChart: null,
  currentDashboard: null,
  loading: false,
  error: null,
}

const visualizationSlice = createSlice({
  name: 'visualization',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
})

export const { clearError } = visualizationSlice.actions
export default visualizationSlice.reducer
