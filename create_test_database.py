#!/usr/bin/env python3
"""
创建测试数据库

为数据源驱动测试创建一个简单的SQLite数据库。
"""

import sqlite3
import os
from datetime import datetime, timedelta
import random

def create_test_database():
    """创建测试数据库"""
    db_path = "test_database.db"
    
    # 删除已存在的数据库
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"🗑️ 删除已存在的数据库: {db_path}")
    
    # 创建新数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print(f"📊 正在创建测试数据库: {db_path}")
    
    # 创建用户表
    cursor.execute("""
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            age INTEGER,
            city TEXT,
            status TEXT DEFAULT 'active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 创建订单表
    cursor.execute("""
        CREATE TABLE orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            product_name TEXT NOT NULL,
            quantity INTEGER DEFAULT 1,
            price DECIMAL(10,2),
            order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'pending',
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """)
    
    # 创建产品表
    cursor.execute("""
        CREATE TABLE products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            price DECIMAL(10,2),
            category TEXT,
            stock INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 创建分类表
    cursor.execute("""
        CREATE TABLE categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            parent_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories (id)
        )
    """)
    
    # 创建日志表
    cursor.execute("""
        CREATE TABLE logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            level TEXT NOT NULL,
            message TEXT NOT NULL,
            module TEXT,
            user_id INTEGER,
            ip_address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """)
    
    print("✅ 数据表创建完成")
    
    # 插入测试数据
    print("📝 正在插入测试数据...")
    
    # 插入用户数据
    users_data = [
        ('张三', '<EMAIL>', 25, '北京', 'active'),
        ('李四', '<EMAIL>', 30, '上海', 'active'),
        ('王五', '<EMAIL>', 28, '广州', 'inactive'),
        ('赵六', '<EMAIL>', 35, '深圳', 'active'),
        ('钱七', '<EMAIL>', 22, '杭州', 'active'),
        ('孙八', '<EMAIL>', 29, '成都', 'active'),
        ('周九', '<EMAIL>', 31, '武汉', 'inactive'),
        ('吴十', '<EMAIL>', 27, '西安', 'active'),
        ('郑十一', '<EMAIL>', 33, '南京', 'active'),
        ('王十二', '<EMAIL>', 26, '重庆', 'active')
    ]
    
    cursor.executemany("""
        INSERT INTO users (name, email, age, city, status) 
        VALUES (?, ?, ?, ?, ?)
    """, users_data)
    
    # 插入分类数据
    categories_data = [
        ('电子产品', '各种电子设备和配件', None),
        ('服装鞋帽', '时尚服装和鞋帽', None),
        ('家居用品', '家庭生活用品', None),
        ('图书音像', '书籍和音像制品', None),
        ('手机数码', '手机和数码产品', 1),
        ('电脑办公', '电脑和办公设备', 1),
        ('男装', '男士服装', 2),
        ('女装', '女士服装', 2),
        ('家具', '各种家具', 3),
        ('厨具', '厨房用具', 3)
    ]
    
    cursor.executemany("""
        INSERT INTO categories (name, description, parent_id) 
        VALUES (?, ?, ?)
    """, categories_data)
    
    # 插入产品数据
    products_data = [
        ('iPhone 15', '苹果最新款手机', 1299.00, '手机数码', 50),
        ('MacBook Pro', '苹果笔记本电脑', 9999.00, '电脑办公', 20),
        ('Nike运动鞋', '舒适运动鞋', 599.00, '服装鞋帽', 100),
        ('办公椅', '人体工学办公椅', 899.00, '家具', 30),
        ('Python编程书', 'Python学习教程', 89.00, '图书音像', 200),
        ('无线耳机', '蓝牙无线耳机', 299.00, '手机数码', 80),
        ('机械键盘', '游戏机械键盘', 399.00, '电脑办公', 60),
        ('休闲T恤', '纯棉休闲T恤', 99.00, '男装', 150),
        ('连衣裙', '夏季连衣裙', 199.00, '女装', 120),
        ('不锈钢锅', '不锈钢炒锅', 299.00, '厨具', 40)
    ]
    
    cursor.executemany("""
        INSERT INTO products (name, description, price, category, stock) 
        VALUES (?, ?, ?, ?, ?)
    """, products_data)
    
    # 插入订单数据
    orders_data = []
    for i in range(50):
        user_id = random.randint(1, 10)
        product_id = random.randint(1, 10)
        quantity = random.randint(1, 5)
        price = random.uniform(50, 2000)
        status = random.choice(['pending', 'completed', 'cancelled', 'shipped'])
        
        # 随机生成过去30天内的日期
        days_ago = random.randint(0, 30)
        order_date = datetime.now() - timedelta(days=days_ago)
        
        orders_data.append((user_id, f'产品{product_id}', quantity, price, order_date.isoformat(), status))
    
    cursor.executemany("""
        INSERT INTO orders (user_id, product_name, quantity, price, order_date, status) 
        VALUES (?, ?, ?, ?, ?, ?)
    """, orders_data)
    
    # 插入日志数据
    logs_data = []
    log_levels = ['INFO', 'WARNING', 'ERROR', 'DEBUG']
    modules = ['auth', 'order', 'user', 'product', 'payment']
    messages = [
        '用户登录成功',
        '订单创建失败',
        '支付处理完成',
        '数据库连接超时',
        '用户注册成功',
        '商品库存不足',
        '系统启动完成',
        '缓存清理完成'
    ]
    
    for i in range(100):
        level = random.choice(log_levels)
        message = random.choice(messages)
        module = random.choice(modules)
        user_id = random.randint(1, 10) if random.random() > 0.3 else None
        ip_address = f"192.168.1.{random.randint(1, 255)}"
        
        # 随机生成过去7天内的日期
        days_ago = random.randint(0, 7)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        log_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
        
        logs_data.append((level, message, module, user_id, ip_address, log_time.isoformat()))
    
    cursor.executemany("""
        INSERT INTO logs (level, message, module, user_id, ip_address, created_at) 
        VALUES (?, ?, ?, ?, ?, ?)
    """, logs_data)
    
    # 提交事务
    conn.commit()
    
    # 显示统计信息
    cursor.execute("SELECT COUNT(*) FROM users")
    user_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM orders")
    order_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM products")
    product_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM categories")
    category_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM logs")
    log_count = cursor.fetchone()[0]
    
    print(f"📊 数据插入完成:")
    print(f"   👥 用户: {user_count} 条")
    print(f"   📦 订单: {order_count} 条")
    print(f"   🛍️ 产品: {product_count} 条")
    print(f"   📂 分类: {category_count} 条")
    print(f"   📝 日志: {log_count} 条")
    
    # 关闭连接
    conn.close()
    
    print(f"✅ 测试数据库创建完成: {os.path.abspath(db_path)}")
    return os.path.abspath(db_path)

if __name__ == "__main__":
    db_path = create_test_database()
    print(f"\n🎯 可以使用以下配置测试SQLite连接:")
    print(f"   数据库类型: sqlite")
    print(f"   数据库路径: {db_path}")
    print(f"\n📝 示例查询:")
    print(f"   SELECT * FROM users LIMIT 10")
    print(f"   SELECT * FROM orders WHERE status = 'completed'")
    print(f"   SELECT COUNT(*) FROM logs WHERE level = 'ERROR'")
