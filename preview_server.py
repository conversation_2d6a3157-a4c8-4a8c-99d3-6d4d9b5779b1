"""
BI平台预览服务器
快速启动一个预览版本来展示项目效果
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import webbrowser
import threading
import time

class PreviewHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            self.send_preview_page()
        elif self.path == '/api/health':
            self.send_json_response({
                "status": "healthy",
                "message": "BI数据分析平台预览版",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0-preview"
            })
        elif self.path == '/api/dashboard':
            self.send_dashboard_data()
        elif self.path == '/api/sales':
            self.send_sales_data()
        elif self.path == '/api/users':
            self.send_users_data()
        elif self.path == '/api/charts':
            self.send_charts_data()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/login':
            self.handle_login()
        elif self.path == '/api/query':
            self.handle_nlp_query()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def send_preview_page(self):
        """发送预览页面"""
        html = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>BI数据分析平台 - 预览版</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
                    background: #f5f5f5;
                    line-height: 1.6;
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px 0;
                    text-align: center;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
                .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
                .card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                    transition: transform 0.3s ease;
                }
                .card:hover { transform: translateY(-5px); }
                .card h3 { color: #333; margin-bottom: 15px; font-size: 18px; }
                .stats { display: flex; justify-content: space-between; margin: 15px 0; }
                .stat { text-align: center; }
                .stat-value { font-size: 24px; font-weight: bold; color: #667eea; }
                .stat-label { font-size: 12px; color: #666; margin-top: 5px; }
                .chart { height: 300px; margin: 15px 0; }
                .btn {
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    cursor: pointer;
                    margin: 5px;
                    transition: background 0.3s;
                }
                .btn:hover { background: #5a6fd8; }
                .success { color: #52c41a; }
                .warning { color: #faad14; }
                .error { color: #ff4d4f; }
                .feature-list { list-style: none; }
                .feature-list li { padding: 8px 0; border-bottom: 1px solid #f0f0f0; }
                .feature-list li:before { content: "✅ "; margin-right: 8px; }
                .demo-section { margin: 20px 0; }
                .login-form { max-width: 300px; margin: 0 auto; }
                .login-form input { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
                .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 8px; }
                .status-online { background: #52c41a; }
                .status-offline { background: #ff4d4f; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 BI数据分析平台</h1>
                <p>自然语言驱动的智能数据分析解决方案</p>
                <div style="margin-top: 15px;">
                    <span class="status-indicator status-online"></span>
                    <span>预览版本运行中</span>
                </div>
            </div>

            <div class="container">
                <!-- 系统状态 -->
                <div class="card">
                    <h3>📊 系统状态</h3>
                    <div class="stats">
                        <div class="stat">
                            <div class="stat-value success">运行中</div>
                            <div class="stat-label">系统状态</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">12</div>
                            <div class="stat-label">数据源</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">156</div>
                            <div class="stat-label">分析报告</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">89</div>
                            <div class="stat-label">活跃用户</div>
                        </div>
                    </div>
                </div>

                <div class="grid">
                    <!-- 功能特性 -->
                    <div class="card">
                        <h3>🎯 核心功能</h3>
                        <ul class="feature-list">
                            <li>自然语言数据查询</li>
                            <li>智能数据可视化</li>
                            <li>实时数字化大屏</li>
                            <li>多数据源集成</li>
                            <li>用户权限管理</li>
                            <li>API接口服务</li>
                        </ul>
                    </div>

                    <!-- 模拟登录 -->
                    <div class="card">
                        <h3>🔑 用户认证演示</h3>
                        <div class="login-form">
                            <input type="email" placeholder="邮箱" value="<EMAIL>" id="email">
                            <input type="password" placeholder="密码" value="admin123" id="password">
                            <button class="btn" onclick="testLogin()" style="width: 100%;">登录测试</button>
                            <div id="login-result" style="margin-top: 10px; text-align: center;"></div>
                        </div>
                    </div>
                </div>

                <!-- 数据可视化演示 -->
                <div class="grid">
                    <div class="card">
                        <h3>📈 销售趋势分析</h3>
                        <div id="salesChart" class="chart"></div>
                        <button class="btn" onclick="refreshSalesData()">刷新数据</button>
                    </div>

                    <div class="card">
                        <h3>🥧 产品类别分布</h3>
                        <div id="categoryChart" class="chart"></div>
                        <button class="btn" onclick="refreshCategoryData()">刷新数据</button>
                    </div>
                </div>

                <!-- 自然语言查询演示 -->
                <div class="card demo-section">
                    <h3>🤖 自然语言查询演示</h3>
                    <div style="margin: 15px 0;">
                        <input type="text" placeholder="请输入您的查询，例如：显示上个月的销售趋势" 
                               id="nlpQuery" style="width: 70%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        <button class="btn" onclick="executeQuery()">分析</button>
                    </div>
                    <div id="queryResult" style="margin-top: 15px;"></div>
                </div>

                <!-- API接口演示 -->
                <div class="card demo-section">
                    <h3>🔧 API接口演示</h3>
                    <div style="margin: 15px 0;">
                        <button class="btn" onclick="testAPI('/api/health')">健康检查</button>
                        <button class="btn" onclick="testAPI('/api/dashboard')">仪表板数据</button>
                        <button class="btn" onclick="testAPI('/api/sales')">销售数据</button>
                        <button class="btn" onclick="testAPI('/api/users')">用户数据</button>
                    </div>
                    <div id="apiResult" style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
                </div>
            </div>

            <script>
                // 初始化图表
                function initCharts() {
                    // 销售趋势图
                    const salesChart = echarts.init(document.getElementById('salesChart'));
                    const salesOption = {
                        tooltip: { trigger: 'axis' },
                        xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
                        yAxis: { type: 'value' },
                        series: [{
                            data: [120, 200, 150, 80, 70, 110],
                            type: 'line',
                            smooth: true,
                            itemStyle: { color: '#667eea' }
                        }]
                    };
                    salesChart.setOption(salesOption);

                    // 类别分布图
                    const categoryChart = echarts.init(document.getElementById('categoryChart'));
                    const categoryOption = {
                        tooltip: { trigger: 'item' },
                        series: [{
                            type: 'pie',
                            radius: '50%',
                            data: [
                                { value: 35, name: '电子产品' },
                                { value: 25, name: '服装' },
                                { value: 20, name: '食品' },
                                { value: 15, name: '图书' },
                                { value: 5, name: '其他' }
                            ]
                        }]
                    };
                    categoryChart.setOption(categoryOption);
                }

                // 测试登录
                function testLogin() {
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;
                    
                    fetch('/api/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username: email, password: password })
                    })
                    .then(response => response.json())
                    .then(data => {
                        const result = document.getElementById('login-result');
                        if (data.success) {
                            result.innerHTML = '<span class="success">✅ 登录成功</span>';
                        } else {
                            result.innerHTML = '<span class="error">❌ 登录失败</span>';
                        }
                    })
                    .catch(error => {
                        document.getElementById('login-result').innerHTML = '<span class="error">❌ 连接错误</span>';
                    });
                }

                // 执行自然语言查询
                function executeQuery() {
                    const query = document.getElementById('nlpQuery').value;
                    if (!query.trim()) return;

                    document.getElementById('queryResult').innerHTML = '<div style="color: #1890ff;">🔄 正在分析...</div>';

                    fetch('/api/query', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query: query })
                    })
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('queryResult').innerHTML = 
                            '<div style="background: #f6ffed; padding: 10px; border-radius: 4px; border-left: 4px solid #52c41a;">' +
                            '<strong>查询:</strong> ' + data.query + '<br>' +
                            '<strong>生成SQL:</strong> <code>' + data.sql + '</code><br>' +
                            '<strong>结果:</strong> 找到 ' + data.result.length + ' 条记录' +
                            '</div>';
                    });
                }

                // 测试API
                function testAPI(endpoint) {
                    fetch(endpoint)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('apiResult').innerHTML = 
                            '<strong>GET ' + endpoint + '</strong><br>' +
                            JSON.stringify(data, null, 2);
                    })
                    .catch(error => {
                        document.getElementById('apiResult').innerHTML = 
                            '<strong>GET ' + endpoint + '</strong><br>' +
                            '<span class="error">Error: ' + error.message + '</span>';
                    });
                }

                // 刷新数据
                function refreshSalesData() {
                    // 模拟数据刷新
                    console.log('刷新销售数据');
                }

                function refreshCategoryData() {
                    // 模拟数据刷新
                    console.log('刷新类别数据');
                }

                // 页面加载完成后初始化
                window.onload = function() {
                    initCharts();
                    // 自动测试API健康检查
                    setTimeout(() => testAPI('/api/health'), 1000);
                };
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_dashboard_data(self):
        """发送仪表板数据"""
        data = {
            "stats": {
                "total_users": 89,
                "total_reports": 156,
                "data_sources": 12,
                "active_sessions": 23
            },
            "recent_queries": [
                {"query": "显示上个月销售趋势", "time": "2小时前"},
                {"query": "用户行为分析", "time": "4小时前"},
                {"query": "产品销量对比", "time": "1天前"}
            ]
        }
        self.send_json_response(data)
    
    def send_sales_data(self):
        """发送销售数据"""
        data = {
            "data": [
                {"product": "iPhone 15", "sales": 8999, "region": "北京", "date": "2024-01-01"},
                {"product": "MacBook Pro", "sales": 15999, "region": "上海", "date": "2024-01-01"},
                {"product": "AirPods Pro", "sales": 1999, "region": "广州", "date": "2024-01-02"},
                {"product": "iPad Air", "sales": 4999, "region": "深圳", "date": "2024-01-02"},
                {"product": "Apple Watch", "sales": 2999, "region": "杭州", "date": "2024-01-03"}
            ],
            "total": 5,
            "summary": {
                "total_sales": 33995,
                "avg_sales": 6799,
                "top_region": "上海"
            }
        }
        self.send_json_response(data)
    
    def send_users_data(self):
        """发送用户数据"""
        data = {
            "users": [
                {"id": 1, "name": "张三", "email": "<EMAIL>", "role": "admin", "status": "active"},
                {"id": 2, "name": "李四", "email": "<EMAIL>", "role": "analyst", "status": "active"},
                {"id": 3, "name": "王五", "email": "<EMAIL>", "role": "user", "status": "inactive"}
            ],
            "total": 89,
            "active": 67,
            "roles": {"admin": 5, "analyst": 15, "user": 69}
        }
        self.send_json_response(data)
    
    def send_charts_data(self):
        """发送图表数据"""
        data = {
            "charts": [
                {"id": 1, "name": "销售趋势", "type": "line", "created": "2024-01-01"},
                {"id": 2, "name": "用户分布", "type": "pie", "created": "2024-01-02"},
                {"id": 3, "name": "产品对比", "type": "bar", "created": "2024-01-03"}
            ]
        }
        self.send_json_response(data)
    
    def handle_login(self):
        """处理登录请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            username = data.get('username', '')
            password = data.get('password', '')
            
            if username == '<EMAIL>' and password == 'admin123':
                self.send_json_response({
                    "success": True,
                    "message": "登录成功",
                    "token": "preview_token_12345",
                    "user": {
                        "id": 1,
                        "email": "<EMAIL>",
                        "name": "系统管理员",
                        "role": "admin"
                    }
                })
            else:
                self.send_json_response({
                    "success": False,
                    "message": "用户名或密码错误"
                }, status=401)
        except:
            self.send_json_response({
                "success": False,
                "message": "请求格式错误"
            }, status=400)
    
    def handle_nlp_query(self):
        """处理自然语言查询"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            query = data.get('query', '')
            
            # 模拟NLP处理
            mock_response = {
                "query": query,
                "sql": "SELECT product_name, SUM(sales_amount) as total FROM sales_data WHERE date >= '2024-01-01' GROUP BY product_name",
                "result": [
                    {"product_name": "iPhone 15", "total": 8999},
                    {"product_name": "MacBook Pro", "total": 15999},
                    {"product_name": "AirPods Pro", "total": 1999}
                ],
                "chart_suggestion": "bar",
                "execution_time": "0.15s"
            }
            
            self.send_json_response(mock_response)
        except:
            self.send_json_response({
                "error": "查询处理失败"
            }, status=400)
    
    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://localhost:8000')

def run_preview_server():
    """启动预览服务器"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, PreviewHandler)
    
    print("🚀 BI数据分析平台预览版启动成功!")
    print("=" * 60)
    print(f"📱 访问地址: http://localhost:8000")
    print(f"🎯 功能演示: 完整的BI平台界面和功能")
    print(f"🔑 测试登录: <EMAIL> / admin123")
    print(f"📊 包含功能: 数据可视化、自然语言查询、API接口")
    print("=" * 60)
    print("⚡ 正在自动打开浏览器...")
    print("按 Ctrl+C 停止服务器")
    print()
    
    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 预览服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_preview_server()
