# 手动启动指南

如果自动启动脚本不工作，请按照以下步骤手动启动项目：

## 前提条件

1. **安装Docker Desktop**
   - 下载地址: https://www.docker.com/products/docker-desktop
   - 安装后确保Docker Desktop正在运行

2. **验证安装**
   ```cmd
   docker --version
   docker-compose --version
   ```

## 启动步骤

### 1. 打开命令提示符或PowerShell
- 按 `Win + R`，输入 `cmd` 或 `powershell`
- 或者在项目文件夹中右键选择"在此处打开PowerShell窗口"

### 2. 进入项目目录
```cmd
cd C:\Users\<USER>\test\test
```

### 3. 创建环境配置文件（首次运行）
```cmd
copy backend\.env.example backend\.env
```

### 4. 启动服务
```cmd
docker-compose up -d --build
```

### 5. 等待服务启动
等待约1-2分钟，让所有服务完全启动。

### 6. 检查服务状态
```cmd
docker-compose ps
```

应该看到类似以下输出：
```
NAME                IMAGE               COMMAND                  SERVICE             CREATED             STATUS              PORTS
bi_backend          test-backend        "uvicorn app.main:ap…"   backend             2 minutes ago       Up 2 minutes        0.0.0.0:8000->8000/tcp
bi_frontend         test-frontend       "/docker-entrypoint.…"   frontend            2 minutes ago       Up 2 minutes        0.0.0.0:80->80/tcp
bi_postgres         postgres:14         "docker-entrypoint.s…"   postgres            2 minutes ago       Up 2 minutes        0.0.0.0:5432->5432/tcp
bi_redis            redis:7-alpine      "docker-entrypoint.s…"   redis               2 minutes ago       Up 2 minutes        0.0.0.0:6379->6379/tcp
```

## 访问应用

启动成功后，在浏览器中访问：

- **前端应用**: http://localhost
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/v1/docs

## 默认登录信息

- **邮箱**: <EMAIL>
- **密码**: admin123

## 常用命令

### 查看日志
```cmd
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
```

### 停止服务
```cmd
docker-compose down
```

### 重启服务
```cmd
docker-compose restart
```

### 重新构建并启动
```cmd
docker-compose down
docker-compose up -d --build
```

## 故障排除

### 问题1: 端口被占用
如果看到端口被占用的错误，检查以下端口是否被其他程序使用：
- 80 (前端)
- 8000 (后端)
- 5432 (PostgreSQL)
- 6379 (Redis)

解决方法：
1. 关闭占用端口的程序
2. 或者修改 `docker-compose.yml` 中的端口映射

### 问题2: Docker Desktop未启动
确保Docker Desktop应用程序正在运行。在系统托盘中应该能看到Docker图标。

### 问题3: 权限问题
如果遇到权限问题，尝试以管理员身份运行命令提示符或PowerShell。

### 问题4: 网络问题
如果下载Docker镜像很慢，可以配置Docker镜像加速器：
1. 打开Docker Desktop
2. 进入Settings -> Docker Engine
3. 添加镜像加速器配置

### 问题5: 内存不足
确保系统有足够的可用内存（建议至少4GB）。可以在Docker Desktop设置中调整内存分配。

## 开发模式

如果要进行开发，可以分别启动前后端：

### 启动后端开发环境
```cmd
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 启动前端开发环境
```cmd
cd frontend
npm install
npm run dev
```

## 获取帮助

如果仍然遇到问题：

1. 检查Docker Desktop是否正常运行
2. 查看详细的错误日志
3. 确保所有文件都已正确创建
4. 检查网络连接
5. 重启Docker Desktop

## 验证安装

成功启动后，您应该能够：

1. 访问 http://localhost 看到登录页面
2. 使用默认账号登录
3. 看到仪表板页面
4. 访问 http://localhost:8000/api/v1/docs 看到API文档

如果以上步骤都正常，说明系统已经成功启动！
