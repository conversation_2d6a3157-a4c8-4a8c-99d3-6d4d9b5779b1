#!/usr/bin/env python3
"""
简化的删除功能测试服务器
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import threading
import time
import webbrowser

# 全局数据存储
data_sources = []
next_id = 1

class SimpleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_test_page()
        elif self.path == '/api/v1/data-sources/':
            self.handle_get_list()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        if self.path == '/api/v1/auth/login':
            self.handle_login()
        elif self.path == '/api/v1/data-sources/':
            self.handle_create()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_DELETE(self):
        if self.path.startswith('/api/v1/data-sources/'):
            self.handle_delete()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def handle_login(self):
        self.send_json_response({
            "access_token": "test_token_12345",
            "token_type": "bearer"
        })
    
    def handle_create(self):
        global data_sources, next_id
        
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))
        
        new_ds = {
            "id": next_id,
            "name": data["name"],
            "description": data.get("description", ""),
            "type": data["type"],
            "status": "disconnected",
            "created_at": datetime.now().isoformat()
        }
        
        data_sources.append(new_ds)
        next_id += 1
        
        print(f"✅ 创建数据源: {new_ds['name']} (ID: {new_ds['id']})")
        print(f"📊 当前总数: {len(data_sources)}")
        
        self.send_json_response(new_ds)
    
    def handle_get_list(self):
        global data_sources
        
        print(f"📋 获取数据源列表，当前总数: {len(data_sources)}")
        
        self.send_json_response({
            "items": data_sources,
            "total": len(data_sources)
        })
    
    def handle_delete(self):
        global data_sources
        
        # 提取ID
        try:
            ds_id = int(self.path.split('/')[-1])
            print(f"🗑️ 删除请求 - ID: {ds_id}")
            print(f"📊 删除前数据源: {[ds['id'] for ds in data_sources]}")
            
            # 查找数据源
            ds_to_delete = None
            for ds in data_sources:
                if ds["id"] == ds_id:
                    ds_to_delete = ds
                    break
            
            if not ds_to_delete:
                print(f"❌ 数据源 {ds_id} 不存在")
                self.send_json_response({"detail": f"数据源 {ds_id} 不存在"}, status=404)
                return
            
            # 执行删除
            original_count = len(data_sources)
            data_sources = [ds for ds in data_sources if ds["id"] != ds_id]
            new_count = len(data_sources)
            
            print(f"📊 删除前: {original_count}, 删除后: {new_count}")
            print(f"📊 删除后数据源: {[ds['id'] for ds in data_sources]}")
            
            if original_count > new_count:
                message = f"数据源 '{ds_to_delete['name']}' (ID: {ds_id}) 删除成功"
                print(f"✅ {message}")
                self.send_json_response({"message": message})
            else:
                print(f"❌ 删除失败")
                self.send_json_response({"detail": "删除操作失败"}, status=500)
                
        except ValueError as e:
            print(f"❌ ID解析错误: {e}")
            self.send_json_response({"detail": f"无效的数据源ID: {str(e)}"}, status=400)
        except Exception as e:
            print(f"❌ 删除异常: {e}")
            self.send_json_response({"detail": f"删除失败: {str(e)}"}, status=500)
    
    def send_test_page(self):
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>删除功能测试</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; cursor: pointer; }
                .btn:hover { background: #0056b3; }
                .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
                .list-item { border: 1px solid #ddd; padding: 10px; margin: 5px 0; }
            </style>
        </head>
        <body>
            <h1>🗑️ 删除功能测试</h1>
            
            <div>
                <h3>创建测试数据源</h3>
                <input type="text" id="dsName" placeholder="数据源名称" value="测试数据源">
                <button class="btn" onclick="createDataSource()">创建数据源</button>
            </div>
            
            <div>
                <h3>数据源列表</h3>
                <button class="btn" onclick="loadDataSources()">刷新列表</button>
                <div id="dataSourceList"></div>
            </div>
            
            <div>
                <h3>操作日志</h3>
                <div id="result" class="result"></div>
            </div>

            <script>
                let token = null;

                // 自动登录
                async function autoLogin() {
                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ username: 'admin', password: 'admin' })
                        });
                        const data = await response.json();
                        token = data.access_token;
                        log('✅ 自动登录成功');
                    } catch (error) {
                        log('❌ 登录失败: ' + error.message);
                    }
                }

                // 创建数据源
                async function createDataSource() {
                    const name = document.getElementById('dsName').value;
                    if (!name) {
                        log('❌ 请输入数据源名称');
                        return;
                    }

                    try {
                        const response = await fetch('/api/v1/data-sources/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify({
                                name: name,
                                description: '测试用数据源',
                                type: 'sqlite'
                            })
                        });

                        const result = await response.json();
                        if (response.ok) {
                            log(`✅ 创建成功: ${result.name} (ID: ${result.id})`);
                            loadDataSources();
                        } else {
                            log(`❌ 创建失败: ${result.detail}`);
                        }
                    } catch (error) {
                        log('❌ 创建请求失败: ' + error.message);
                    }
                }

                // 加载数据源列表
                async function loadDataSources() {
                    try {
                        const response = await fetch('/api/v1/data-sources/', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        const result = await response.json();
                        if (response.ok) {
                            displayDataSources(result.items);
                            log(`📋 加载列表成功: 共${result.total}个数据源`);
                        } else {
                            log(`❌ 加载失败: ${result.detail}`);
                        }
                    } catch (error) {
                        log('❌ 加载请求失败: ' + error.message);
                    }
                }

                // 显示数据源列表
                function displayDataSources(dataSources) {
                    const container = document.getElementById('dataSourceList');
                    if (dataSources.length === 0) {
                        container.innerHTML = '<p>暂无数据源</p>';
                        return;
                    }

                    let html = '';
                    dataSources.forEach(ds => {
                        html += `
                            <div class="list-item">
                                <strong>${ds.name}</strong> (ID: ${ds.id})
                                <button class="btn" onclick="deleteDataSource(${ds.id})" style="background: #dc3545;">删除</button>
                            </div>
                        `;
                    });
                    container.innerHTML = html;
                }

                // 删除数据源
                async function deleteDataSource(id) {
                    if (!confirm(`确定要删除数据源 ID: ${id} 吗？`)) return;

                    try {
                        log(`🔄 正在删除数据源 ID: ${id}...`);
                        
                        const response = await fetch(`/api/v1/data-sources/${id}`, {
                            method: 'DELETE',
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        const result = await response.json();
                        if (response.ok) {
                            log(`✅ ${result.message}`);
                            loadDataSources();
                        } else {
                            log(`❌ 删除失败: ${result.detail}`);
                        }
                    } catch (error) {
                        log(`❌ 删除请求失败: ${error.message}`);
                    }
                }

                // 记录日志
                function log(message) {
                    const resultDiv = document.getElementById('result');
                    const timestamp = new Date().toLocaleTimeString();
                    resultDiv.innerHTML = `[${timestamp}] ${message}<br>` + resultDiv.innerHTML;
                }

                // 页面加载完成后自动登录
                window.onload = function() {
                    autoLogin();
                    setTimeout(loadDataSources, 1000);
                };
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

def open_browser():
    time.sleep(2)
    webbrowser.open('http://localhost:8000')

def run_server():
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, SimpleHandler)
    
    print("🚀 简化删除测试服务器启动成功!")
    print("📱 访问地址: http://localhost:8000")
    print("🧪 专门测试删除功能")
    print("按 Ctrl+C 停止服务器")
    print()
    
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
