"""
API v1 路由汇总

汇总所有API v1版本的路由。
"""

from fastapi import APIRouter

from app.api.api_v1.endpoints import auth, users, health, data_sources

api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(data_sources.router, prefix="/data-sources", tags=["data-sources"])
