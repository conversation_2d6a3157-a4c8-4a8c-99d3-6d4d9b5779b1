#!/bin/bash

# 数据库驱动一键安装脚本 (Linux/Mac)

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo "============================================================"
    echo "🗄️ 数据库驱动一键安装器"
    echo "============================================================"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}📋 $1${NC}"
}

# 检查Python
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        if ! command -v python &> /dev/null; then
            print_error "Python未安装或不在PATH中"
            echo "请先安装Python 3.8+"
            exit 1
        else
            PYTHON_CMD="python"
        fi
    else
        PYTHON_CMD="python3"
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $PYTHON_VERSION"
    
    # 检查pip
    if ! $PYTHON_CMD -m pip --version &> /dev/null; then
        print_error "pip不可用"
        exit 1
    fi
}

# 升级pip
upgrade_pip() {
    print_info "升级pip..."
    $PYTHON_CMD -m pip install --upgrade pip
    print_success "pip升级完成"
}

# 安装包
install_package() {
    local package=$1
    local description=$2
    
    echo
    print_info "安装 $description..."
    
    if $PYTHON_CMD -m pip install "$package"; then
        print_success "$description 安装成功"
        return 0
    else
        print_warning "$description 安装失败，继续安装其他驱动..."
        return 1
    fi
}

# 测试导入
test_import() {
    local module=$1
    local description=$2
    
    if $PYTHON_CMD -c "import $module; print('✅ $description 可用')" 2>/dev/null; then
        return 0
    else
        print_error "$description 不可用"
        return 1
    fi
}

# 安装系统依赖 (Linux)
install_system_deps() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_info "检查系统依赖..."
        
        # 检测Linux发行版
        if command -v apt-get &> /dev/null; then
            # Debian/Ubuntu
            print_info "检测到Debian/Ubuntu系统"
            if command -v sudo &> /dev/null; then
                sudo apt-get update
                sudo apt-get install -y python3-dev libpq-dev build-essential
            else
                print_warning "需要sudo权限安装系统依赖"
            fi
        elif command -v yum &> /dev/null; then
            # CentOS/RHEL
            print_info "检测到CentOS/RHEL系统"
            if command -v sudo &> /dev/null; then
                sudo yum install -y python3-devel postgresql-devel gcc
            else
                print_warning "需要sudo权限安装系统依赖"
            fi
        elif command -v dnf &> /dev/null; then
            # Fedora
            print_info "检测到Fedora系统"
            if command -v sudo &> /dev/null; then
                sudo dnf install -y python3-devel postgresql-devel gcc
            else
                print_warning "需要sudo权限安装系统依赖"
            fi
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        print_info "检测到macOS系统"
        if command -v brew &> /dev/null; then
            print_info "使用Homebrew安装系统依赖..."
            brew install postgresql mysql mongodb redis
        else
            print_warning "建议安装Homebrew以获得更好的支持"
        fi
    fi
}

# 主函数
main() {
    print_header
    
    # 检查Python环境
    check_python
    
    # 安装系统依赖
    install_system_deps
    
    # 升级pip
    upgrade_pip
    
    echo
    print_info "安装核心数据库驱动..."
    echo "============================================================"
    
    # 核心驱动
    core_success=0
    core_total=4
    
    install_package "psycopg2-binary" "PostgreSQL驱动" && ((core_success++))
    install_package "pymysql" "MySQL驱动" && ((core_success++))
    install_package "pymongo" "MongoDB驱动" && ((core_success++))
    install_package "redis" "Redis驱动" && ((core_success++))
    
    echo
    print_info "安装可选数据库驱动..."
    echo "============================================================"
    
    # 可选驱动
    optional_success=0
    optional_total=3
    
    install_package "pyodbc" "SQL Server驱动" && ((optional_success++))
    install_package "elasticsearch" "Elasticsearch驱动" && ((optional_success++))
    install_package "duckdb" "DuckDB驱动" && ((optional_success++))
    
    echo
    print_info "测试驱动安装..."
    echo "============================================================"
    
    # 测试驱动
    test_success=0
    test_total=5
    
    test_import "sqlite3" "SQLite (内置)" && ((test_success++))
    test_import "psycopg2" "PostgreSQL驱动" && ((test_success++))
    test_import "pymysql" "MySQL驱动" && ((test_success++))
    test_import "pymongo" "MongoDB驱动" && ((test_success++))
    test_import "redis" "Redis驱动" && ((test_success++))
    
    echo
    echo "============================================================"
    print_info "安装总结"
    echo "============================================================"
    
    echo "核心驱动: $core_success/$core_total 成功"
    echo "可选驱动: $optional_success/$optional_total 成功"
    echo "驱动测试: $test_success/$test_total 通过"
    
    if [ $core_success -ge 3 ]; then
        echo
        print_success "数据库驱动安装完成！"
        echo
        echo "🚀 下一步:"
        echo "  1. 启动真实连接测试服务器: $PYTHON_CMD real_connection_server.py"
        echo "  2. 打开高级数据源管理平台进行测试"
        echo "  3. 尝试创建和测试数据源连接"
        echo
        echo "💡 提示:"
        echo "  - SQLite 立即可用，无需额外配置"
        echo "  - 其他数据库需要相应的服务器运行"
        echo "  - 如有安装失败，请查看错误信息或手动安装"
    else
        echo
        print_warning "部分核心驱动安装失败"
        echo "💡 可以先使用SQLite进行测试"
    fi
    
    echo
    echo "============================================================"
}

# 运行主函数
main "$@"
