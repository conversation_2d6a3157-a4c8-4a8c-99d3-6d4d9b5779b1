# 手动开发环境搭建指南

## 🎯 目标
避免Docker构建慢的问题，直接在本地运行开发环境进行测试。

## 📋 前提条件

### 必需软件
1. **Python 3.10+**
   - 下载：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **Node.js 18+**
   - 下载：https://nodejs.org/
   - 推荐下载LTS版本

3. **Docker Desktop**（仅用于数据库）
   - 下载：https://www.docker.com/products/docker-desktop

### 验证安装
```cmd
python --version
node --version
npm --version
docker --version
```

## 🚀 启动步骤

### 步骤1: 启动数据库服务
```cmd
# 在项目根目录运行
start-db-only.bat
```

这将启动：
- PostgreSQL (端口5432)
- Redis (端口6379)

### 步骤2: 启动后端服务
```cmd
# 打开新的命令提示符窗口
cd backend
start-dev.bat
```

这将：
- 创建Python虚拟环境
- 安装所需依赖
- 启动FastAPI开发服务器 (http://localhost:8000)

### 步骤3: 启动前端服务
```cmd
# 打开第三个命令提示符窗口
cd frontend
start-dev.bat
```

这将：
- 安装Node.js依赖
- 启动Vite开发服务器 (http://localhost:3000)

## 🌐 访问地址

启动成功后：
- **前端**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/v1/docs
- **数据库**: localhost:5432 (postgres/password)

## 🔑 默认登录信息

- **邮箱**: <EMAIL>
- **密码**: admin123

## 🛠️ 开发工作流

### 修改代码
- **后端代码**: 修改`backend/`目录下的文件，服务器会自动重载
- **前端代码**: 修改`frontend/src/`目录下的文件，页面会自动刷新

### 查看日志
- **后端日志**: 在后端命令提示符窗口查看
- **前端日志**: 在前端命令提示符窗口查看
- **数据库日志**: `docker logs bi_postgres`

### 停止服务
- **后端/前端**: 在对应窗口按 `Ctrl+C`
- **数据库**: `docker stop bi_postgres bi_redis`

## 🔍 故障排除

### 问题1: Python虚拟环境创建失败
```cmd
# 手动创建虚拟环境
cd backend
python -m venv venv
venv\Scripts\activate.bat
pip install -r requirements.txt
```

### 问题2: npm安装依赖失败
```cmd
# 清理缓存重新安装
cd frontend
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 问题3: 数据库连接失败
```cmd
# 检查数据库状态
docker ps
docker logs bi_postgres

# 重启数据库
docker restart bi_postgres
```

### 问题4: 端口被占用
- 后端端口8000被占用：修改`backend/start-dev.bat`中的端口
- 前端端口3000被占用：Vite会自动选择其他端口
- 数据库端口5432被占用：修改`start-db-only.bat`中的端口映射

## 📝 开发提示

### 后端开发
- 代码位置：`backend/app/`
- 主要文件：
  - `main.py` - 应用入口
  - `api/` - API路由
  - `db/models/` - 数据模型
  - `services/` - 业务逻辑

### 前端开发
- 代码位置：`frontend/src/`
- 主要文件：
  - `App.tsx` - 应用入口
  - `pages/` - 页面组件
  - `components/` - 可复用组件
  - `store/` - 状态管理

### 数据库操作
```cmd
# 连接数据库
docker exec -it bi_postgres psql -U postgres -d bi_platform

# 查看表
\dt

# 查看示例数据
SELECT * FROM sample_sales LIMIT 5;
```

## 🎉 验证安装

1. 访问 http://localhost:3000 看到登录页面
2. 使用默认账号登录成功
3. 访问 http://localhost:8000/api/v1/docs 看到API文档
4. 在自然语言分析页面尝试查询

如果以上都正常，说明开发环境搭建成功！
