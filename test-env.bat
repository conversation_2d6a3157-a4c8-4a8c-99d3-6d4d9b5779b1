@echo off
echo Environment Check
echo ================

echo.
echo 1. Checking Docker...
docker --version
if errorlevel 1 (
    echo ERROR: Docker not installed or not running
    goto :error
)

echo.
echo 2. Checking Docker Compose...
docker-compose --version
if errorlevel 1 (
    echo ERROR: Docker Compose not installed
    goto :error
)

echo.
echo 3. Checking project files...
if not exist "docker-compose.yml" (
    echo ERROR: docker-compose.yml file not found
    goto :error
)

if not exist "backend\Dockerfile" (
    echo ERROR: backend\Dockerfile file not found
    goto :error
)

if not exist "frontend\Dockerfile" (
    echo ERROR: frontend\Dockerfile file not found
    goto :error
)

echo.
echo 4. Checking environment config...
if not exist "backend\.env" (
    if exist "backend\.env.example" (
        echo Creating environment config file...
        copy "backend\.env.example" "backend\.env" >nul
        echo Created backend\.env
    ) else (
        echo ERROR: backend\.env.example file not found
        goto :error
    )
) else (
    echo backend\.env file exists
)

echo.
echo 5. Testing Docker Compose config...
docker-compose config >nul
if errorlevel 1 (
    echo ERROR: docker-compose.yml configuration error
    goto :error
)

echo.
echo SUCCESS: Environment check passed!
echo.
echo You can now run the following command to start services:
echo docker-compose up -d --build
echo.
goto :end

:error
echo.
echo FAILED: Environment check failed, please fix the above errors
echo.

:end
pause
