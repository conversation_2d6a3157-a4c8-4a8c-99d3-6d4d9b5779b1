# 数据库连接依赖包
# Database Connection Dependencies

# ===== 核心依赖 =====
# Core Dependencies
requests>=2.31.0
urllib3>=2.0.0

# ===== SQLite =====
# SQLite (内置于Python，无需额外安装)
# Built-in with Python, no additional installation required

# ===== PostgreSQL =====
# PostgreSQL Database Driver
psycopg2-binary>=2.9.7
# 备选驱动 Alternative driver
# asyncpg>=0.28.0  # 异步驱动 Async driver

# ===== MySQL =====
# MySQL Database Driver
pymysql>=1.1.0
# 备选驱动 Alternative drivers
# mysql-connector-python>=8.1.0
# aiomysql>=0.2.0  # 异步驱动 Async driver

# ===== SQL Server =====
# Microsoft SQL Server Driver
pyodbc>=4.0.39
# 备选驱动 Alternative driver
# pymssql>=2.2.8

# ===== MongoDB =====
# MongoDB Document Database Driver
pymongo>=4.5.0
# 备选驱动 Alternative driver
# motor>=3.3.0  # 异步驱动 Async driver

# ===== Redis =====
# Redis In-Memory Database Driver
redis>=5.0.0
# 备选驱动 Alternative driver
# aioredis>=2.0.0  # 异步驱动 Async driver

# ===== Oracle =====
# Oracle Database Driver
cx-Oracle>=8.3.0
# 注意：需要安装Oracle Instant Client
# Note: Requires Oracle Instant Client installation

# ===== Elasticsearch =====
# Elasticsearch Search Engine Driver
elasticsearch>=8.9.0

# ===== InfluxDB =====
# InfluxDB Time Series Database Driver
influxdb-client>=1.37.0

# ===== Cassandra =====
# Apache Cassandra Driver
cassandra-driver>=3.28.0

# ===== Neo4j =====
# Neo4j Graph Database Driver
neo4j>=5.12.0

# ===== ClickHouse =====
# ClickHouse Analytics Database Driver
clickhouse-driver>=0.2.6
clickhouse-connect>=0.6.12

# ===== DuckDB =====
# DuckDB Analytics Database Driver
duckdb>=0.8.1

# ===== 连接池和工具 =====
# Connection Pooling and Utilities
SQLAlchemy>=2.0.20  # ORM和连接池 ORM and connection pooling
sqlalchemy-utils>=0.41.1  # SQLAlchemy工具 SQLAlchemy utilities

# ===== 数据库迁移工具 =====
# Database Migration Tools
alembic>=1.12.0  # 数据库迁移 Database migrations

# ===== 配置和环境 =====
# Configuration and Environment
python-dotenv>=1.0.0  # 环境变量管理 Environment variable management
pydantic>=2.3.0  # 数据验证 Data validation
pydantic-settings>=2.0.3  # 设置管理 Settings management

# ===== 异步支持 =====
# Async Support
asyncio>=3.4.3
aiofiles>=23.2.1
asyncpg>=0.28.0  # PostgreSQL异步驱动
aiomysql>=0.2.0  # MySQL异步驱动
aioredis>=2.0.0  # Redis异步驱动
motor>=3.3.0     # MongoDB异步驱动

# ===== 数据处理 =====
# Data Processing
pandas>=2.1.0  # 数据分析 Data analysis
numpy>=1.24.0   # 数值计算 Numerical computing

# ===== 日志和监控 =====
# Logging and Monitoring
loguru>=0.7.0  # 高级日志 Advanced logging
prometheus-client>=0.17.1  # 监控指标 Monitoring metrics

# ===== 安全 =====
# Security
cryptography>=41.0.4  # 加密支持 Encryption support
bcrypt>=4.0.1  # 密码哈希 Password hashing

# ===== 测试 =====
# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.1
pytest-mock>=3.11.1

# ===== Web框架支持 =====
# Web Framework Support
fastapi>=0.103.0
uvicorn>=0.23.0
starlette>=0.27.0

# ===== 开发工具 =====
# Development Tools
black>=23.7.0  # 代码格式化 Code formatting
isort>=5.12.0  # 导入排序 Import sorting
flake8>=6.0.0  # 代码检查 Code linting
mypy>=1.5.0    # 类型检查 Type checking
