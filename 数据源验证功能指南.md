# 🔐 数据源验证功能指南

## 📋 功能概述

新建数据源现已增强为完整的验证流程，确保数据源配置正确且连接成功后才能创建。

### ✅ 验证功能特性

| 功能 | 描述 | 状态 |
|------|------|------|
| 必填字段验证 | 实时验证必填字段完整性 | ✅ |
| 格式验证 | 端口号、主机地址等格式检查 | ✅ |
| 连接测试 | 真实数据库连接验证 | ✅ |
| 智能按钮状态 | 根据验证状态动态启用/禁用按钮 | ✅ |
| 错误提示 | 详细的错误信息和修复建议 | ✅ |
| 表单重置 | 一键重置所有表单内容 | ✅ |

## 🔧 使用流程

### 第一步：填写基本信息
1. **数据源名称** ⭐ (必填)
   - 输入有意义的数据源名称
   - 例如：`生产环境MySQL`、`测试Redis缓存`

2. **描述信息** (可选)
   - 详细描述数据源用途
   - 例如：`用户数据主库`、`会话缓存服务器`

3. **数据库类型** ⭐ (必填)
   - 选择对应的数据库类型
   - 系统会自动加载对应的连接配置

### 第二步：配置连接参数

#### SQLite 配置 (推荐测试)
- **数据库路径** ⭐: `C:\Users\<USER>\test\test\test_database.db`

#### PostgreSQL 配置
- **主机地址** ⭐: `localhost`
- **端口** ⭐: `5432`
- **数据库** ⭐: `postgres`
- **用户名** ⭐: `postgres`
- **密码** ⭐: `your_password`

#### MySQL 配置
- **主机地址** ⭐: `localhost`
- **端口** ⭐: `3306`
- **数据库** ⭐: `mysql`
- **用户名** ⭐: `root`
- **密码** ⭐: `your_password`

#### SQL Server 配置
- **主机地址** ⭐: `localhost`
- **端口** ⭐: `1433`
- **数据库** ⭐: `master`
- **用户名** ⭐: `sa`
- **密码** ⭐: `Password123!`

#### MongoDB 配置
- **主机地址** ⭐: `localhost`
- **端口** ⭐: `27017`
- **数据库** ⭐: `test`
- **用户名**: `admin` (可选)
- **密码**: `password` (可选)

#### Redis 配置
- **主机地址** ⭐: `localhost`
- **端口** ⭐: `6379`
- **密码**: `your_password` (可选)

### 第三步：验证连接

1. **填写完必填字段后**
   - 🔍 测试连接按钮自动启用
   - 系统实时验证字段格式

2. **点击"🔍 测试连接"**
   - 系统执行真实的数据库连接测试
   - 显示详细的连接结果和服务器信息

3. **连接成功后**
   - ✅ 创建数据源按钮启用
   - 显示数据库版本和特性信息

### 第四步：创建数据源

1. **连接测试通过后**
   - 点击"✅ 创建数据源"按钮
   - 系统创建数据源并显示成功信息

2. **创建成功后**
   - 表单自动重置
   - 可以继续创建新的数据源

## 🎯 验证规则

### 必填字段验证
- ⭐ 标记的字段必须填写
- 空值会显示红色边框和错误提示
- 必须通过验证才能进行连接测试

### 格式验证
- **端口号**: 1-65535之间的数字
- **主机地址**: 有效的主机名或IP地址
- **邮箱**: 标准邮箱格式（如果适用）

### 连接验证
- **真实连接**: 实际连接到数据库服务器
- **版本检查**: 获取数据库版本信息
- **权限验证**: 确认用户有足够权限

## 🚨 常见问题

### 连接失败
1. **检查服务状态**
   ```
   ❌ 连接被拒绝
   → 确保数据库服务正在运行
   ```

2. **验证网络连接**
   ```
   ❌ 连接超时
   → 检查网络连接和防火墙设置
   ```

3. **确认认证信息**
   ```
   ❌ 认证失败
   → 验证用户名和密码是否正确
   ```

### 字段验证失败
1. **端口号错误**
   ```
   ❌ 端口号必须在1-65535之间
   → 输入有效的端口号
   ```

2. **主机地址格式**
   ```
   ❌ 请输入有效的主机地址
   → 使用localhost、IP地址或域名
   ```

### 按钮状态问题
1. **测试连接按钮禁用**
   - 检查是否选择了数据库类型
   - 确认必填字段已填写

2. **创建按钮禁用**
   - 必须先通过连接测试
   - 确认所有必填字段已验证

## 🎨 界面提示

### 字段状态
- **🔴 红色边框**: 验证失败，需要修正
- **🟢 绿色边框**: 验证通过
- **⚪ 默认边框**: 未验证或可选字段

### 按钮状态
- **🔍 测试连接**: 灰色禁用 → 蓝色可用 → 绿色成功/红色失败
- **✅ 创建数据源**: 灰色禁用 → 绿色可用
- **🔄 重置表单**: 始终可用

### 提示信息
- **ℹ️ 蓝色**: 信息提示
- **✅ 绿色**: 成功状态
- **❌ 红色**: 错误状态
- **⚠️ 黄色**: 警告信息

## 🚀 最佳实践

### 测试建议
1. **从SQLite开始**: 使用已准备的测试数据库
2. **逐步测试**: 先测试本地数据库，再测试远程
3. **验证权限**: 确保数据库用户有足够权限

### 安全建议
1. **密码安全**: 不要使用弱密码
2. **网络安全**: 生产环境使用SSL连接
3. **权限最小化**: 只授予必要的数据库权限

### 性能建议
1. **连接池**: 生产环境配置连接池
2. **超时设置**: 合理设置连接超时时间
3. **监控**: 定期检查数据源连接状态

---

**开始验证吧！** 🎯

现在您可以安全、可靠地创建数据源，系统会确保每个数据源都经过完整验证！
