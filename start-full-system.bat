@echo off
echo Starting Full BI Platform
echo ============================

echo Step 1: Starting backend server...
start "Backend Server" cmd /k "upgrade-backend.bat"

echo Step 2: Waiting for backend to initialize...
timeout /t 10 /nobreak >nul

echo Step 3: Starting frontend server...
start "Frontend Server" cmd /k "start-frontend.bat"

echo.
echo ============================
echo Full BI Platform is starting!
echo ============================
echo.
echo Services will be available at:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:8000
echo - API Docs: http://localhost:8000/api/v1/docs
echo.
echo Default login:
echo - Email: <EMAIL>
echo - Password: admin123
echo.
echo Two windows will open:
echo 1. Backend Server (FastAPI)
echo 2. Frontend Server (React)
echo.
echo Press any key to close this window...
pause >nul
