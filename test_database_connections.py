#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

def test_database_connections():
    print("🔍 测试数据库连接...")
    
    # SQLite测试
    try:
        import sqlite3
        conn = sqlite3.connect(':memory:')
        conn.close()
        print("✅ SQLite: 可用")
    except Exception as e:
        print(f"❌ SQLite: {e}")
    
    # PostgreSQL测试
    try:
        import psycopg2
        print("✅ PostgreSQL驱动: 已安装")
    except ImportError:
        print("❌ PostgreSQL驱动: 未安装")
    
    # MySQL测试
    try:
        import pymysql
        print("✅ MySQL驱动: 已安装")
    except ImportError:
        print("❌ MySQL驱动: 未安装")
    
    # MongoDB测试
    try:
        import pymongo
        print("✅ MongoDB驱动: 已安装")
    except ImportError:
        print("❌ MongoDB驱动: 未安装")
    
    # Redis测试
    try:
        import redis
        print("✅ Redis驱动: 已安装")
    except ImportError:
        print("❌ Redis驱动: 未安装")

if __name__ == "__main__":
    test_database_connections()
