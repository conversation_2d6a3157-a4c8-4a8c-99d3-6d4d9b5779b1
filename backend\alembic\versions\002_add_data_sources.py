"""Add data sources table

Revision ID: 002
Revises: 001
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    """Create data_sources table"""
    op.create_table(
        'data_sources',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False, comment='数据源名称'),
        sa.Column('description', sa.Text(), nullable=True, comment='数据源描述'),
        sa.Column('type', sa.Enum(
            'postgresql', 'mysql', 'mongodb', 'redis', 'sqlite', 'oracle', 'sqlserver', 'csv', 'excel', 'api',
            name='datasourcetype'
        ), nullable=False, comment='数据源类型'),
        sa.Column('status', sa.Enum(
            'connected', 'disconnected', 'error', 'testing',
            name='datasourcestatus'
        ), nullable=True, comment='连接状态'),
        sa.Column('host', sa.String(length=255), nullable=True, comment='主机地址'),
        sa.Column('port', sa.Integer(), nullable=True, comment='端口号'),
        sa.Column('database', sa.String(length=100), nullable=True, comment='数据库名'),
        sa.Column('username', sa.String(length=100), nullable=True, comment='用户名'),
        sa.Column('password', sa.String(length=255), nullable=True, comment='密码（加密存储）'),
        sa.Column('config', sa.JSON(), nullable=True, comment='扩展配置'),
        sa.Column('connection_string', sa.Text(), nullable=True, comment='连接字符串'),
        sa.Column('last_test_time', sa.DateTime(), nullable=True, comment='最后测试时间'),
        sa.Column('last_test_result', sa.Text(), nullable=True, comment='最后测试结果'),
        sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
        sa.Column('created_by', sa.Integer(), nullable=True, comment='创建者ID'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        comment='数据源表'
    )
    
    # 创建索引
    op.create_index('ix_data_sources_id', 'data_sources', ['id'])
    op.create_index('ix_data_sources_name', 'data_sources', ['name'])
    op.create_index('ix_data_sources_type', 'data_sources', ['type'])
    op.create_index('ix_data_sources_status', 'data_sources', ['status'])
    op.create_index('ix_data_sources_created_by', 'data_sources', ['created_by'])
    
    # 添加外键约束（如果users表存在）
    try:
        op.create_foreign_key(
            'fk_data_sources_created_by',
            'data_sources', 'users',
            ['created_by'], ['id'],
            ondelete='SET NULL'
        )
    except:
        # 如果users表不存在，跳过外键约束
        pass


def downgrade():
    """Drop data_sources table"""
    # 删除外键约束
    try:
        op.drop_constraint('fk_data_sources_created_by', 'data_sources', type_='foreignkey')
    except:
        pass
    
    # 删除索引
    op.drop_index('ix_data_sources_created_by', 'data_sources')
    op.drop_index('ix_data_sources_status', 'data_sources')
    op.drop_index('ix_data_sources_type', 'data_sources')
    op.drop_index('ix_data_sources_name', 'data_sources')
    op.drop_index('ix_data_sources_id', 'data_sources')
    
    # 删除表
    op.drop_table('data_sources')
    
    # 删除枚举类型
    op.execute('DROP TYPE IF EXISTS datasourcestatus')
    op.execute('DROP TYPE IF EXISTS datasourcetype')
