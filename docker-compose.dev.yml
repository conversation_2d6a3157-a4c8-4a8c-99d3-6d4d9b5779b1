services:
  # PostgreSQL数据库
  postgres:
    image: postgres:14
    container_name: bi_postgres
    environment:
      POSTGRES_DB: bi_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - bi_network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: bi_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - bi_network
    restart: unless-stopped

  # 后端API服务 - 开发模式（不构建，直接运行）
  backend:
    image: python:3.10-slim
    container_name: bi_backend_dev
    working_dir: /app
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=bi_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - PYTHONPATH=/app
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - bi_network
    restart: unless-stopped
    command: >
      bash -c "
        pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple &&
        pip install fastapi uvicorn sqlalchemy psycopg2-binary redis pydantic python-jose passlib &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "

  # 前端Web服务 - 使用预构建镜像
  frontend:
    image: nginx:alpine
    container_name: bi_frontend_dev
    volumes:
      - ./frontend/public:/usr/share/nginx/html
      - ./frontend/nginx.conf:/etc/nginx/nginx.conf
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - bi_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  bi_network:
    driver: bridge
