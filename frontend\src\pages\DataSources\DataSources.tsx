/**
 * 数据源管理页面组件
 */

import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons'

const { Title } = Typography
const { Option } = Select

interface DataSource {
  id: number
  name: string
  type: string
  status: 'connected' | 'disconnected' | 'error'
  host?: string
  port?: number
  database?: string
  description?: string
  created_at: string
  updated_at: string
}

const DataSources: React.FC = () => {
  const [dataSource, setDataSources] = useState<DataSource[]>([
    {
      id: 1,
      name: '主数据库',
      type: 'PostgreSQL',
      status: 'connected',
      host: 'localhost',
      port: 5432,
      database: 'main_db',
      description: '主要业务数据库',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      name: '用户数据',
      type: 'MySQL',
      status: 'connected',
      host: 'user-db.example.com',
      port: 3306,
      database: 'users',
      description: '用户相关数据',
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
    },
    {
      id: 3,
      name: '日志数据',
      type: 'MongoDB',
      status: 'error',
      host: 'log-db.example.com',
      port: 27017,
      database: 'logs',
      description: '系统日志数据',
      created_at: '2024-01-03T00:00:00Z',
      updated_at: '2024-01-03T00:00:00Z',
    },
  ])

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingDataSource, setEditingDataSource] = useState<DataSource | null>(null)
  const [form] = Form.useForm()

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DataSource) => (
        <Space>
          <DatabaseOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          connected: { color: 'success', icon: <CheckCircleOutlined />, text: '已连接' },
          disconnected: { color: 'warning', icon: <ExclamationCircleOutlined />, text: '未连接' },
          error: { color: 'error', icon: <CloseCircleOutlined />, text: '连接错误' },
        }
        const config = statusConfig[status as keyof typeof statusConfig]
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        )
      },
    },
    {
      title: '主机',
      dataIndex: 'host',
      key: 'host',
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: '数据库',
      dataIndex: 'database',
      key: 'database',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DataSource) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => handleTest(record)}
          >
            测试连接
          </Button>
          <Popconfirm
            title="确定要删除这个数据源吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleAdd = () => {
    setEditingDataSource(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  const handleEdit = (record: DataSource) => {
    setEditingDataSource(record)
    form.setFieldsValue(record)
    setIsModalVisible(true)
  }

  const handleDelete = (id: number) => {
    setDataSources(prev => prev.filter(item => item.id !== id))
    message.success('数据源删除成功')
  }

  const handleTest = async (record: DataSource) => {
    message.loading('正在测试连接...', 0)
    // 模拟测试连接
    setTimeout(() => {
      message.destroy()
      if (Math.random() > 0.3) {
        message.success('连接测试成功')
      } else {
        message.error('连接测试失败')
      }
    }, 2000)
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      
      if (editingDataSource) {
        // 编辑
        setDataSources(prev =>
          prev.map(item =>
            item.id === editingDataSource.id
              ? { ...item, ...values, updated_at: new Date().toISOString() }
              : item
          )
        )
        message.success('数据源更新成功')
      } else {
        // 新增
        const newDataSource: DataSource = {
          id: Date.now(),
          ...values,
          status: 'disconnected',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
        setDataSources(prev => [...prev, newDataSource])
        message.success('数据源创建成功')
      }
      
      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>数据源管理</Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          添加数据源
        </Button>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          pagination={{
            total: dataSource.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingDataSource ? '编辑数据源' : '添加数据源'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'PostgreSQL',
            port: 5432,
          }}
        >
          <Form.Item
            name="name"
            label="数据源名称"
            rules={[{ required: true, message: '请输入数据源名称' }]}
          >
            <Input placeholder="请输入数据源名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="数据源类型"
            rules={[{ required: true, message: '请选择数据源类型' }]}
          >
            <Select placeholder="请选择数据源类型">
              <Option value="PostgreSQL">PostgreSQL</Option>
              <Option value="MySQL">MySQL</Option>
              <Option value="MongoDB">MongoDB</Option>
              <Option value="Redis">Redis</Option>
              <Option value="SQLServer">SQL Server</Option>
              <Option value="Oracle">Oracle</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="host"
            label="主机地址"
            rules={[{ required: true, message: '请输入主机地址' }]}
          >
            <Input placeholder="请输入主机地址" />
          </Form.Item>

          <Form.Item
            name="port"
            label="端口"
            rules={[{ required: true, message: '请输入端口' }]}
          >
            <Input type="number" placeholder="请输入端口" />
          </Form.Item>

          <Form.Item
            name="database"
            label="数据库名"
            rules={[{ required: true, message: '请输入数据库名' }]}
          >
            <Input placeholder="请输入数据库名" />
          </Form.Item>

          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入描述信息" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default DataSources
