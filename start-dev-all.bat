@echo off
echo Starting Full Development Environment
echo =======================================

echo Step 1: Starting database services...
call start-db-only.bat

echo.
echo Step 2: Opening backend development server...
start "Backend Server" cmd /k "cd backend && start-dev.bat"

echo.
echo Step 3: Waiting for backend to initialize...
timeout /t 10 /nobreak >nul

echo.
echo Step 4: Opening frontend development server...
start "Frontend Server" cmd /k "cd frontend && start-dev.bat"

echo.
echo Development environment is starting!
echo.
echo Services will be available at:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:8000
echo - API Docs: http://localhost:8000/api/v1/docs
echo.
echo Default login: <EMAIL> / admin123
echo.
echo Press any key to exit this window...
pause >nul
