#!/usr/bin/env python3
"""
ODBC驱动检测和安装指导工具

检查系统中可用的SQL Server ODBC驱动程序
"""

import sys
import platform
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 70)
    print("🔍 SQL Server ODBC驱动检测工具")
    print("=" * 70)
    print(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print("=" * 70)

def check_pyodbc():
    """检查pyodbc模块"""
    print("\n📦 检查pyodbc模块...")
    try:
        import pyodbc
        version = getattr(pyodbc, 'version', 'Unknown')
        print(f"✅ pyodbc已安装，版本: {version}")
        return True
    except ImportError:
        print("❌ pyodbc未安装")
        print("💡 安装命令: pip install pyodbc")
        return False

def get_available_drivers():
    """获取可用的ODBC驱动"""
    try:
        import pyodbc
        drivers = pyodbc.drivers()
        return drivers
    except Exception as e:
        print(f"❌ 获取驱动列表失败: {e}")
        return []

def check_sql_server_drivers():
    """检查SQL Server相关驱动"""
    print("\n🗄️ 检查SQL Server ODBC驱动...")
    
    drivers = get_available_drivers()
    if not drivers:
        print("❌ 无法获取ODBC驱动列表")
        return False
    
    print(f"📋 系统中共有 {len(drivers)} 个ODBC驱动:")
    for i, driver in enumerate(drivers, 1):
        print(f"   {i}. {driver}")
    
    # SQL Server驱动的优先级列表
    sql_server_drivers = [
        'ODBC Driver 18 for SQL Server',
        'ODBC Driver 17 for SQL Server', 
        'ODBC Driver 13 for SQL Server',
        'ODBC Driver 11 for SQL Server',
        'SQL Server Native Client 11.0',
        'SQL Server Native Client 10.0',
        'SQL Server'
    ]
    
    print(f"\n🔍 检查SQL Server驱动 (按优先级排序):")
    found_drivers = []
    
    for driver in sql_server_drivers:
        if driver in drivers:
            print(f"✅ {driver} - 可用")
            found_drivers.append(driver)
        else:
            print(f"❌ {driver} - 未安装")
    
    if found_drivers:
        print(f"\n🎉 找到 {len(found_drivers)} 个SQL Server驱动")
        print(f"🔧 推荐使用: {found_drivers[0]}")
        return True
    else:
        print(f"\n❌ 未找到任何SQL Server ODBC驱动")
        return False

def provide_installation_guide():
    """提供安装指导"""
    print("\n" + "=" * 70)
    print("📥 ODBC驱动安装指导")
    print("=" * 70)
    
    os_type = platform.system()
    
    if os_type == "Windows":
        print("\n🪟 Windows系统安装指导:")
        print("1. 访问Microsoft官方下载页面:")
        print("   https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server")
        print("\n2. 下载适合您系统的版本:")
        print("   - ODBC Driver 18 for SQL Server (推荐)")
        print("   - ODBC Driver 17 for SQL Server")
        print("\n3. 运行安装程序并按照提示完成安装")
        print("\n4. 重新运行此检测工具验证安装")
        
    elif os_type == "Linux":
        print("\n🐧 Linux系统安装指导:")
        print("1. Ubuntu/Debian系统:")
        print("   curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -")
        print("   curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list")
        print("   apt-get update")
        print("   ACCEPT_EULA=Y apt-get install -y msodbcsql18")
        print("\n2. CentOS/RHEL系统:")
        print("   curl https://packages.microsoft.com/config/rhel/8/prod.repo > /etc/yum.repos.d/mssql-release.repo")
        print("   yum remove unixODBC-utf16 unixODBC-utf16-devel")
        print("   ACCEPT_EULA=Y yum install -y msodbcsql18")
        
    elif os_type == "Darwin":
        print("\n🍎 macOS系统安装指导:")
        print("1. 使用Homebrew安装:")
        print("   brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release")
        print("   brew update")
        print("   HOMEBREW_NO_ENV_FILTERING=1 ACCEPT_EULA=Y brew install msodbcsql18 mssql-tools18")
        
    else:
        print(f"\n❓ 未知操作系统: {os_type}")
        print("请访问Microsoft官方文档获取安装指导")

def test_connection_string():
    """测试连接字符串构建"""
    print("\n🧪 测试连接字符串构建...")
    
    try:
        import pyodbc
        drivers = pyodbc.drivers()
        
        # 查找SQL Server驱动
        sql_server_drivers = [
            'ODBC Driver 18 for SQL Server',
            'ODBC Driver 17 for SQL Server', 
            'ODBC Driver 13 for SQL Server',
            'SQL Server'
        ]
        
        available_driver = None
        for driver in sql_server_drivers:
            if driver in drivers:
                available_driver = driver
                break
        
        if available_driver:
            print(f"✅ 将使用驱动: {available_driver}")
            
            # 构建示例连接字符串
            example_conn_str = f"DRIVER={{{available_driver}}};SERVER=localhost,1433;DATABASE=master;UID=sa;PWD=password;Timeout=10"
            print(f"📝 示例连接字符串:")
            print(f"   {example_conn_str.replace('password', '***')}")
            
        else:
            print("❌ 未找到可用的SQL Server驱动")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def generate_fix_summary():
    """生成修复总结"""
    print("\n" + "=" * 70)
    print("🔧 问题修复总结")
    print("=" * 70)
    
    print("\n📋 原始错误:")
    print("   IM002: 未发现数据源名称并且未指定默认驱动程序")
    
    print("\n🎯 修复方案:")
    print("   1. ✅ 智能ODBC驱动检测 - 自动查找可用驱动")
    print("   2. ✅ 驱动优先级排序 - 使用最新最稳定的驱动")
    print("   3. ✅ 详细错误诊断 - 提供具体的安装指导")
    print("   4. ✅ 连接字符串优化 - 动态构建连接参数")
    
    print("\n🚀 使用建议:")
    print("   1. 安装推荐的ODBC驱动程序")
    print("   2. 重启真实连接测试服务器")
    print("   3. 重新测试SQL Server连接")
    print("   4. 查看详细的连接日志")

def main():
    """主函数"""
    print_header()
    
    # 检查pyodbc
    pyodbc_available = check_pyodbc()
    
    if pyodbc_available:
        # 检查SQL Server驱动
        drivers_available = check_sql_server_drivers()
        
        # 测试连接字符串
        test_connection_string()
        
        if not drivers_available:
            provide_installation_guide()
    else:
        print("\n💡 请先安装pyodbc: pip install pyodbc")
    
    # 生成修复总结
    generate_fix_summary()
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    main()
