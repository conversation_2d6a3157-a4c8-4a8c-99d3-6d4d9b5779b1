/**
 * 用户相关类型定义
 */

import { UserRole } from './auth'

export interface User {
  id: number
  email: string
  username: string
  full_name?: string
  role: UserRole
  is_active: boolean
  is_verified: boolean
  avatar_url?: string
  phone?: string
  department?: string
  position?: string
  language: string
  theme: string
  timezone: string
  email_notifications: boolean
  push_notifications: boolean
  bio?: string
  created_at: string
  updated_at: string
  last_login_at?: string
}

export interface UserUpdateRequest {
  email?: string
  username?: string
  full_name?: string
  phone?: string
  department?: string
  position?: string
  language?: string
  theme?: string
  timezone?: string
  email_notifications?: boolean
  push_notifications?: boolean
  bio?: string
  role?: UserRole
  is_active?: boolean
  avatar_url?: string
}

export interface UserListResponse {
  users: User[]
  total: number
  skip: number
  limit: number
}
