#!/usr/bin/env python3
"""
删除功能测试脚本

专门测试数据源删除功能是否正常工作。
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def test_delete_function():
    """测试删除功能"""
    print("🧪 开始测试删除功能")
    print("=" * 50)
    
    # 1. 登录获取token
    print("1. 登录获取token...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "<EMAIL>",
        "password": "admin123"
    })
    
    if login_response.status_code != 200:
        print("❌ 登录失败")
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 创建测试数据源
    print("\n2. 创建测试数据源...")
    create_data = {
        "name": "删除测试数据源",
        "description": "用于测试删除功能",
        "type": "sqlite",
        "host": "localhost",
        "port": 0,
        "database": ":memory:",
        "username": "test",
        "password": "test",
        "is_active": True
    }
    
    create_response = requests.post(f"{BASE_URL}/data-sources/", json=create_data, headers=headers)
    
    if create_response.status_code != 200:
        print(f"❌ 创建数据源失败: {create_response.text}")
        return False
    
    created_ds = create_response.json()
    ds_id = created_ds["id"]
    print(f"✅ 创建数据源成功，ID: {ds_id}")
    
    # 3. 验证数据源存在
    print(f"\n3. 验证数据源 {ds_id} 存在...")
    list_response = requests.get(f"{BASE_URL}/data-sources/", headers=headers)
    
    if list_response.status_code != 200:
        print("❌ 获取数据源列表失败")
        return False
    
    data_sources = list_response.json()["items"]
    found = any(ds["id"] == ds_id for ds in data_sources)
    
    if not found:
        print(f"❌ 数据源 {ds_id} 不在列表中")
        return False
    
    print(f"✅ 数据源 {ds_id} 存在于列表中")
    print(f"   当前数据源总数: {len(data_sources)}")
    
    # 4. 执行删除
    print(f"\n4. 删除数据源 {ds_id}...")
    delete_response = requests.delete(f"{BASE_URL}/data-sources/{ds_id}", headers=headers)
    
    print(f"   删除请求状态码: {delete_response.status_code}")
    print(f"   删除响应内容: {delete_response.text}")
    
    if delete_response.status_code != 200:
        print(f"❌ 删除请求失败: {delete_response.text}")
        return False
    
    delete_result = delete_response.json()
    print(f"✅ 删除请求成功: {delete_result.get('message', '无消息')}")
    
    # 5. 验证删除结果
    print(f"\n5. 验证数据源 {ds_id} 已被删除...")
    time.sleep(1)  # 等待一秒确保删除完成
    
    list_response2 = requests.get(f"{BASE_URL}/data-sources/", headers=headers)
    
    if list_response2.status_code != 200:
        print("❌ 获取数据源列表失败")
        return False
    
    data_sources2 = list_response2.json()["items"]
    still_exists = any(ds["id"] == ds_id for ds in data_sources2)
    
    if still_exists:
        print(f"❌ 数据源 {ds_id} 仍然存在于列表中")
        print(f"   当前数据源: {[ds['id'] for ds in data_sources2]}")
        return False
    
    print(f"✅ 数据源 {ds_id} 已成功删除")
    print(f"   删除前数据源总数: {len(data_sources)}")
    print(f"   删除后数据源总数: {len(data_sources2)}")
    
    # 6. 尝试删除不存在的数据源
    print(f"\n6. 尝试删除不存在的数据源...")
    fake_id = 99999
    delete_fake_response = requests.delete(f"{BASE_URL}/data-sources/{fake_id}", headers=headers)
    
    if delete_fake_response.status_code == 404:
        print(f"✅ 正确返回404错误（数据源不存在）")
    else:
        print(f"⚠️ 删除不存在数据源的响应异常: {delete_fake_response.status_code}")
    
    print("\n" + "=" * 50)
    print("🎉 删除功能测试完成！")
    return True

def main():
    """主函数"""
    try:
        success = test_delete_function()
        if success:
            print("✅ 所有删除功能测试通过")
        else:
            print("❌ 删除功能测试失败")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保测试服务器正在运行")
        print("   启动命令: python test_data_source_server.py")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
