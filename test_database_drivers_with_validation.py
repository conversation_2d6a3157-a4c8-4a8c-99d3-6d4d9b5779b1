#!/usr/bin/env python3
"""
数据源驱动测试服务器 - 带验证功能

测试各种数据库驱动的连接和查询功能，包含完整的验证流程。
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import threading
import time
import webbrowser

# 全局数据存储
data_sources = []
next_id = 1

class DatabaseDriverTestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_test_page()
        elif self.path == '/api/v1/data-sources/':
            self.handle_get_list()
        elif self.path == '/api/v1/data-sources/types':
            self.handle_get_types()
        elif self.path.startswith('/api/v1/data-sources/types/'):
            self.handle_get_type_requirements()
        elif '/tables' in self.path:
            self.handle_get_tables()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        if self.path == '/api/v1/auth/login':
            self.handle_login()
        elif self.path == '/api/v1/data-sources/':
            self.handle_create()
        elif self.path == '/api/v1/data-sources/test':
            self.handle_test_connection()
        elif self.path == '/api/v1/data-sources/query':
            self.handle_query()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_DELETE(self):
        if self.path.startswith('/api/v1/data-sources/'):
            self.handle_delete()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def handle_login(self):
        self.send_json_response({
            "access_token": "test_token_12345",
            "token_type": "bearer"
        })
    
    def handle_get_types(self):
        """获取支持的数据库类型"""
        types = [
            "postgresql", "mysql", "sqlserver", "mssql", 
            "mongodb", "redis", "sqlite", "oracle"
        ]
        self.send_json_response(types)
    
    def handle_get_type_requirements(self):
        """获取数据库类型要求"""
        path_parts = self.path.split('/')
        db_type = path_parts[4]  # /api/v1/data-sources/types/{type}/requirements
        
        requirements = {
            "postgresql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 5432,
                "supports_ssl": True,
                "driver": "psycopg2-binary",
                "features": ["ACID事务", "JSON支持", "全文搜索", "并行查询"]
            },
            "mysql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 3306,
                "supports_ssl": True,
                "driver": "pymysql",
                "features": ["InnoDB引擎", "分区表", "JSON支持", "窗口函数"]
            },
            "mssql": {
                "required_fields": ["host", "port", "database", "username", "password"],
                "default_port": 1433,
                "supports_ssl": True,
                "driver": "pyodbc + ODBC Driver 17",
                "features": ["T-SQL", "列存储索引", "内存优化表", "Always Encrypted"]
            },
            "mongodb": {
                "required_fields": ["host", "port", "database"],
                "optional_fields": ["username", "password"],
                "default_port": 27017,
                "supports_ssl": True,
                "driver": "pymongo",
                "features": ["文档存储", "聚合管道", "分片", "副本集"]
            },
            "redis": {
                "required_fields": ["host", "port"],
                "optional_fields": ["password"],
                "default_port": 6379,
                "supports_ssl": False,
                "driver": "redis-py",
                "features": ["键值存储", "发布订阅", "Lua脚本", "集群模式"]
            },
            "sqlite": {
                "required_fields": ["database"],
                "default_port": 0,
                "supports_ssl": False,
                "driver": "内置sqlite3模块",
                "features": ["嵌入式数据库", "ACID事务", "全文搜索", "JSON支持"]
            }
        }
        
        self.send_json_response(requirements.get(db_type, {}))
    
    def handle_create(self):
        global data_sources, next_id
        
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))
        
        new_ds = {
            "id": next_id,
            "name": data["name"],
            "description": data.get("description", ""),
            "type": data["type"],
            "host": data.get("host", ""),
            "port": data.get("port", 0),
            "database": data.get("database", ""),
            "username": data.get("username", ""),
            "status": "connected",  # 由于已通过连接测试，设为已连接
            "created_at": datetime.now().isoformat(),
            "tables": []
        }
        
        # 根据数据库类型添加模拟表
        if data["type"] in ["postgresql", "mysql", "mssql"]:
            new_ds["tables"] = ["users", "orders", "products", "categories", "inventory"]
        elif data["type"] == "mongodb":
            new_ds["tables"] = ["users", "orders", "products", "logs", "sessions"]
        elif data["type"] == "redis":
            new_ds["tables"] = ["user:*", "session:*", "cache:*", "queue:*"]
        elif data["type"] == "sqlite":
            new_ds["tables"] = ["users", "orders", "products", "categories", "logs"]
        
        data_sources.append(new_ds)
        next_id += 1
        
        print(f"✅ 创建数据源: {new_ds['name']} (ID: {new_ds['id']}, 类型: {new_ds['type']})")
        
        self.send_json_response(new_ds)
    
    def handle_get_list(self):
        global data_sources
        
        self.send_json_response({
            "items": data_sources,
            "total": len(data_sources)
        })
    
    def handle_test_connection(self):
        """测试数据库连接"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))
        
        db_type = data["type"]
        
        # 模拟连接测试，但更真实
        if db_type == "sqlite":
            # SQLite特殊处理
            database_path = data.get("database", "")
            if database_path and ("test_database.db" in database_path or ":memory:" in database_path):
                result = {
                    "success": True,
                    "message": "SQLite连接成功",
                    "details": {
                        "version": "SQLite 3.39.0",
                        "driver": "内置sqlite3模块",
                        "database": database_path,
                        "features": ["嵌入式数据库", "ACID事务", "全文搜索", "JSON支持"],
                        "tables_found": 5,
                        "file_size": "2.5MB"
                    }
                }
            else:
                result = {
                    "success": False,
                    "message": "SQLite数据库文件不存在或路径无效"
                }
        else:
            # 其他数据库的模拟测试
            result = self._simulate_connection_test(db_type, data)
        
        result["test_time"] = datetime.now().isoformat()
        self.send_json_response(result)

    def _simulate_connection_test(self, db_type, data):
        """模拟连接测试"""
        if db_type == "postgresql":
            return {
                "success": True,
                "message": "PostgreSQL连接成功",
                "details": {
                    "version": "PostgreSQL 15.0",
                    "driver": "psycopg2-binary",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["ACID事务", "JSON支持", "全文搜索", "并行查询"]
                }
            }
        elif db_type == "mysql":
            return {
                "success": True,
                "message": "MySQL连接成功",
                "details": {
                    "version": "MySQL 8.0.35",
                    "driver": "pymysql",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["InnoDB引擎", "分区表", "JSON支持", "窗口函数"]
                }
            }
        elif db_type == "mssql":
            return {
                "success": True,
                "message": "SQL Server连接成功",
                "details": {
                    "version": "Microsoft SQL Server 2022",
                    "driver": "pyodbc + ODBC Driver 17",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["T-SQL", "列存储索引", "内存优化表"]
                }
            }
        elif db_type == "mongodb":
            return {
                "success": True,
                "message": "MongoDB连接成功",
                "details": {
                    "version": "MongoDB 7.0",
                    "driver": "pymongo",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["文档存储", "聚合管道", "分片", "副本集"]
                }
            }
        elif db_type == "redis":
            return {
                "success": True,
                "message": "Redis连接成功",
                "details": {
                    "version": "Redis 7.2",
                    "driver": "redis-py",
                    "host": data.get("host"),
                    "port": data.get("port"),
                    "features": ["键值存储", "发布订阅", "Lua脚本"]
                }
            }
        else:
            return {
                "success": False,
                "message": f"暂不支持 {db_type} 类型的连接测试"
            }

    def handle_get_tables(self):
        """获取数据源表列表"""
        path_parts = self.path.split('/')
        ds_id = int(path_parts[4])

        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({"detail": "数据源不存在"}, status=404)
            return

        self.send_json_response({
            "data_source_id": ds_id,
            "data_source_name": ds["name"],
            "tables": ds["tables"],
            "total": len(ds["tables"])
        })

    def handle_query(self):
        """执行查询"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))

        ds_id = data["data_source_id"]
        query = data["query"]

        ds = next((ds for ds in data_sources if ds["id"] == ds_id), None)
        if not ds:
            self.send_json_response({
                "success": False,
                "message": "数据源不存在"
            })
            return

        # 模拟查询结果
        mock_data = [
            {"id": 1, "name": "张三", "email": "<EMAIL>", "created_at": "2024-01-01"},
            {"id": 2, "name": "李四", "email": "<EMAIL>", "created_at": "2024-01-02"},
            {"id": 3, "name": "王五", "email": "<EMAIL>", "created_at": "2024-01-03"}
        ]

        self.send_json_response({
            "success": True,
            "data": mock_data,
            "columns": ["id", "name", "email", "created_at"],
            "total": len(mock_data),
            "execution_time": 0.025,
            "message": f"在 {ds['type']} 数据源上执行查询成功"
        })

    def handle_delete(self):
        global data_sources

        ds_id = int(self.path.split('/')[-1])
        original_count = len(data_sources)
        data_sources = [ds for ds in data_sources if ds["id"] != ds_id]

        if len(data_sources) < original_count:
            self.send_json_response({"message": f"数据源 ID {ds_id} 删除成功"})
        else:
            self.send_json_response({"detail": "数据源不存在"}, status=404)

    def send_test_page(self):
        """发送测试页面"""
        html = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>数据源验证测试平台</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    background: #f5f5f5; line-height: 1.6; padding: 20px;
                }
                .container { max-width: 1400px; margin: 0 auto; }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px;
                    text-align: center;
                }
                .card {
                    background: white; border-radius: 12px; padding: 25px; margin: 20px 0;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1); transition: transform 0.3s ease;
                }
                .card:hover { transform: translateY(-2px); }
                .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px; }
                .btn {
                    background: #667eea; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; margin: 8px; font-size: 14px;
                    transition: all 0.3s ease;
                }
                .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
                .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
                .btn-danger { background: #ff4757; }
                .btn-danger:hover { background: #ff3742; }
                .btn-success { background: #2ed573; }
                .btn-success:hover { background: #26d467; }
                .btn-warning { background: #ffa502; }
                .btn-warning:hover { background: #ff9500; }
                .form-group { margin: 15px 0; }
                .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; }
                .form-group input, .form-group select, .form-group textarea {
                    width: 100%; padding: 12px; border: 2px solid #e1e8ed; border-radius: 8px;
                    font-size: 14px; transition: border-color 0.3s ease;
                }
                .form-group input:focus, .form-group select:focus {
                    border-color: #667eea; outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
                .form-group input.error, .form-group select.error {
                    border-color: #ff4757; box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
                }
                .form-group input.success, .form-group select.success {
                    border-color: #2ed573; box-shadow: 0 0 0 3px rgba(46, 213, 115, 0.1);
                }
                .field-error { color: #ff4757; font-size: 12px; margin-top: 5px; }
                .field-success { color: #2ed573; font-size: 12px; margin-top: 5px; }
                .required { color: #ff4757; }
                .result {
                    background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;
                    font-family: 'Courier New', monospace; font-size: 13px; max-height: 400px; overflow-y: auto;
                    border-left: 4px solid #667eea;
                }
                .success { color: #2ed573; font-weight: 600; }
                .error { color: #ff4757; font-weight: 600; }
                .warning { color: #ffa502; font-weight: 600; }
                .info { color: #3742fa; font-weight: 600; }
                .data-source-item {
                    border: 2px solid #e1e8ed; padding: 20px; margin: 15px 0; border-radius: 12px;
                    transition: all 0.3s ease;
                }
                .data-source-item:hover { border-color: #667eea; }
                .status-connected { color: #2ed573; }
                .status-disconnected { color: #ff4757; }
                .feature-tag {
                    display: inline-block; background: #667eea; color: white; padding: 4px 8px;
                    border-radius: 4px; font-size: 12px; margin: 2px;
                }
                .driver-info {
                    background: #e8f4fd; padding: 15px; border-radius: 8px; margin: 10px 0;
                    border-left: 4px solid #3742fa;
                }
                .tabs { display: flex; border-bottom: 2px solid #e1e8ed; margin-bottom: 20px; }
                .tab {
                    padding: 12px 24px; cursor: pointer; border-bottom: 2px solid transparent;
                    transition: all 0.3s ease;
                }
                .tab.active { border-bottom-color: #667eea; color: #667eea; font-weight: 600; }
                .tab-content { display: none; }
                .tab-content.active { display: block; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 数据源验证测试平台</h1>
                    <p>完整的连接验证流程，确保数据源配置正确</p>
                    <div style="margin-top: 15px;">
                        <span class="feature-tag">必填验证</span>
                        <span class="feature-tag">连接测试</span>
                        <span class="feature-tag">智能按钮</span>
                        <span class="feature-tag">错误提示</span>
                    </div>
                </div>

                <div class="tabs">
                    <div class="tab active" onclick="switchTab('create')">创建数据源</div>
                    <div class="tab" onclick="switchTab('list')">数据源列表</div>
                    <div class="tab" onclick="switchTab('test')">连接测试</div>
                </div>

                <!-- 创建数据源 -->
                <div id="create-tab" class="tab-content active">
                    <div class="grid">
                        <div class="card">
                            <h3>📝 基本信息</h3>
                            <div class="form-group">
                                <label>数据源名称 <span class="required">*</span>:</label>
                                <input type="text" id="dsName" placeholder="例如: 生产环境MySQL"
                                       onchange="updateButtonStates()" onblur="updateButtonStates()" />
                                <div id="dsNameError" class="field-error" style="display: none;"></div>
                            </div>
                            <div class="form-group">
                                <label>描述:</label>
                                <textarea id="dsDescription" placeholder="数据源用途描述"></textarea>
                            </div>
                            <div class="form-group">
                                <label>数据库类型 <span class="required">*</span>:</label>
                                <select id="dsType" onchange="updateConnectionForm()">
                                    <option value="">请选择数据库类型</option>
                                    <option value="sqlite">SQLite (推荐测试)</option>
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="mysql">MySQL</option>
                                    <option value="mssql">SQL Server</option>
                                    <option value="mongodb">MongoDB</option>
                                    <option value="redis">Redis</option>
                                </select>
                            </div>
                        </div>

                        <div class="card">
                            <h3>🔗 连接配置</h3>
                            <div id="connectionForm">
                                <p class="info">请先选择数据库类型</p>
                            </div>
                            <div style="margin-top: 20px;">
                                <button class="btn" onclick="testCreateConnection()" id="testConnBtn" disabled>
                                    🔍 测试连接
                                </button>
                                <button class="btn btn-success" onclick="createDataSource()" id="createBtn" disabled>
                                    ✅ 创建数据源
                                </button>
                                <button class="btn btn-warning" onclick="resetCreateForm()" id="resetBtn">
                                    🔄 重置表单
                                </button>
                            </div>
                            <div id="connectionTestResult" class="result" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>📋 操作日志</h3>
                        <div id="createResult" class="result"></div>
                    </div>
                </div>

                <!-- 数据源列表 -->
                <div id="list-tab" class="tab-content">
                    <div class="card">
                        <h3>📊 数据源管理</h3>
                        <button class="btn" onclick="loadDataSources()">刷新列表</button>
                        <div id="dataSourceList"></div>
                    </div>
                </div>

                <!-- 连接测试 -->
                <div id="test-tab" class="tab-content">
                    <div class="card">
                        <h3>🔗 独立连接测试</h3>
                        <div class="grid">
                            <div>
                                <div class="form-group">
                                    <label>测试类型:</label>
                                    <select id="testType" onchange="updateTestForm()">
                                        <option value="sqlite">SQLite</option>
                                        <option value="postgresql">PostgreSQL</option>
                                        <option value="mysql">MySQL</option>
                                        <option value="mssql">SQL Server</option>
                                        <option value="mongodb">MongoDB</option>
                                        <option value="redis">Redis</option>
                                    </select>
                                </div>
                                <div id="testForm"></div>
                                <button class="btn" onclick="testConnection()">测试连接</button>
                            </div>
                            <div>
                                <h4>连接结果</h4>
                                <div id="testResult" class="result"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                let token = null;

                // 自动登录
                async function autoLogin() {
                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ username: 'admin', password: 'admin' })
                        });
                        const data = await response.json();
                        token = data.access_token;
                        log('✅ 自动登录成功', 'createResult');
                    } catch (error) {
                        log('❌ 登录失败: ' + error.message, 'createResult');
                    }
                }

                // 更新连接表单
                async function updateConnectionForm() {
                    const type = document.getElementById('dsType').value;
                    const formDiv = document.getElementById('connectionForm');

                    if (!type) {
                        formDiv.innerHTML = '<p class="info">请先选择数据库类型</p>';
                        updateButtonStates();
                        return;
                    }

                    try {
                        const response = await fetch(`/api/v1/data-sources/types/${type}/requirements`, {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });
                        const requirements = await response.json();

                        let html = '<div class="driver-info">';
                        html += `<strong>驱动:</strong> ${requirements.driver}<br>`;
                        html += `<strong>默认端口:</strong> ${requirements.default_port}<br>`;
                        html += `<strong>SSL支持:</strong> ${requirements.supports_ssl ? '是' : '否'}<br>`;
                        if (requirements.features) {
                            html += `<strong>特性:</strong> ${requirements.features.join(', ')}`;
                        }
                        html += '</div>';

                        // 必需字段
                        requirements.required_fields.forEach(field => {
                            const label = getFieldLabel(field);
                            const defaultValue = getDefaultValue(field, type);
                            const inputType = field === 'password' ? 'password' : (field === 'port' ? 'number' : 'text');

                            html += `
                                <div class="form-group">
                                    <label>${label} <span class="required">*</span>:</label>
                                    <input type="${inputType}" id="ds${capitalize(field)}" value="${defaultValue}"
                                           onchange="validateField('ds${capitalize(field)}', true)"
                                           onblur="validateField('ds${capitalize(field)}', true)"
                                           required />
                                    <div id="ds${capitalize(field)}Error" class="field-error" style="display: none;"></div>
                                </div>
                            `;
                        });

                        // 可选字段
                        if (requirements.optional_fields) {
                            requirements.optional_fields.forEach(field => {
                                const label = getFieldLabel(field);
                                const inputType = field === 'password' ? 'password' : (field === 'port' ? 'number' : 'text');

                                html += `
                                    <div class="form-group">
                                        <label>${label}:</label>
                                        <input type="${inputType}" id="ds${capitalize(field)}"
                                               onchange="validateField('ds${capitalize(field)}', false)"
                                               onblur="validateField('ds${capitalize(field)}', false)" />
                                        <div id="ds${capitalize(field)}Error" class="field-error" style="display: none;"></div>
                                    </div>
                                `;
                            });
                        }

                        formDiv.innerHTML = html;
                        updateButtonStates();
                    } catch (error) {
                        formDiv.innerHTML = '<p class="error">加载连接要求失败</p>';
                    }
                }

                // 获取字段标签
                function getFieldLabel(field) {
                    const labels = {
                        'host': '主机地址',
                        'port': '端口',
                        'database': '数据库',
                        'username': '用户名',
                        'password': '密码'
                    };
                    return labels[field] || field;
                }

                // 获取默认值
                function getDefaultValue(field, type) {
                    if (field === 'host') return 'localhost';
                    if (field === 'port') {
                        const ports = {
                            'postgresql': 5432,
                            'mysql': 3306,
                            'mssql': 1433,
                            'mongodb': 27017,
                            'redis': 6379,
                            'sqlite': 0
                        };
                        return ports[type] || '';
                    }
                    if (field === 'database') {
                        if (type === 'sqlite') return 'C:\\\\Users\\\\<USER>\\\\test\\\\test\\\\test_database.db';
                        if (type === 'postgresql') return 'postgres';
                        if (type === 'mysql') return 'mysql';
                        if (type === 'mssql') return 'master';
                        return 'test';
                    }
                    if (field === 'username') {
                        if (type === 'postgresql') return 'postgres';
                        if (type === 'mysql') return 'root';
                        if (type === 'mssql') return 'sa';
                        return 'admin';
                    }
                    return '';
                }

                // 首字母大写
                function capitalize(str) {
                    return str.charAt(0).toUpperCase() + str.slice(1);
                }

                // 验证字段
                function validateField(fieldId, isRequired) {
                    const field = document.getElementById(fieldId);
                    const errorDiv = document.getElementById(fieldId + 'Error');

                    if (!field || !errorDiv) return true;

                    const value = field.value.trim();
                    let isValid = true;
                    let errorMessage = '';

                    // 必填字段验证
                    if (isRequired && !value) {
                        isValid = false;
                        errorMessage = '此字段为必填项';
                    }

                    // 端口号验证
                    if (fieldId.toLowerCase().includes('port') && value) {
                        const port = parseInt(value);
                        if (isNaN(port) || port < 1 || port > 65535) {
                            isValid = false;
                            errorMessage = '端口号必须在1-65535之间';
                        }
                    }

                    // 更新UI状态
                    if (isValid) {
                        field.classList.remove('error');
                        field.classList.add('success');
                        errorDiv.style.display = 'none';
                    } else {
                        field.classList.remove('success');
                        field.classList.add('error');
                        errorDiv.textContent = errorMessage;
                        errorDiv.style.display = 'block';
                    }

                    // 更新按钮状态
                    updateButtonStates();

                    return isValid;
                }

                // 更新按钮状态
                function updateButtonStates() {
                    const testBtn = document.getElementById('testConnBtn');
                    const createBtn = document.getElementById('createBtn');

                    if (!testBtn || !createBtn) return;

                    const type = document.getElementById('dsType').value;
                    const name = document.getElementById('dsName').value.trim();

                    // 测试连接按钮：需要选择类型、填写名称和基本连接信息
                    if (type && name && hasMinimumConnectionInfo()) {
                        testBtn.disabled = false;
                        testBtn.textContent = '🔍 测试连接';
                    } else {
                        testBtn.disabled = true;
                        testBtn.textContent = '🔍 测试连接';
                    }

                    // 创建按钮：需要通过连接测试
                    const connectionTested = testBtn.dataset.tested === 'true';
                    if (type && name && connectionTested) {
                        createBtn.disabled = false;
                        createBtn.textContent = '✅ 创建数据源';
                    } else {
                        createBtn.disabled = true;
                        createBtn.textContent = connectionTested ? '⏳ 请完善信息' : '⏳ 请先测试连接';
                    }
                }

                // 检查是否有最基本的连接信息
                function hasMinimumConnectionInfo() {
                    const type = document.getElementById('dsType').value;
                    if (!type) return false;

                    if (type === 'sqlite') {
                        const database = document.getElementById('dsDatabase');
                        return database && database.value.trim();
                    } else {
                        const host = document.getElementById('dsHost');
                        const port = document.getElementById('dsPort');
                        return host && host.value.trim() && port && port.value.trim();
                    }
                }

                // 测试创建连接
                async function testCreateConnection() {
                    const type = document.getElementById('dsType').value;
                    const testBtn = document.getElementById('testConnBtn');
                    const resultDiv = document.getElementById('connectionTestResult');

                    if (!type) {
                        log('❌ 请先选择数据库类型', 'createResult');
                        return;
                    }

                    // 收集连接参数
                    const data = { type };
                    const fields = ['host', 'port', 'database', 'username', 'password'];
                    fields.forEach(field => {
                        const element = document.getElementById(`ds${capitalize(field)}`);
                        if (element && element.value) {
                            data[field] = field === 'port' ? parseInt(element.value) : element.value;
                        }
                    });

                    // 更新按钮状态
                    testBtn.disabled = true;
                    testBtn.textContent = '🔄 正在测试...';
                    resultDiv.style.display = 'block';
                    resultDiv.innerHTML = '<span class="info">🔄 正在连接数据库，请稍候...</span>';

                    try {
                        const response = await fetch('/api/v1/data-sources/test', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();

                        if (result.success) {
                            testBtn.dataset.tested = 'true';
                            testBtn.textContent = '✅ 连接成功';
                            testBtn.className = 'btn btn-success';

                            let html = `<span class="success">✅ ${result.message}</span><br>`;
                            if (result.details) {
                                html += '<div style="margin-top: 10px;"><strong>连接详情:</strong><br>';
                                Object.keys(result.details).forEach(key => {
                                    if (key !== 'password') {
                                        html += `<span class="info">${key}:</span> ${result.details[key]}<br>`;
                                    }
                                });
                                html += '</div>';
                            }
                            resultDiv.innerHTML = html;

                            log(`✅ 连接测试成功: ${result.message}`, 'createResult');
                        } else {
                            testBtn.dataset.tested = 'false';
                            testBtn.textContent = '❌ 连接失败';
                            testBtn.className = 'btn btn-danger';

                            resultDiv.innerHTML = `<span class="error">❌ ${result.message}</span>`;
                            log(`❌ 连接测试失败: ${result.message}`, 'createResult');
                        }
                    } catch (error) {
                        testBtn.dataset.tested = 'false';
                        testBtn.textContent = '❌ 测试失败';
                        testBtn.className = 'btn btn-danger';

                        resultDiv.innerHTML = `<span class="error">❌ 请求失败: ${error.message}</span>`;
                        log(`❌ 连接测试异常: ${error.message}`, 'createResult');
                    } finally {
                        testBtn.disabled = false;
                        updateButtonStates();
                    }
                }

                // 创建数据源
                async function createDataSource() {
                    const name = document.getElementById('dsName').value;
                    const description = document.getElementById('dsDescription').value;
                    const type = document.getElementById('dsType').value;

                    if (!name || !type) {
                        log('❌ 请填写数据源名称和类型', 'createResult');
                        return;
                    }

                    // 检查是否已通过连接测试
                    const testBtn = document.getElementById('testConnBtn');
                    if (testBtn.dataset.tested !== 'true') {
                        log('❌ 请先通过连接测试', 'createResult');
                        return;
                    }

                    // 收集连接参数
                    const data = { name, description, type };
                    const fields = ['host', 'port', 'database', 'username', 'password'];
                    fields.forEach(field => {
                        const element = document.getElementById(`ds${capitalize(field)}`);
                        if (element && element.value) {
                            data[field] = field === 'port' ? parseInt(element.value) : element.value;
                        }
                    });

                    try {
                        const response = await fetch('/api/v1/data-sources/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();
                        if (response.ok) {
                            log(`✅ 创建成功: ${result.name} (ID: ${result.id}, 类型: ${result.type})`, 'createResult');

                            // 重置表单
                            resetCreateForm();

                            // 显示成功消息
                            const successMsg = document.createElement('div');
                            successMsg.className = 'success';
                            successMsg.style.cssText = 'background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 15px 0;';
                            successMsg.innerHTML = `
                                <strong>🎉 数据源创建成功！</strong><br>
                                名称: ${result.name}<br>
                                类型: ${result.type}<br>
                                状态: ${result.status}<br>
                                <small>您可以在"数据源列表"标签页中查看和管理此数据源</small>
                            `;

                            const createResult = document.getElementById('createResult');
                            createResult.insertBefore(successMsg, createResult.firstChild);

                            // 5秒后自动移除成功消息
                            setTimeout(() => {
                                if (successMsg.parentNode) {
                                    successMsg.parentNode.removeChild(successMsg);
                                }
                            }, 5000);

                        } else {
                            log(`❌ 创建失败: ${result.detail}`, 'createResult');
                        }
                    } catch (error) {
                        log('❌ 创建请求失败: ' + error.message, 'createResult');
                    }
                }

                // 重置创建表单
                function resetCreateForm() {
                    // 清空基本信息
                    document.getElementById('dsName').value = '';
                    document.getElementById('dsDescription').value = '';
                    document.getElementById('dsType').value = '';

                    // 清空连接表单
                    const connectionForm = document.getElementById('connectionForm');
                    connectionForm.innerHTML = '<p class="info">请先选择数据库类型</p>';

                    // 重置按钮状态
                    const testBtn = document.getElementById('testConnBtn');
                    const createBtn = document.getElementById('createBtn');

                    if (testBtn) {
                        testBtn.disabled = true;
                        testBtn.textContent = '🔍 测试连接';
                        testBtn.className = 'btn';
                        testBtn.dataset.tested = 'false';
                    }

                    if (createBtn) {
                        createBtn.disabled = true;
                        createBtn.textContent = '✅ 创建数据源';
                        createBtn.className = 'btn btn-success';
                    }

                    // 隐藏测试结果
                    const resultDiv = document.getElementById('connectionTestResult');
                    if (resultDiv) {
                        resultDiv.style.display = 'none';
                        resultDiv.innerHTML = '';
                    }

                    log('📝 表单已重置，可以创建新的数据源', 'createResult');
                }

                // 切换标签页
                function switchTab(tabName) {
                    // 隐藏所有标签内容
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    document.querySelectorAll('.tab').forEach(tab => {
                        tab.classList.remove('active');
                    });

                    // 显示选中的标签
                    document.getElementById(tabName + '-tab').classList.add('active');
                    event.target.classList.add('active');

                    // 加载对应数据
                    if (tabName === 'list') {
                        loadDataSources();
                    } else if (tabName === 'test') {
                        updateTestForm();
                    }
                }

                // 加载数据源列表
                async function loadDataSources() {
                    try {
                        const response = await fetch('/api/v1/data-sources/', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        const result = await response.json();
                        if (response.ok) {
                            displayDataSources(result.items);
                        } else {
                            log(`❌ 加载失败: ${result.detail}`, 'createResult');
                        }
                    } catch (error) {
                        log('❌ 加载请求失败: ' + error.message, 'createResult');
                    }
                }

                // 显示数据源列表
                function displayDataSources(dataSources) {
                    const container = document.getElementById('dataSourceList');
                    if (dataSources.length === 0) {
                        container.innerHTML = '<p class="info">暂无数据源</p>';
                        return;
                    }

                    let html = '';
                    dataSources.forEach(ds => {
                        const statusClass = `status-${ds.status}`;
                        html += `
                            <div class="data-source-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h4>${ds.name} <span class="feature-tag">${ds.type}</span></h4>
                                        <p>${ds.description || '无描述'}</p>
                                        <small>创建时间: ${new Date(ds.created_at).toLocaleString()}</small>
                                    </div>
                                    <div>
                                        <span class="${statusClass}">● ${ds.status}</span><br>
                                        <button class="btn btn-danger" onclick="deleteDataSource(${ds.id})">删除</button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    container.innerHTML = html;
                }

                // 删除数据源
                async function deleteDataSource(id) {
                    if (!confirm(`确定要删除数据源 ID: ${id} 吗？`)) return;

                    try {
                        const response = await fetch(`/api/v1/data-sources/${id}`, {
                            method: 'DELETE',
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        const result = await response.json();
                        if (response.ok) {
                            log(`✅ ${result.message}`, 'createResult');
                            loadDataSources();
                        } else {
                            log(`❌ 删除失败: ${result.detail}`, 'createResult');
                        }
                    } catch (error) {
                        log(`❌ 删除请求失败: ${error.message}`, 'createResult');
                    }
                }

                // 更新测试表单
                function updateTestForm() {
                    const type = document.getElementById('testType').value;
                    const formDiv = document.getElementById('testForm');

                    const configs = {
                        'postgresql': { host: 'localhost', port: 5432, database: 'postgres', username: 'postgres', password: 'password' },
                        'mysql': { host: 'localhost', port: 3306, database: 'mysql', username: 'root', password: 'password' },
                        'mssql': { host: 'localhost', port: 1433, database: 'master', username: 'sa', password: 'Password123!' },
                        'mongodb': { host: 'localhost', port: 27017, database: 'test', username: 'admin', password: 'password' },
                        'redis': { host: 'localhost', port: 6379, password: 'password' },
                        'sqlite': { database: 'C:\\\\Users\\\\<USER>\\\\test\\\\test\\\\test_database.db' }
                    };

                    const config = configs[type];
                    let html = '';

                    Object.keys(config).forEach(key => {
                        const inputType = key === 'password' ? 'password' : (key === 'port' ? 'number' : 'text');
                        html += `
                            <div class="form-group">
                                <label>${getFieldLabel(key)}:</label>
                                <input type="${inputType}" id="test${capitalize(key)}" value="${config[key]}" />
                            </div>
                        `;
                    });

                    formDiv.innerHTML = html;
                }

                // 测试连接
                async function testConnection() {
                    const type = document.getElementById('testType').value;

                    // 收集测试数据
                    const data = { type };
                    const fields = ['host', 'port', 'database', 'username', 'password'];

                    fields.forEach(field => {
                        const element = document.getElementById(`test${capitalize(field)}`);
                        if (element && element.value) {
                            data[field] = field === 'port' ? parseInt(element.value) : element.value;
                        }
                    });

                    try {
                        const response = await fetch('/api/v1/data-sources/test', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();
                        const resultDiv = document.getElementById('testResult');

                        if (result.success) {
                            let html = `<span class="success">✅ ${result.message}</span><br>`;
                            if (result.details) {
                                html += '<strong>连接详情:</strong><br>';
                                Object.keys(result.details).forEach(key => {
                                    html += `${key}: ${result.details[key]}<br>`;
                                });
                            }
                            resultDiv.innerHTML = html;
                        } else {
                            resultDiv.innerHTML = `<span class="error">❌ ${result.message}</span>`;
                        }
                    } catch (error) {
                        document.getElementById('testResult').innerHTML =
                            `<span class="error">❌ 请求失败: ${error.message}</span>`;
                    }
                }

                // 记录日志
                function log(message, targetId = 'createResult') {
                    const resultDiv = document.getElementById(targetId);
                    const timestamp = new Date().toLocaleTimeString();
                    resultDiv.innerHTML = `[${timestamp}] ${message}<br>` + resultDiv.innerHTML;
                }

                // 页面加载完成后自动登录
                window.onload = function() {
                    autoLogin();
                    updateTestForm();
                    updateButtonStates();
                };
            </script>
        </body>
        </html>
        """

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))


def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://localhost:8000')


def run_server():
    """启动测试服务器"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, DatabaseDriverTestHandler)

    print("🚀 数据源验证测试平台启动成功!")
    print("=" * 60)
    print(f"📱 访问地址: http://localhost:8000")
    print(f"🔐 验证功能: 必填验证, 连接测试, 智能按钮")
    print(f"🗄️ 支持数据库: SQLite, PostgreSQL, MySQL, SQL Server, MongoDB, Redis")
    print("=" * 60)
    print("⚡ 正在自动打开浏览器...")
    print("按 Ctrl+C 停止服务器")
    print()

    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 数据源验证测试平台已停止")
        httpd.server_close()


if __name__ == '__main__':
    run_server()
