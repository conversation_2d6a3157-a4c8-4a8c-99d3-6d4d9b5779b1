#!/usr/bin/env python3
"""
数据库驱动检查脚本

检查所有数据库驱动的安装状态和可用性
"""

import sys
import importlib
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 70)
    print("🔍 数据库驱动检查报告")
    print("=" * 70)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print("=" * 70)

def check_driver(module_name, display_name, description, required=True):
    """检查单个驱动"""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'Unknown')
        status = "✅ 已安装"
        color = "\033[92m"  # 绿色
        
        # 尝试获取更详细的版本信息
        if hasattr(module, 'version'):
            version = module.version
        elif hasattr(module, 'VERSION'):
            version = module.VERSION
        elif hasattr(module, '__version__'):
            version = module.__version__
            
        print(f"{color}{status}\033[0m {display_name:<15} | {description:<30} | 版本: {version}")
        return True
        
    except ImportError as e:
        if required:
            status = "❌ 缺失"
            color = "\033[91m"  # 红色
        else:
            status = "⚠️ 可选"
            color = "\033[93m"  # 黄色
            
        print(f"{color}{status}\033[0m {display_name:<15} | {description:<30} | 错误: {str(e)}")
        return False
    except Exception as e:
        status = "⚠️ 异常"
        color = "\033[93m"  # 黄色
        print(f"{color}{status}\033[0m {display_name:<15} | {description:<30} | 异常: {str(e)}")
        return False

def check_all_drivers():
    """检查所有数据库驱动"""
    print("\n📋 数据库驱动检查:")
    print("-" * 70)
    
    # 定义所有数据库驱动
    drivers = [
        # 内置数据库
        ("sqlite3", "SQLite", "内置关系型数据库", True),
        
        # 关系型数据库
        ("psycopg2", "PostgreSQL", "开源关系型数据库", True),
        ("pymysql", "MySQL", "流行关系型数据库", True),
        ("pyodbc", "SQL Server", "微软企业级数据库", False),
        ("cx_Oracle", "Oracle", "企业级数据库", False),
        
        # NoSQL数据库
        ("pymongo", "MongoDB", "文档型数据库", True),
        ("redis", "Redis", "内存键值数据库", True),
        ("cassandra", "Cassandra", "分布式NoSQL数据库", False),
        ("neo4j", "Neo4j", "图数据库", False),
        
        # 分析型数据库
        ("clickhouse_driver", "ClickHouse", "列式分析数据库", False),
        ("duckdb", "DuckDB", "嵌入式分析数据库", False),
        
        # 搜索引擎
        ("elasticsearch", "Elasticsearch", "搜索和分析引擎", False),
        
        # 时序数据库
        ("influxdb_client", "InfluxDB", "时序数据库", False),
        
        # 异步驱动
        ("asyncpg", "AsyncPG", "PostgreSQL异步驱动", False),
        ("aiomysql", "AioMySQL", "MySQL异步驱动", False),
        ("aioredis", "AioRedis", "Redis异步驱动", False),
        ("motor", "Motor", "MongoDB异步驱动", False),
    ]
    
    # 检查驱动
    installed_count = 0
    required_count = 0
    total_count = len(drivers)
    
    for module_name, display_name, description, required in drivers:
        if required:
            required_count += 1
        
        if check_driver(module_name, display_name, description, required):
            installed_count += 1
    
    # 统计结果
    print("\n📊 检查统计:")
    print("-" * 70)
    print(f"总计驱动: {total_count}")
    print(f"已安装: {installed_count}")
    print(f"必需驱动: {required_count}")
    print(f"必需已安装: {min(installed_count, required_count)}")
    print(f"安装率: {installed_count/total_count*100:.1f}%")
    print(f"必需安装率: {min(installed_count, required_count)/required_count*100:.1f}%")
    
    return installed_count, required_count, total_count

def test_basic_connections():
    """测试基本连接功能"""
    print("\n🧪 基本连接测试:")
    print("-" * 70)
    
    # SQLite测试
    try:
        import sqlite3
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version()")
        version = cursor.fetchone()[0]
        conn.close()
        print(f"✅ SQLite连接测试成功 | 版本: {version}")
    except Exception as e:
        print(f"❌ SQLite连接测试失败 | 错误: {e}")
    
    # Redis测试（如果可用）
    try:
        import redis
        # 尝试连接到本地Redis（如果运行的话）
        r = redis.Redis(host='localhost', port=6379, socket_timeout=1)
        r.ping()
        info = r.info()
        print(f"✅ Redis连接测试成功 | 版本: {info.get('redis_version', 'Unknown')}")
        r.close()
    except ImportError:
        print("⚠️ Redis驱动未安装")
    except Exception as e:
        print(f"⚠️ Redis连接测试失败 | 错误: {e} (服务器可能未运行)")

def generate_install_commands():
    """生成安装命令"""
    print("\n📦 安装命令:")
    print("-" * 70)
    
    commands = {
        "核心数据库驱动": [
            "pip install psycopg2-binary",  # PostgreSQL
            "pip install pymysql",          # MySQL
            "pip install pymongo",          # MongoDB
            "pip install redis",            # Redis
        ],
        "可选数据库驱动": [
            "pip install pyodbc",           # SQL Server
            "pip install cx-Oracle",        # Oracle
            "pip install elasticsearch",    # Elasticsearch
            "pip install influxdb-client",  # InfluxDB
            "pip install cassandra-driver", # Cassandra
            "pip install neo4j",            # Neo4j
            "pip install clickhouse-driver", # ClickHouse
            "pip install duckdb",           # DuckDB
        ],
        "异步驱动": [
            "pip install asyncpg",          # PostgreSQL异步
            "pip install aiomysql",         # MySQL异步
            "pip install aioredis",         # Redis异步
            "pip install motor",            # MongoDB异步
        ]
    }
    
    for category, cmd_list in commands.items():
        print(f"\n{category}:")
        for cmd in cmd_list:
            print(f"  {cmd}")
    
    print(f"\n一键安装核心驱动:")
    print(f"  pip install psycopg2-binary pymysql pymongo redis")
    
    print(f"\n从requirements文件安装:")
    print(f"  pip install -r database_requirements.txt")

def check_system_requirements():
    """检查系统要求"""
    print("\n🖥️ 系统要求检查:")
    print("-" * 70)
    
    import platform
    import sys
    
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    print(f"Python版本: {sys.version}")
    
    # 检查Python版本
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print("✅ Python版本符合要求 (>= 3.8)")
    else:
        print("❌ Python版本过低，建议升级到3.8+")
    
    # 检查pip
    try:
        import pip
        print(f"✅ pip可用")
    except ImportError:
        print("❌ pip不可用")

def main():
    """主函数"""
    print_header()
    
    # 检查系统要求
    check_system_requirements()
    
    # 检查所有驱动
    installed, required, total = check_all_drivers()
    
    # 测试基本连接
    test_basic_connections()
    
    # 生成安装命令
    generate_install_commands()
    
    # 总结
    print("\n" + "=" * 70)
    print("📋 检查总结")
    print("=" * 70)
    
    if installed >= required:
        print("🎉 核心数据库驱动已安装完成！")
        print("✅ 可以启动真实连接测试服务器")
    else:
        print("⚠️ 部分核心驱动缺失，建议安装")
        print("💡 可以先使用SQLite进行测试")
    
    print(f"\n🚀 下一步:")
    print(f"  1. 安装缺失的驱动")
    print(f"  2. 启动服务器: python real_connection_server.py")
    print(f"  3. 测试连接功能")
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    main()
