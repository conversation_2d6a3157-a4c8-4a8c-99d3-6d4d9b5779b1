"""
认证服务

提供用户认证相关的业务逻辑。
"""

from datetime import datetime
from typing import Optional

from sqlalchemy.orm import Session

from app.core.security import get_password_hash, verify_password
from app.db.models.user import User
from app.schemas.auth import UserRegister
from app.services.user_service import UserService


class AuthService:
    """认证服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.user_service = UserService(db)
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱获取用户。
        
        Args:
            email: 邮箱地址
            
        Returns:
            Optional[User]: 用户对象或None
        """
        return self.user_service.get_user_by_email(email)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名获取用户。
        
        Args:
            username: 用户名
            
        Returns:
            Optional[User]: 用户对象或None
        """
        return self.user_service.get_user_by_username(username)
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        验证用户凭据。
        
        Args:
            username: 用户名或邮箱
            password: 密码
            
        Returns:
            Optional[User]: 验证成功的用户对象或None
        """
        # 尝试通过用户名查找
        user = self.get_user_by_username(username)
        
        # 如果用户名查找失败，尝试通过邮箱查找
        if not user:
            user = self.get_user_by_email(username)
        
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        return user
    
    def create_user(self, user_data: UserRegister) -> User:
        """
        创建新用户。
        
        Args:
            user_data: 用户注册数据
            
        Returns:
            User: 创建的用户对象
        """
        from app.schemas.user import UserCreate
        
        user_create_data = UserCreate(
            email=user_data.email,
            username=user_data.username,
            password=user_data.password,
            full_name=user_data.full_name,
            role=user_data.role
        )
        
        return self.user_service.create_user(user_create_data)
    
    def update_last_login(self, user_id: int) -> None:
        """
        更新用户最后登录时间。
        
        Args:
            user_id: 用户ID
        """
        user = self.user_service.get_user(user_id)
        if user:
            user.last_login_at = datetime.utcnow()
            self.db.commit()
    
    def change_password(self, user_id: int, current_password: str, new_password: str) -> bool:
        """
        修改用户密码。
        
        Args:
            user_id: 用户ID
            current_password: 当前密码
            new_password: 新密码
            
        Returns:
            bool: 是否修改成功
        """
        user = self.user_service.get_user(user_id)
        if not user:
            return False
        
        if not verify_password(current_password, user.hashed_password):
            return False
        
        user.hashed_password = get_password_hash(new_password)
        self.db.commit()
        return True
