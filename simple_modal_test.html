<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化模态框测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f8fafc; padding: 20px; color: #333;
        }
        .container { max-width: 800px; margin: 0 auto; }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 30px; border-radius: 16px; margin-bottom: 30px;
            text-align: center;
        }
        .btn { 
            background: #667eea; color: white; border: none; padding: 12px 24px;
            border-radius: 10px; cursor: pointer; margin: 8px; font-size: 14px;
            font-weight: 600; transition: all 0.3s ease;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-1px); }
        .btn:disabled { background: #cbd5e1; cursor: not-allowed; transform: none; }
        .btn-primary { background: #3b82f6; }
        .btn-success { background: #10b981; }
        .btn-secondary { background: #6b7280; }
        
        /* 模态框样式 */
        .modal { 
            display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 1000; backdrop-filter: blur(4px);
        }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { 
            background: white; border-radius: 20px; padding: 40px; max-width: 600px; width: 90%;
            max-height: 90vh; overflow-y: auto; box-shadow: 0 25px 50px rgba(0,0,0,0.25);
        }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
        .modal-title { font-size: 24px; font-weight: 700; color: #1f2937; }
        .modal-close { 
            background: none; border: none; font-size: 24px; cursor: pointer;
            color: #6b7280; padding: 8px; border-radius: 8px;
        }
        .modal-close:hover { background: #f3f4f6; color: #374151; }
        
        /* 表单样式 */
        .form-group { margin: 20px 0; }
        .form-group label { 
            display: block; margin-bottom: 8px; font-weight: 600; color: #374151;
            font-size: 14px;
        }
        .form-group input, .form-group select { 
            width: 100%; padding: 14px 16px; border: 2px solid #e5e7eb; border-radius: 10px;
            font-size: 14px; transition: all 0.3s ease; background: #fafafa;
        }
        .form-group input:focus, .form-group select:focus { 
            border-color: #667eea; outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        .required { color: #ef4444; }
        
        /* 日志样式 */
        .log { 
            background: #1f2937; color: #e5e7eb; border-radius: 12px; 
            padding: 20px; font-family: 'Courier New', monospace; font-size: 13px;
            max-height: 200px; overflow-y: auto; margin: 20px 0;
        }
        .log-entry { margin: 4px 0; padding: 4px 0; }
        .log-success { color: #34d399; }
        .log-error { color: #f87171; }
        .log-warning { color: #fbbf24; }
        .log-info { color: #60a5fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 简化模态框测试</h1>
            <p>测试模态框打开和关闭功能</p>
        </div>

        <div style="background: white; padding: 30px; border-radius: 16px; margin-bottom: 20px;">
            <h3>测试控制</h3>
            <button class="btn btn-primary" onclick="openModal()">
                ➕ 打开模态框
            </button>
            <button class="btn btn-secondary" onclick="clearLog()">
                🔄 清空日志
            </button>
        </div>

        <div style="background: white; padding: 30px; border-radius: 16px;">
            <h3>操作日志</h3>
            <div id="operationLog" class="log"></div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="testModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">创建数据源</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            
            <form id="testForm" onsubmit="return false;">
                <div class="form-group">
                    <label>数据源名称 <span class="required">*</span></label>
                    <input type="text" id="dsName" placeholder="例如: 测试SQLite数据库" required>
                </div>
                
                <div class="form-group">
                    <label>描述</label>
                    <input type="text" id="dsDescription" placeholder="数据源用途描述">
                </div>
                
                <div class="form-group">
                    <label>数据库类型 <span class="required">*</span></label>
                    <select id="dsType" onchange="handleTypeChange()" required>
                        <option value="">请选择数据库类型</option>
                        <option value="sqlite">SQLite</option>
                        <option value="postgresql">PostgreSQL</option>
                        <option value="mysql">MySQL</option>
                    </select>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 30px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">
                        取消
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveDataSource()" id="saveBtn">
                        💾 保存数据源
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局错误捕获
        window.onerror = function(message, source, lineno, colno, error) {
            log(`❌ JavaScript错误: ${message} (行 ${lineno})`, 'error');
            return false;
        };

        window.addEventListener('unhandledrejection', function(event) {
            log(`❌ Promise错误: ${event.reason}`, 'error');
        });

        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('operationLog');
            const timestamp = new Date().toLocaleTimeString();
            const icons = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${icons[type]} ${message}`;
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
            
            // 限制日志条数
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.lastChild);
            }
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 清空日志
        function clearLog() {
            document.getElementById('operationLog').innerHTML = '';
            log('🔄 日志已清空', 'info');
        }

        // 打开模态框
        function openModal() {
            try {
                log('📝 尝试打开模态框...', 'info');
                
                const modal = document.getElementById('testModal');
                if (!modal) {
                    log('❌ 找不到模态框元素', 'error');
                    return;
                }
                
                // 重置表单
                document.getElementById('testForm').reset();
                
                // 显示模态框
                modal.classList.add('show');
                log('✅ 模态框已打开', 'success');
                
                // 检查模态框是否立即消失
                setTimeout(() => {
                    if (modal.classList.contains('show')) {
                        log('✅ 模态框保持打开状态', 'success');
                    } else {
                        log('❌ 模态框意外关闭了！', 'error');
                    }
                }, 100);
                
            } catch (error) {
                log(`❌ 打开模态框时发生错误: ${error.message}`, 'error');
            }
        }

        // 关闭模态框
        function closeModal() {
            try {
                log('🔒 关闭模态框...', 'info');
                
                const modal = document.getElementById('testModal');
                if (!modal) {
                    log('❌ 找不到模态框元素', 'error');
                    return;
                }
                
                modal.classList.remove('show');
                log('✅ 模态框已关闭', 'success');
                
            } catch (error) {
                log(`❌ 关闭模态框时发生错误: ${error.message}`, 'error');
            }
        }

        // 处理类型改变
        function handleTypeChange() {
            try {
                const type = document.getElementById('dsType').value;
                log(`🔧 数据库类型改变为: ${type || '(空)'}`, 'info');
            } catch (error) {
                log(`❌ 处理类型改变时发生错误: ${error.message}`, 'error');
            }
        }

        // 保存数据源
        function saveDataSource() {
            try {
                const name = document.getElementById('dsName').value.trim();
                const description = document.getElementById('dsDescription').value.trim();
                const type = document.getElementById('dsType').value;
                
                log('💾 尝试保存数据源...', 'info');
                
                if (!name || !type) {
                    log('❌ 请填写完整的基本信息', 'error');
                    return;
                }
                
                log(`✅ 数据源保存成功: ${name} (${type})`, 'success');
                
                // 模拟保存延迟
                setTimeout(() => {
                    closeModal();
                    log('🎉 数据源创建完成', 'success');
                }, 500);
                
            } catch (error) {
                log(`❌ 保存数据源时发生错误: ${error.message}`, 'error');
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('testModal');
            if (event.target === modal) {
                log('🖱️ 点击模态框外部，关闭模态框', 'info');
                closeModal();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('testModal');
                if (modal && modal.classList.contains('show')) {
                    log('⌨️ 按下ESC键，关闭模态框', 'info');
                    closeModal();
                }
            }
        });

        // 表单提交处理
        document.getElementById('testForm').addEventListener('submit', function(e) {
            log('📝 表单提交事件触发', 'info');
            e.preventDefault();
            log('🛑 表单默认提交已阻止', 'info');
            saveDataSource();
        });

        // 页面加载完成
        window.onload = function() {
            log('🚀 页面加载完成，模态框测试就绪', 'success');
        };
    </script>
</body>
</html>
