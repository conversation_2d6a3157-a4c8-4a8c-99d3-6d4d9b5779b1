# 故障排除指南

## 🔍 问题诊断步骤

### 第一步：环境检查
```cmd
check-env.bat
```

这会检查：
- Python是否安装
- Node.js是否安装
- 项目文件是否完整

### 第二步：最简单测试
```cmd
start-simple.bat
```

这会启动一个最基础的服务器，只需要Python标准库。

## 🚨 常见问题及解决方案

### 问题1: "Python不是内部或外部命令"

**原因**: Python未安装或未添加到PATH

**解决方案**:
1. 下载Python: https://www.python.org/downloads/
2. 安装时勾选"Add Python to PATH"
3. 重启命令提示符
4. 运行 `python --version` 验证

### 问题2: "Node不是内部或外部命令"

**原因**: Node.js未安装

**解决方案**:
1. 下载Node.js: https://nodejs.org/
2. 安装LTS版本
3. 重启命令提示符
4. 运行 `node --version` 验证

### 问题3: 脚本运行后立即关闭

**原因**: 可能是权限问题或路径问题

**解决方案**:
1. 右键脚本，选择"以管理员身份运行"
2. 或者手动在命令提示符中运行：
   ```cmd
   cd C:\Users\<USER>\test\test
   start-simple.bat
   ```

### 问题4: 端口被占用

**症状**: 提示"Address already in use"或端口8000被占用

**解决方案**:
1. 关闭占用端口的程序
2. 或修改 `simple_backend.py` 中的端口号：
   ```python
   server_address = ('', 8001)  # 改为8001或其他端口
   ```

### 问题5: 防火墙阻止

**症状**: 启动成功但无法访问网页

**解决方案**:
1. 允许Python通过防火墙
2. 或临时关闭防火墙测试

### 问题6: 编码问题

**症状**: 中文显示乱码

**解决方案**:
1. 在命令提示符中运行：
   ```cmd
   chcp 65001
   ```
2. 或使用PowerShell代替命令提示符

## 🛠️ 手动测试步骤

如果所有脚本都无法运行，请手动执行：

### 测试Python
```cmd
python --version
python -c "print('Hello World')"
```

### 测试简单服务器
```cmd
python -c "
from http.server import HTTPServer, BaseHTTPRequestHandler
class Handler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<h1>Test Server Works!</h1>')
server = HTTPServer(('', 8000), Handler)
print('Server running at http://localhost:8000')
server.serve_forever()
"
```

### 测试网络访问
在浏览器中访问：http://localhost:8000

## 📞 获取更多帮助

### 收集错误信息
1. 运行 `check-env.bat` 并截图结果
2. 运行 `start-simple.bat` 并复制错误信息
3. 检查Windows版本：`winver`
4. 检查Python版本：`python --version`

### 替代方案

#### 方案1: 在线开发环境
- 使用 Replit: https://replit.com/
- 使用 CodeSandbox: https://codesandbox.io/
- 使用 Gitpod: https://gitpod.io/

#### 方案2: 使用Python内置服务器
```cmd
cd frontend
python -m http.server 3000
```

#### 方案3: 使用VSCode Live Server
1. 安装VSCode
2. 安装Live Server扩展
3. 右键HTML文件选择"Open with Live Server"

## 🎯 最小可行测试

如果一切都失败了，创建这个最简单的测试：

创建文件 `test.py`:
```python
print("Python works!")
import json
print("JSON module works!")
import http.server
print("HTTP server module works!")
print("All basic modules are available!")
```

运行：
```cmd
python test.py
```

如果这个都不能运行，说明Python安装有问题，需要重新安装Python。

## 🔄 重新开始

如果问题太多，建议：

1. **重新安装Python**:
   - 卸载现有Python
   - 下载最新版本
   - 安装时勾选所有选项

2. **重新安装Node.js**:
   - 卸载现有Node.js
   - 下载LTS版本
   - 使用默认设置安装

3. **重新下载项目**:
   - 删除当前项目文件夹
   - 重新下载或克隆项目

4. **使用不同的方法**:
   - 尝试使用PowerShell而不是CMD
   - 尝试使用Git Bash
   - 尝试在不同的目录运行
