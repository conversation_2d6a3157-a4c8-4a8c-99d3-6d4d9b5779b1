# 自然语言BI数据分析平台项目规范

## 开发规范

### 代码规范
- 使用 Black 和 isort 格式化 Python 代码
- 使用 ESLint 和 Prettier 格式化前端代码
- 遵循 PEP 8 规范
- 使用类型注解

### 文档规范
- 所有公共API必须有文档字符串
- 使用 Sphinx 生成API文档
- 关键算法需要有设计文档
- README 包含项目概述和快速开始指南

### 测试规范
- 单元测试覆盖率 > 80%
- 集成测试覆盖关键流程
- 使用 pytest 进行测试
- 前端使用 Jest 和 React Testing Library

### 版本控制
- 使用 Git Flow 工作流
- 语义化版本号
- 提交信息遵循 Conventional Commits 规范

## 技术栈

### 后端
- **编程语言**: Python 3.10+
- **Web框架**: FastAPI 0.95+
- **数据处理**: Pandas 2.0+, NumPy
- **NLP处理**: Hugging Face Transformers 4.30+, LangChain 0.0.200+
- **数据库**: 
  - PostgreSQL 14+ (主数据库)
  - Redis 7.0+ (缓存)
- **ORM**: SQLAlchemy 2.0+
- **任务队列**: Celery

### 前端
- **框架**: React 18+ + TypeScript 4.5+
- **UI组件库**: Ant Design 5.0+
- **数据可视化**: ECharts 5.0+, D3.js
- **3D渲染**: Three.js/WebGL
- **状态管理**: Redux Toolkit
- **实时通信**: WebSocket

### 部署
- Docker & Kubernetes
- CI/CD 流程
- 云原生架构

## 项目目录结构

```
bi_platform/
├── backend/
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── db/             # 数据库模型和连接
│   │   ├── nlp/            # 自然语言处理模块
│   │   │   ├── intent/     # 意图识别
│   │   │   ├── sql_gen/    # SQL生成
│   │   │   └── semantic/   # 语义理解
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── tests/              # 测试
│   └── alembic/            # 数据库迁移
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/     # UI组件
│   │   ├── pages/          # 页面
│   │   ├── services/       # API调用
│   │   ├── store/          # 状态管理
│   │   └── utils/          # 工具函数
│   └── tests/              # 前端测试
└── docs/                   # 项目文档
```

## 开发流程

1. 需求分析与规划
2. 设计与原型
3. 开发与测试
4. 代码审查
5. 集成测试
6. 部署与监控

## 命名规范

### Python
- 类名: PascalCase
- 函数名和变量名: snake_case
- 常量: UPPER_CASE
- 私有方法/变量: _leading_underscore

### JavaScript/TypeScript
- 类名: PascalCase
- 函数名和变量名: camelCase
- 常量: UPPER_CASE
- 组件文件名: PascalCase
- 非组件文件名: camelCase

### 数据库
- 表名: snake_case, 复数形式
- 字段名: snake_case
- 主键: id
- 外键: entity_id

## 代码注释规范

### Python
```python
def function_name(param1: type, param2: type) -> return_type:
    """
    简短的函数描述。

    详细的函数描述，可以多行。

    Args:
        param1: 参数1的描述
        param2: 参数2的描述

    Returns:
        返回值的描述

    Raises:
        ExceptionType: 异常情况描述
    """
    pass
```

### JavaScript/TypeScript
```javascript
/**
 * 简短的函数描述。
 * 
 * 详细的函数描述，可以多行。
 * 
 * @param {type} param1 - 参数1的描述
 * @param {type} param2 - 参数2的描述
 * @returns {type} 返回值的描述
 * @throws {ExceptionType} 异常情况描述
 */
function functionName(param1, param2) {
    // 实现
}
```

## 提交信息规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

- type: feat, fix, docs, style, refactor, test, chore
- scope: 影响范围
- subject: 简短描述
- body: 详细描述
- footer: 破坏性变更和issue引用

示例:
```
feat(auth): implement JWT authentication

- Add JWT token generation
- Add token validation middleware
- Update user service to handle token refresh

Closes #123
```

## 分支管理

- main: 生产环境分支
- develop: 开发环境分支
- feature/*: 功能分支
- bugfix/*: 修复分支
- release/*: 发布分支

## 代码审查清单

- 代码是否符合规范
- 是否有适当的测试
- 是否有文档
- 性能考虑
- 安全考虑
- 错误处理
